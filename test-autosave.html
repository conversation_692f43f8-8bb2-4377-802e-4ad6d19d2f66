<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الحفظ التلقائي - Glass ERP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #1e7e34; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-warning:hover { background-color: #e0a800; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الحفظ التلقائي - Glass ERP</h1>
        
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="system-status" class="status info">
                جاري فحص النظام...
            </div>
            <button onclick="checkSystemStatus()">🔄 فحص حالة النظام</button>
        </div>

        <div class="test-section">
            <h3>💾 اختبار الحفظ التلقائي</h3>
            <div id="autosave-status" class="status info">
                جاري فحص الحفظ التلقائي...
            </div>
            <button onclick="testAutoSave()" class="btn-success">▶️ بدء اختبار الحفظ التلقائي</button>
            <button onclick="stopAutoSaveTest()" class="btn-danger">⏹️ إيقاف الاختبار</button>
        </div>

        <div class="test-section">
            <h3>📝 اختبار تعديل البيانات</h3>
            <p>قم بتعديل البيانات أدناه لاختبار تتبع التغييرات:</p>
            <input type="text" id="test-input" placeholder="اكتب شيئاً هنا..." onchange="testDataChange()">
            <textarea id="test-textarea" placeholder="أو اكتب في هذا المربع..." onchange="testDataChange()"></textarea>
            <div id="data-change-status" class="status info">
                لم يتم تعديل أي بيانات بعد
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات التحكم</h3>
            <button onclick="forceSave()" class="btn-warning">💾 حفظ فوري</button>
            <button onclick="toggleAutoSave()" class="btn-warning">🔄 تفعيل/إلغاء الحفظ التلقائي</button>
            <button onclick="clearTestData()" class="btn-danger">🗑️ مسح بيانات الاختبار</button>
        </div>

        <div class="test-section">
            <h3>📋 سجل الأحداث</h3>
            <div id="event-log" class="log">
                جاري تهيئة سجل الأحداث...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testInterval = null;
        let testCounter = 0;
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تهيئة صفحة الاختبار');
            checkSystemStatus();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // مراقبة التغييرات في الحقول
            document.getElementById('test-input').addEventListener('input', function() {
                log('📝 تم تعديل حقل النص');
                if (typeof markDataAsChanged === 'function') {
                    markDataAsChanged();
                }
            });
            
            document.getElementById('test-textarea').addEventListener('input', function() {
                log('📝 تم تعديل منطقة النص');
                if (typeof markDataAsChanged === 'function') {
                    markDataAsChanged();
                }
            });
        }

        // فحص حالة النظام
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let status = '';
            let className = 'info';
            
            // فحص وجود الوظائف المطلوبة
            const requiredFunctions = [
                'saveSystemData',
                'markDataAsChanged', 
                'getAutoSaveStatus',
                'startAutoSave',
                'stopAutoSave'
            ];
            
            const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');
            
            if (missingFunctions.length === 0) {
                status = '✅ جميع وظائف النظام متاحة';
                className = 'success';
                log('✅ فحص النظام: جميع الوظائف متاحة');
            } else {
                status = `❌ وظائف مفقودة: ${missingFunctions.join(', ')}`;
                className = 'error';
                log(`❌ فحص النظام: وظائف مفقودة - ${missingFunctions.join(', ')}`);
            }
            
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
            
            // فحص حالة الحفظ التلقائي
            checkAutoSaveStatus();
        }

        // فحص حالة الحفظ التلقائي
        function checkAutoSaveStatus() {
            const statusDiv = document.getElementById('autosave-status');
            
            if (typeof getAutoSaveStatus === 'function') {
                const status = getAutoSaveStatus();
                const statusText = `
                    الحالة: ${status.enabled ? 'مفعل' : 'معطل'} | 
                    يعمل: ${status.running ? 'نعم' : 'لا'} | 
                    آخر حفظ: ${status.lastSaveTime ? new Date(status.lastSaveTime).toLocaleTimeString('ar-SA') : 'لا يوجد'}
                `;
                statusDiv.textContent = statusText;
                statusDiv.className = `status ${status.enabled ? 'success' : 'error'}`;
                log(`📊 حالة الحفظ التلقائي: ${JSON.stringify(status)}`);
            } else {
                statusDiv.textContent = '❌ وظيفة فحص الحفظ التلقائي غير متاحة';
                statusDiv.className = 'status error';
                log('❌ وظيفة getAutoSaveStatus غير متاحة');
            }
        }

        // اختبار الحفظ التلقائي
        function testAutoSave() {
            if (testInterval) {
                log('⚠️ الاختبار يعمل بالفعل');
                return;
            }
            
            log('🧪 بدء اختبار الحفظ التلقائي');
            testCounter = 0;
            
            testInterval = setInterval(() => {
                testCounter++;
                
                // تعديل البيانات لمحاكاة التغييرات
                document.getElementById('test-input').value = `اختبار ${testCounter} - ${new Date().toLocaleTimeString('ar-SA')}`;
                
                // تحديد أن البيانات تغيرت
                if (typeof markDataAsChanged === 'function') {
                    markDataAsChanged();
                    log(`📝 تم تحديد تغيير البيانات - اختبار ${testCounter}`);
                }
                
                // فحص حالة النظام كل 10 ثوان
                if (testCounter % 10 === 0) {
                    checkAutoSaveStatus();
                }
                
            }, 1000); // كل ثانية
            
            log('▶️ تم بدء اختبار الحفظ التلقائي');
        }

        // إيقاف اختبار الحفظ التلقائي
        function stopAutoSaveTest() {
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
                log('⏹️ تم إيقاف اختبار الحفظ التلقائي');
            }
        }

        // اختبار تغيير البيانات
        function testDataChange() {
            const statusDiv = document.getElementById('data-change-status');
            statusDiv.textContent = `✅ تم تعديل البيانات في ${new Date().toLocaleTimeString('ar-SA')}`;
            statusDiv.className = 'status success';
            log('📝 تم اختبار تغيير البيانات');
        }

        // حفظ فوري
        function forceSave() {
            if (typeof saveSystemData === 'function') {
                log('💾 تشغيل حفظ فوري...');
                const result = saveSystemData();
                log(`💾 نتيجة الحفظ الفوري: ${result ? 'نجح' : 'فشل'}`);
                checkAutoSaveStatus();
            } else {
                log('❌ وظيفة الحفظ غير متاحة');
            }
        }

        // تفعيل/إلغاء الحفظ التلقائي
        function toggleAutoSave() {
            if (typeof toggleAutoSave === 'function') {
                toggleAutoSave();
                log('🔄 تم تغيير حالة الحفظ التلقائي');
                setTimeout(checkAutoSaveStatus, 100);
            } else {
                log('❌ وظيفة تغيير حالة الحفظ التلقائي غير متاحة');
            }
        }

        // مسح بيانات الاختبار
        function clearTestData() {
            document.getElementById('test-input').value = '';
            document.getElementById('test-textarea').value = '';
            document.getElementById('data-change-status').textContent = 'تم مسح بيانات الاختبار';
            document.getElementById('data-change-status').className = 'status info';
            log('🧹 تم مسح بيانات الاختبار');
        }

        // إضافة رسالة إلى السجل
        function log(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry.trim());
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('event-log').textContent = '';
            log('🧹 تم مسح سجل الأحداث');
        }

        // حفظ عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            log('🚪 إغلاق صفحة الاختبار');
            stopAutoSaveTest();
        });
    </script>
</body>
</html>
