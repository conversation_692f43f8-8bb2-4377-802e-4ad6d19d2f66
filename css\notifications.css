/* ===== نظام الإشعارات ===== */

/* زر الإشعارات */
.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-1px);
}

.notification-btn:active {
    transform: translateY(0);
}

/* شارة الإشعارات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* قائمة الإشعارات المنسدلة */
.notifications-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.notifications-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* رأس قائمة الإشعارات */
.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
}

.notifications-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mark-all-read {
    background: none;
    border: none;
    color: #667eea;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.mark-all-read:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* قائمة الإشعارات */
.notifications-list {
    max-height: 350px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* عنصر الإشعار */
.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background: #f8fafc;
}

.notification-item.unread {
    background: #f0f9ff;
    border-left: 3px solid #667eea;
}

.notification-item.unread:hover {
    background: #e0f2fe;
}

.notification-item.read {
    opacity: 0.8;
}

/* أيقونة الإشعار */
.notification-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1rem;
}

.notification-item.unread .notification-icon {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.notification-item.read .notification-icon {
    background: #f1f5f9;
    color: #64748b;
}

/* محتوى الإشعار */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.notification-message {
    color: #64748b;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    color: #94a3b8;
    font-size: 0.75rem;
    font-weight: 500;
}

/* إجراءات الإشعار */
.notification-actions {
    flex-shrink: 0;
    margin-right: 0.5rem;
}

.delete-notification {
    background: none;
    border: none;
    color: #94a3b8;
    font-size: 0.8rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0;
}

.notification-item:hover .delete-notification {
    opacity: 1;
}

.delete-notification:hover {
    background: #fee2e2;
    color: #ef4444;
}

/* حالة عدم وجود إشعارات */
.no-notifications {
    text-align: center;
    padding: 2rem 1.5rem;
    color: #94a3b8;
}

.no-notifications i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-notifications p {
    margin: 0;
    font-size: 0.9rem;
}

/* تذييل قائمة الإشعارات */
.notifications-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #f1f5f9;
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
}

.view-all-notifications {
    width: 100%;
    background: none;
    border: 1px solid #e2e8f0;
    color: #667eea;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-all-notifications:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* ألوان أنواع الإشعارات */
.notification-item[data-type="success"] .notification-icon {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.notification-item[data-type="error"] .notification-icon {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.notification-item[data-type="warning"] .notification-icon {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.notification-item[data-type="info"] .notification-icon {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .notifications-dropdown {
        width: 320px;
        left: -50px;
    }
    
    .notification-item {
        padding: 0.75rem 1rem;
    }
    
    .notifications-header,
    .notifications-footer {
        padding: 0.75rem 1rem;
    }
}

/* الرسوم المتحركة */
@keyframes slideInNotification {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-item {
    animation: slideInNotification 0.3s ease;
}

/* تحسين الأداء */
.notifications-dropdown {
    will-change: transform, opacity;
    backface-visibility: hidden;
}

/* تأثيرات إضافية */
.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
    pointer-events: none;
} 