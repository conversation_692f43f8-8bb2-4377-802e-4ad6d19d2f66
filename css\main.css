/* ========================================
   Glass ERP System - Main CSS
   التنسيقات الرئيسية للنظام - محسنة للأداء
   ======================================== */

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
  /* تحسين الأداء */
  will-change: auto;
  transform: translateZ(0);
}

/* ========================================
   Sidebar Styles - القائمة الجانبية
   ======================================== */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #fff;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  padding: 0;
  box-shadow: -5px 0 15px rgba(0,0,0,0.3);
  z-index: 1000;
  transition: transform 0.3s ease;
  transform: translateZ(0);
  overflow-y: auto;
  overflow-x: visible;
}

.sidebar-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* إزالة الـ animation المعقدة لتحسين الأداء */
.sidebar-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  /* إزالة الـ animation */
  opacity: 0.5;
}

.sidebar-header h2 {
  font-size: 1.8em;
  font-weight: 800;
  margin: 0;
  position: relative;
  z-index: 1;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.sidebar-header .subtitle {
  font-size: 0.9em;
  opacity: 0.9;
  margin-top: 5px;
  position: relative;
  z-index: 1;
}

.nav-menu {
  padding: 20px 0;
  height: calc(100vh - 120px);
  overflow-y: auto;
  /* تحسين الأداء */
  will-change: auto;
}

.nav-menu::-webkit-scrollbar {
  width: 5px;
}

.nav-menu::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
}

.nav-menu::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
  border-radius: 5px;
}

.nav-item {
  padding: 0;
  margin: 5px 15px;
  border-radius: 12px;
  transition: background-color 0.3s ease, transform 0.2s ease;
  position: relative;
  overflow: hidden;
}

/* إزالة الـ animation المعقدة */
.nav-item::before {
  display: none;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  border-radius: 12px;
  transition: background-color 0.3s ease, transform 0.2s ease;
  position: relative;
  z-index: 1;
}

.nav-link i {
  width: 25px;
  margin-left: 15px;
  font-size: 1.1em;
  transition: transform 0.2s ease;
}

.nav-link span {
  font-weight: 500;
  font-size: 0.95em;
}

.nav-item:hover .nav-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateX(-3px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.nav-item.active .nav-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.nav-item:hover i {
  transform: scale(1.1);
}

/* ========================================
   Main Content - المحتوى الرئيسي
   ======================================== */
.main {
  margin-right: 280px;
  padding: 0;
  min-height: 100vh;
  background: #f8fafc;
  transition: margin-right 0.3s ease;
  will-change: margin-right;
  overflow-y: auto;
  height: 100vh;
}

.top-header {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  padding: 20px 30px;
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  /* تحسين الأداء */
  will-change: auto;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.8em;
  font-weight: 700;
  color: #1a1a2e;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.notification-btn {
  background: none;
  border: none;
  color: #1a1a2e;
  font-size: 1.2em;
  cursor: pointer;
  position: relative;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.notification-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7em;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.user-profile:hover {
  background: rgba(102, 126, 234, 0.1);
}

.user-avatar {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1em;
}

.content {
  padding: 30px;
}

.module {
  display: none;
}

.module.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ========================================
   Buttons & Forms - الأزرار والنماذج
   ======================================== */
.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1a1a2e;
}

.form-group input, .form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1em;
  transition: border-color 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.form-group input:focus, .form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ========================================
   Responsive Design - التصميم المتجاوب
   ======================================== */
@media (max-width: 1024px) {
  .sidebar {
    /* أزل أي حركة أو إخفاء */
    transform: none !important;
    right: 0 !important;
    position: fixed !important;
    display: block !important;
  }
  .main {
    margin-right: 280px !important;
  }
}

@media (max-width: 768px) {
  .sidebar {
    /* أزل أي حركة أو إخفاء */
    transform: none !important;
    right: 0 !important;
    position: fixed !important;
    display: block !important;
  }
  .main {
    margin-right: 280px !important;
  }
}

/* ========================================
   Sidebar Toggle - زر إخفاء/إظهار القائمة
   ======================================== */
/* احذف كل ما يخص .sidebar-toggle */

/* ========================================
   Company Info - معلومات الشركة
   ======================================== */
.company-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-time {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  background: rgba(0, 123, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.company-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.company-name {
  font-weight: 600;
  color: #1a1a2e;
  font-size: 1.1em;
}

.current-time {
  font-size: 0.9em;
  color: #64748b;
  font-family: 'Courier New', monospace;
}

/* ========================================
   Language Toggle - تبديل اللغة
   ======================================== */
.language-toggle {
  background: none;
  border: 1px solid #e2e8f0;
  color: #475569;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
  font-size: 0.9em;
}

.language-toggle:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.language-toggle:active {
  transform: scale(0.98);
}

.lang-text {
  font-weight: 500;
}

/* ========================================
   Collapsed Sidebar - القائمة الجانبية المطوية
   ======================================== */
.sidebar.collapsed {
  /* أزل أي حركة */
  transform: none !important;
  right: 0 !important;
  position: fixed !important;
  display: block !important;
}

.main.expanded {
  margin-right: 280px !important;
}

.header-content {
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.page-title {
  flex: 1;
  min-width: 200px;
}

/* ========================================
   Additional Responsive - تحسينات إضافية للتصميم المتجاوب
   ======================================== */
@media (max-width: 1200px) {
  .company-info {
    display: none;
  }
  
  .company-name {
    display: none;
  }
  
  .current-time {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .company-info {
    display: none;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .sidebar-toggle {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .company-details {
    display: none;
  }
  
  .language-toggle {
    display: none;
  }
  
  .lang-text {
    display: none;
  }
}

/* نافذة القيد اليومية (modal) */
.modal {
  width: 75vw !important;
  max-width: 1100px;
  min-width: 320px;
  min-height: 300px;
  max-height: 90vh;
  height: auto;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  border-radius: 18px !important;
  box-shadow: 0 8px 40px rgba(0,0,0,0.18);
  padding: 32px 28px !important;
  overflow-y: auto;
  background: #fff;
  transition: width 0.3s, height 0.3s;
}

@media (max-width: 900px) {
  .modal {
    width: 95vw !important;
    padding: 12px 4px !important;
  }
  .sidebar-toggle {
    right: 20px;
    top: 20px;
  }
} 