/**
 * Settings Module - موديول الإعدادات
 * ========================================
 */

// متغيرات الإعدادات
let companySettings = {
    name: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    taxNumber: '',
    currency: 'LYD',
    logo: ''
};

/**
 * تهيئة موديول الإعدادات
 */
function initializeSettings() {
    console.log('⚙️ تهيئة موديول الإعدادات...');
    
    // مسح أي بيانات تجريبية قديمة
    clearOldData();
    
    // إنشاء محتوى الإعدادات
    createSettingsContent();
    
    // تحميل الإعدادات المحفوظة
    loadSettings();
    
    // ربط الأحداث
    bindSettingsEvents();
    
    console.log('✅ تم تهيئة موديول الإعدادات بنجاح');
}

/**
 * مسح البيانات القديمة
 */
function clearOldData() {
    try {
        // مسح البيانات التجريبية القديمة إذا كانت موجودة
        const oldKeys = ['glassERP_companySettings', 'glassERP_companyInfo'];
        oldKeys.forEach(key => {
            if (localStorage.getItem(key)) {
                localStorage.removeItem(key);
                console.log(`🗑️ تم مسح البيانات القديمة: ${key}`);
            }
        });
    } catch (error) {
        console.error('❌ خطأ في مسح البيانات القديمة:', error);
    }
}

/**
 * إنشاء محتوى الإعدادات
 */
function createSettingsContent() {
    const settingsContainer = document.getElementById('settings');
    
    settingsContainer.innerHTML = `
        <div class="settings-container">
            <h2><i class="fas fa-cog"></i> إعدادات الشركة</h2>
            
            <!-- معلومات الشركة الأساسية -->
            <div class="settings-section">
                <h3><i class="fas fa-building"></i> معلومات الشركة</h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="company-name-input">اسم الشركة *</label>
                        <input type="text" id="company-name-input" placeholder="أدخل اسم الشركة" required minlength="2" maxlength="100">
                        <small class="form-text">اسم الشركة مطلوب ولا يمكن تركه فارغاً</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="company-address">عنوان الشركة</label>
                        <input type="text" id="company-address" placeholder="أدخل عنوان الشركة">
                    </div>
                    
                    <div class="form-group">
                        <label for="company-phone">رقم الهاتف</label>
                        <input type="tel" id="company-phone" placeholder="أدخل رقم الهاتف">
                    </div>
                    
                    <div class="form-group">
                        <label for="company-email">البريد الإلكتروني</label>
                        <input type="email" id="company-email" placeholder="أدخل البريد الإلكتروني">
                    </div>
                    
                    <div class="form-group">
                        <label for="company-website">الموقع الإلكتروني</label>
                        <input type="url" id="company-website" placeholder="أدخل الموقع الإلكتروني">
                    </div>
                    
                    <div class="form-group">
                        <label for="company-tax">الرقم الضريبي</label>
                        <input type="text" id="company-tax" placeholder="أدخل الرقم الضريبي">
                    </div>
                </div>
            </div>
            
            <!-- إعدادات النظام -->
            <div class="settings-section">
                <h3><i class="fas fa-globe"></i> إعدادات النظام</h3>
                
                <div class="form-group">
                    <label for="currency">العملة الافتراضية</label>
                    <select id="currency">
                        <option value="LYD">دينار ليبي (LYD)</option>
                        <option value="SAR">ريال سعودي (SAR)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                        <option value="GBP">جنيه إسترليني (GBP)</option>
                        <option value="AED">درهم إماراتي (AED)</option>
                        <option value="EGP">جنيه مصري (EGP)</option>
                        <option value="JOD">دينار أردني (JOD)</option>
                        <option value="KWD">دينار كويتي (KWD)</option>
                        <option value="QAR">ريال قطري (QAR)</option>
                        <option value="BHD">دينار بحريني (BHD)</option>
                    </select>
                </div>
                

            </div>
            
            <!-- شعار الشركة -->
            <div class="settings-section">
                <h3><i class="fas fa-image"></i> شعار الشركة</h3>
                
                <div class="logo-upload">
                    <div class="logo-preview" id="logo-preview" onclick="document.getElementById('logo-input').click()">
                        <span>انقر لرفع الشعار</span>
                    </div>
                    <input type="file" id="logo-input" accept="image/*" style="display: none;">
                    <div class="logo-info">
                        <p><i class="fas fa-info-circle"></i> الأبعاد الموصى بها: 200×200 بكسل</p>
                        <p><i class="fas fa-exclamation-triangle"></i> <strong>مهم:</strong> يفضل استخدام صورة PNG شفافة الخلفية للحصول على أفضل نتيجة</p>
                    </div>
                </div>
            </div>
            
            <!-- إجراءات الإعدادات -->
            <div class="settings-actions">
                <button type="button" class="btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button type="button" class="btn-secondary" onclick="resetSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;
}

/**
 * ربط أحداث الإعدادات
 */
function bindSettingsEvents() {
    // حدث رفع الشعار
    const logoInput = document.getElementById('logo-input');
    if (logoInput) {
        logoInput.addEventListener('change', handleLogoUpload);
    }
    
    // الحفظ الفوري لجميع الحقول
    document.querySelectorAll('#settings input, #settings select').forEach(el => {
        // الحفظ عند الكتابة
        el.addEventListener('input', () => {
            saveSettingsAuto();
        });
        
        // الحفظ عند التغيير
        el.addEventListener('change', () => {
            saveSettingsAuto();
        });
        
        // الحفظ عند الخروج من الحقل
        el.addEventListener('blur', () => {
            saveSettingsAuto();
        });
        
        // الحفظ عند الضغط على Enter
        el.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                saveSettingsAuto();
            }
        });
    });
    
    console.log('✅ تم ربط أحداث الحفظ الفوري للإعدادات');
}

/**
 * حفظ الإعدادات تلقائياً (بدون رسائل)
 */
function saveSettingsAuto() {
    try {
        // جمع البيانات من الواجهة
        const newSettings = {
            name: document.getElementById('company-name-input')?.value || '',
            address: document.getElementById('company-address')?.value || '',
            phone: document.getElementById('company-phone')?.value || '',
            email: document.getElementById('company-email')?.value || '',
            website: document.getElementById('company-website')?.value || '',
            taxNumber: document.getElementById('company-tax')?.value || '',
            currency: document.getElementById('currency')?.value || 'LYD',
            logo: companySettings.logo || ''
        };
        
        // تحديث المتغير العام
        companySettings = newSettings;
        
        // حفظ فوري في localStorage
        localStorage.setItem('glassERP_settings', JSON.stringify(companySettings));
        
        // حفظ النظام بالكامل بعد حفظ الإعدادات
        if (typeof saveSystemData === 'function') {
            saveSystemData();
        }
        
        // تأكيد الحفظ
        const savedData = localStorage.getItem('glassERP_settings');
        if (!savedData) {
            console.error('❌ فشل في الحفظ الفوري للإعدادات');
            return false;
        }
        
        // تطبيق التغييرات على النظام
        applySettingsToSystem();
        
        console.log('💾 تم الحفظ الفوري للإعدادات');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في الحفظ الفوري:', error);
        return false;
    }
}

/**
 * تحميل الإعدادات
 */
function loadSettings() {
    try {
        const savedSettings = localStorage.getItem('glassERP_settings');
        
        if (savedSettings) {
            companySettings = JSON.parse(savedSettings);
            console.log('📋 تم تحميل الإعدادات المحفوظة:', companySettings);
        } else {
            companySettings = {
                name: '', 
                address: '', 
                phone: '', 
                email: '', 
                website: '', 
                taxNumber: '', 
                currency: 'LYD', 
                logo: ''
            };
            console.log('⚠️ لا توجد إعدادات محفوظة، تم استخدام الإعدادات الافتراضية');
        }
        
        // تطبيق الإعدادات على الواجهة
        applySettingsToUI();
        
        // تطبيق الإعدادات على النظام
        applySettingsToSystem();
        
        console.log('✅ تم تحميل وتطبيق الإعدادات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل الإعدادات:', error);
        // استخدام الإعدادات الافتراضية في حالة الخطأ
        companySettings = {
            name: '', 
            address: '', 
            phone: '', 
            email: '', 
            website: '', 
            taxNumber: '', 
            currency: 'LYD', 
            logo: ''
        };
        applySettingsToUI();
    }
}

/**
 * تطبيق الإعدادات على الواجهة
 */
function applySettingsToUI() {
    // تأخير قصير للتأكد من تحميل الواجهة
    setTimeout(() => {
        try {
            // تطبيق على الحقول
            const fields = {
                'company-name-input': companySettings.name || '',
                'company-address': companySettings.address || '',
                'company-phone': companySettings.phone || '',
                'company-email': companySettings.email || '',
                'company-website': companySettings.website || '',
                'company-tax': companySettings.taxNumber || '',
                'currency': companySettings.currency || 'LYD'
            };
            
            Object.keys(fields).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = fields[id];
                    console.log(`✅ تم تطبيق ${id}: ${fields[id]}`);
                } else {
                    console.warn(`⚠️ العنصر ${id} غير موجود في الواجهة`);
                }
            });
            
            // تطبيق الشعار
            if (companySettings.logo) {
                const logoPreview = document.getElementById('logo-preview');
                if (logoPreview) {
                    logoPreview.innerHTML = `<img src="${companySettings.logo}" alt="شعار الشركة">`;
                    console.log('✅ تم تطبيق شعار الشركة');
                }
            }
            
            // تحديث عنوان الصفحة
            if (companySettings.name) {
                document.title = `${companySettings.name} - Glass ERP System`;
                console.log('✅ تم تحديث عنوان الصفحة');
            }
            
            console.log('✅ تم تطبيق الإعدادات على الواجهة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تطبيق الإعدادات على الواجهة:', error);
        }
    }, 100);
}

/**
 * حفظ الإعدادات
 */
function saveSettings() {
    try {
        // جمع البيانات من الواجهة
        const newSettings = {
            name: document.getElementById('company-name-input')?.value || '',
            address: document.getElementById('company-address')?.value || '',
            phone: document.getElementById('company-phone')?.value || '',
            email: document.getElementById('company-email')?.value || '',
            website: document.getElementById('company-website')?.value || '',
            taxNumber: document.getElementById('company-tax')?.value || '',
            currency: document.getElementById('currency')?.value || 'LYD',
            logo: companySettings.logo || ''
        };
        
        // التحقق من صحة البيانات
        const companyNameField = document.getElementById('company-name-input');
        console.log('🔍 فحص حقل اسم الشركة:', companyNameField);
        console.log('🔍 قيمة الحقل:', companyNameField?.value);
        console.log('🔍 قيمة newSettings.name:', newSettings.name);
        
        if (!companyNameField) {
            console.error('❌ حقل اسم الشركة غير موجود');
            showErrorMessage('خطأ في الواجهة - حقل اسم الشركة غير موجود');
            return;
        }
        
        if (!newSettings.name || !newSettings.name.trim()) {
            console.warn('⚠️ اسم الشركة فارغ');
            console.warn('⚠️ قيمة الحقل:', companyNameField.value);
            console.warn('⚠️ طول القيمة:', companyNameField.value.length);
            showErrorMessage('اسم الشركة مطلوب - يرجى إدخال اسم الشركة');
            companyNameField.focus();
            companyNameField.style.borderColor = '#dc3545';
            companyNameField.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
            return;
        }
        
        // تحديث المتغير العام
        companySettings = newSettings;
        
        // حفظ فوري في localStorage
        localStorage.setItem('glassERP_settings', JSON.stringify(companySettings));
        
        // حفظ النظام بالكامل بعد حفظ الإعدادات
        if (typeof saveSystemData === 'function') {
            saveSystemData();
        }
        
        // تأكيد الحفظ فوراً
        const savedData = localStorage.getItem('glassERP_settings');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            console.log('💾 تم حفظ إعدادات الشركة فوراً');
            console.log('🏢 البيانات المحفوظة:', parsedData);
            console.log('🏢 اسم الشركة المحفوظ:', parsedData.name);
        } else {
            console.error('❌ فشل في حفظ البيانات');
            showErrorMessage('فشل في حفظ البيانات');
            return;
        }
        
        // تحديث عنوان الصفحة
        if (companySettings.name) {
            document.title = `${companySettings.name} - Glass ERP System`;
        }
        
        // تطبيق التغييرات على النظام
        applySettingsToSystem();
        
        // إضافة إشعار نجاح الحفظ فقط عند الضغط على زر الحفظ
        if (window.NotificationsModule && window.NotificationsModule.addNotification) {
            window.NotificationsModule.addNotification(
                'تم حفظ الإعدادات بنجاح',
                'تم تحديث إعدادات الشركة وحفظها في النظام',
                'success',
                'settings'
            );
        }
        
        // رسالة نجاح إضافية
        showSuccessMessage('تم حفظ إعدادات الشركة بنجاح');
        console.log('✅ تم حفظ الإعدادات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في حفظ الإعدادات:', error);
        showErrorMessage('خطأ في حفظ الإعدادات');
    }
}

/**
 * تطبيق الإعدادات على النظام
 */
function applySettingsToSystem() {
    try {
        // تحديث معلومات الشركة في الشريط العلوي
        if (typeof updateCompanyInfo === 'function') {
            updateCompanyInfo();
        }
        
        // تحديث معلومات الشركة في الداشبورد
        if (typeof loadCompanyInfoForDashboard === 'function') {
            loadCompanyInfoForDashboard();
        }
        
        // تحديث العملة في النظام
        if (typeof updateCurrencyFormat === 'function') {
            updateCurrencyFormat(companySettings.currency);
        }
        
        // تحديث عنوان الصفحة
        if (companySettings.name) {
            document.title = `${companySettings.name} - Glass ERP System`;
        }
        
        // تحديث اسم الشركة في الشريط العلوي (header)
        const topHeaderCompanyName = document.getElementById('company-name');
        if (topHeaderCompanyName) {
            topHeaderCompanyName.textContent = companySettings.name || '';
            console.log('✅ تم تحديث اسم الشركة في الشريط العلوي (header):', companySettings.name);
        }
        
        // تحديث معلومات الشركة في الشريط العلوي فوراً
        const companyNameElement = document.getElementById('company-name-input');
        if (companyNameElement) {
            companyNameElement.textContent = companySettings.name || '';
            console.log('✅ تم تحديث اسم الشركة في الشريط العلوي:', companySettings.name);
        }
        
        // تحديث معلومات الشركة في الداشبورد فوراً
        const dashboardCompanyName = document.querySelector('.dashboard-company-name');
        if (dashboardCompanyName) {
            dashboardCompanyName.textContent = companySettings.name || '';
            console.log('✅ تم تحديث اسم الشركة في الداشبورد:', companySettings.name);
        }
        
        const dashboardCompanyAddress = document.querySelector('#dashboard-company-address');
        if (dashboardCompanyAddress) {
            dashboardCompanyAddress.textContent = companySettings.address || '';
            console.log('✅ تم تحديث عنوان الشركة في الداشبورد:', companySettings.address);
        }
        
        const dashboardCompanyContact = document.querySelector('#dashboard-company-contact');
        if (dashboardCompanyContact) {
            const phone = companySettings.phone || '';
            const email = companySettings.email || '';
            const contactText = phone && email ? `هاتف: ${phone} | بريد: ${email}` : 
                               phone ? `هاتف: ${phone}` : 
                               email ? `بريد: ${email}` : '';
            dashboardCompanyContact.textContent = contactText;
            console.log('✅ تم تحديث معلومات الاتصال في الداشبورد');
        }
        
        // تحديث الشعار في الداشبورد
        const companyLogoElement = document.getElementById('company-logo');
        const logoPlaceholderElement = document.getElementById('logo-placeholder');
        
        if (companyLogoElement && logoPlaceholderElement) {
            if (companySettings.logo) {
                companyLogoElement.src = companySettings.logo;
                companyLogoElement.style.display = 'block';
                logoPlaceholderElement.style.display = 'none';
                console.log('✅ تم تحديث شعار الشركة في الداشبورد');
            } else {
                companyLogoElement.style.display = 'none';
                logoPlaceholderElement.style.display = 'flex';
                console.log('⚠️ لا يوجد شعار محفوظ');
            }
        }
        
        console.log('🔄 تم تطبيق الإعدادات على النظام بنجاح');
        console.log('🏢 معلومات الشركة:', companySettings.name);
        console.log('💰 العملة:', companySettings.currency);
        
    } catch (error) {
        console.error('❌ خطأ في تطبيق الإعدادات:', error);
    }
}

/**
 * إعادة تعيين الإعدادات
 */
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        try {
            // مسح الإعدادات
            localStorage.removeItem('glassERP_settings');
            
                    // إعادة تعيين المتغير
        companySettings = {
            name: '',
            address: '',
            phone: '',
            email: '',
            website: '',
            taxNumber: '',
            currency: 'LYD',
            logo: ''
        };
            
            // إعادة تطبيق الواجهة
            applySettingsToUI();
            
            // إعادة تعيين عنوان الصفحة
            document.title = 'Glass ERP System - Development';
            
            console.log('🔄 تم إعادة تعيين الإعدادات');
            showSuccessMessage('تم إعادة تعيين الإعدادات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);
            showErrorMessage('خطأ في إعادة تعيين الإعدادات');
        }
    }
}

/**
 * معالجة رفع الشعار
 */
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showErrorMessage('يرجى اختيار ملف صورة صالح');
        return;
    }
    
    // التحقق من حجم الملف (أقل من 2 ميجابايت)
    if (file.size > 2 * 1024 * 1024) {
        showErrorMessage('حجم الملف يجب أن يكون أقل من 2 ميجابايت');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const logoPreview = document.getElementById('logo-preview');
        if (logoPreview) {
            logoPreview.innerHTML = `<img src="${e.target.result}" alt="شعار الشركة">`;
        }
        
        // حفظ الشعار
        companySettings.logo = e.target.result;
        saveSettings();
        
        console.log('🖼️ تم رفع شعار الشركة');
    };
    
    reader.readAsDataURL(file);
}



/**
 * عرض رسالة نجاح (للحالات الأخرى)
 */
function showSuccessMessage(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    if (typeof GlassERP !== 'undefined' && GlassERP.showSuccess) {
        GlassERP.showSuccess(message);
    } else {
        alert(message);
    }
}

/**
 * عرض رسالة خطأ
 */
function showErrorMessage(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    if (typeof GlassERP !== 'undefined' && GlassERP.showError) {
        GlassERP.showError(message);
    } else {
        alert('خطأ: ' + message);
    }
}

/**
 * الحصول على إعدادات الشركة
 */
function getCompanySettings() {
    return companySettings;
}

/**
 * تحديث إعدادات الشركة
 */
function updateCompanySettings(newSettings) {
    companySettings = { ...companySettings, ...newSettings };
    saveSettings();
}

/**
 * اختبار حفظ واسترجاع البيانات
 */
function testDataPersistence() {
    console.log('🧪 بدء اختبار حفظ واسترجاع البيانات...');
    
    // حفظ بيانات تجريبية
    const testData = {
        name: 'شركة الاختبار',
        address: 'عنوان الاختبار',
        phone: '0*********',
        email: '<EMAIL>',
        website: 'www.test.com',
        taxNumber: '*********',
        currency: 'LYD',
        language: 'ar',
        logo: ''
    };
    
    localStorage.setItem('glassERP_settings', JSON.stringify(testData));
    console.log('💾 تم حفظ البيانات التجريبية:', testData);
    
    // استرجاع البيانات
    const retrievedData = localStorage.getItem('glassERP_settings');
    const parsedData = JSON.parse(retrievedData);
    console.log('📋 تم استرجاع البيانات:', parsedData);
    
    // مقارنة البيانات
    const isMatch = JSON.stringify(testData) === JSON.stringify(parsedData);
    console.log('✅ تطابق البيانات:', isMatch);
    
    return isMatch;
}

// تصدير الدوال للاستخدام في الملفات الأخرى
window.SettingsModule = {
    initializeSettings,
    loadSettings,
    saveSettings,
    getCompanySettings,
    updateCompanySettings,
    testDataPersistence
};

// تهيئة الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قليل للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        if (document.getElementById('settings').classList.contains('active')) {
            initializeSettings();
        }
    }, 100);
});

// الحفظ التلقائي عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    if (typeof saveSettingsAuto === 'function') saveSettingsAuto();
});