// SQLite database setup using sql.js
// This file will handle all database operations for the ERP system

// تحميل مكتبة sql.js (يجب تضمينها في index.html)
// <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>

let db = null;

async function initSQLite() {
    if (db) return db;
    if (!window.initSqlJs) {
        throw new Error('sql.js library is not loaded!');
    }
    const SQL = await window.initSqlJs({ locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}` });
    db = new SQL.Database();
    // إنشاء الجداول الأساسية
    db.run(`CREATE TABLE IF NOT EXISTS settings (
        name TEXT,
        address TEXT,
        phone TEXT,
        email TEXT,
        website TEXT,
        taxNumber TEXT,
        currency TEXT,
        logo TEXT
    );`);
    db.run(`CREATE TABLE IF NOT EXISTS accounts (
        id TEXT PRIMARY KEY,
        name TEXT,
        type TEXT,
        parentId TEXT,
        level INTEGER,
        balance REAL,
        isActive INTEGER
    );`);
    db.run(`CREATE TABLE IF NOT EXISTS journalEntries (
        id TEXT PRIMARY KEY,
        date TEXT,
        description TEXT
    );`);
    db.run(`CREATE TABLE IF NOT EXISTS journalLines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        journalId TEXT,
        accountId TEXT,
        debit REAL,
        credit REAL
    );`);
    return db;
}

// مثال: إضافة إعدادات الشركة
async function saveSettingsToSQLite(settings) {
    const db = await initSQLite();
    db.run('DELETE FROM settings;');
    db.run(`INSERT INTO settings (name, address, phone, email, website, taxNumber, currency, logo) VALUES (?, ?, ?, ?, ?, ?, ?, ?);`,
        [settings.name, settings.address, settings.phone, settings.email, settings.website, settings.taxNumber, settings.currency, settings.logo]);
}

// مثال: جلب إعدادات الشركة
async function getSettingsFromSQLite() {
    const db = await initSQLite();
    const res = db.exec('SELECT * FROM settings LIMIT 1;');
    if (res[0] && res[0].values[0]) {
        const cols = res[0].columns;
        const vals = res[0].values[0];
        let obj = {};
        cols.forEach((col, i) => obj[col] = vals[i]);
        return obj;
    }
    return null;
}

// يمكن إضافة دوال مماثلة للحسابات وقيود اليومية
// ...

window.initSQLite = initSQLite;
window.saveSettingsToSQLite = saveSettingsToSQLite;
window.getSettingsFromSQLite = getSettingsFromSQLite;
// ... تصدير دوال الحسابات والقيود اليومية لاحقًا
