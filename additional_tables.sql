-- جداول إضافية للنظام
-- يمكنك نسخ هذا الكود وتنفيذه في SQL Editor في Supabase

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    first_name VARCHAR(100),
    last_name VARCHA<PERSON>(100),
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أدوار المستخدمين
CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS activity_logs (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    module VARCHAR(50),
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    size_bytes BIGINT,
    backup_type VARCHAR(50) DEFAULT 'full',
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_config (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);

-- إنشاء trigger لتحديث updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at 
    BEFORE UPDATE ON user_roles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at 
    BEFORE UPDATE ON system_config 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات تجريبية للصلاحيات
INSERT INTO permissions (name, description, module) VALUES 
    ('read_accounts', 'قراءة الحسابات', 'accounts'),
    ('write_accounts', 'كتابة الحسابات', 'accounts'),
    ('delete_accounts', 'حذف الحسابات', 'accounts'),
    ('read_journal', 'قراءة القيود اليومية', 'accounts'),
    ('write_journal', 'كتابة القيود اليومية', 'accounts'),
    ('delete_journal', 'حذف القيود اليومية', 'accounts'),
    ('read_reports', 'قراءة التقارير', 'reports'),
    ('export_data', 'تصدير البيانات', 'system'),
    ('manage_users', 'إدارة المستخدمين', 'system'),
    ('system_settings', 'إعدادات النظام', 'system');

-- إدراج أدوار تجريبية
INSERT INTO user_roles (name, description, permissions) VALUES 
    ('admin', 'مدير النظام', '["read_accounts", "write_accounts", "delete_accounts", "read_journal", "write_journal", "delete_journal", "read_reports", "export_data", "manage_users", "system_settings"]'),
    ('accountant', 'محاسب', '["read_accounts", "write_accounts", "read_journal", "write_journal", "read_reports"]'),
    ('user', 'مستخدم عادي', '["read_accounts", "read_journal", "read_reports"]'),
    ('viewer', 'مشاهد فقط', '["read_accounts", "read_journal"]');

-- إدراج إعدادات نظام تجريبية
INSERT INTO system_config (key, value, description, is_public) VALUES 
    ('app_version', '"2.0.0"', 'إصدار التطبيق', true),
    ('maintenance_mode', 'false', 'وضع الصيانة', true),
    ('max_file_size', '5242880', 'الحد الأقصى لحجم الملف', false),
    ('session_timeout', '3600', 'مهلة الجلسة بالثواني', false),
    ('backup_schedule', '"daily"', 'جدول النسخ الاحتياطي', false),
    ('email_settings', '{"smtp_server": "smtp.gmail.com", "smtp_port": 587, "enabled": true}', 'إعدادات البريد الإلكتروني', false);

-- عرض الجداول المنشأة
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'permissions', 'user_roles', 'activity_logs', 'notifications', 'backups', 'system_config')
ORDER BY table_name; 