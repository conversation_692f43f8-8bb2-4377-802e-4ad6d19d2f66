-- إنشاء جدول إعدادات الشركة في Supabase
-- يمكنك نسخ هذا الكود وتنفيذه في SQL Editor في Supabase

-- إنشاء الجدول
CREATE TABLE IF NOT EXISTS company_settings (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    company_address TEXT,
    company_phone VARCHAR(50),
    company_email VARCHAR(255),
    company_website VARCHAR(255),
    tax_number VARCHAR(50),
    currency VARCHAR(10) DEFAULT 'LYD',
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    notes TEXT
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_company_settings_active ON company_settings(is_active);
CREATE INDEX IF NOT EXISTS idx_company_settings_name ON company_settings(company_name);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at
CREATE TRIGGER update_company_settings_updated_at 
    BEFORE UPDATE ON company_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات تجريبية
INSERT INTO company_settings (
    company_name, 
    company_address, 
    company_phone, 
    company_email, 
    company_website, 
    tax_number, 
    currency, 
    logo_url, 
    notes
) VALUES 
    ('شركة الزجاج المتقدمة', 'طرابلس - ليبيا', '00218-21-1234567', '<EMAIL>', 'https://www.glasscompany.ly', '*********', 'LYD', 'https://example.com/logo.png', 'إعدادات افتراضية للشركة'),
    ('مؤسسة الألمنيوم والزجاج', 'بنغازي - ليبيا', '00218-61-9876543', '<EMAIL>', 'https://www.aluminumglass.ly', '*********', 'LYD', 'https://example.com/logo2.png', 'مؤسسة متخصصة في الألمنيوم والزجاج'),
    ('شركة النوافذ الحديثة', 'مصراتة - ليبيا', '00218-31-5555555', '<EMAIL>', 'https://www.modernwindows.ly', '*********', 'LYD', 'https://example.com/logo3.png', 'متخصصة في النوافذ والأبواب'),
    ('مصنع الزجاج الوطني', 'الزاوية - ليبيا', '00218-23-7777777', '<EMAIL>', 'https://www.nationalglass.ly', '*********', 'LYD', 'https://example.com/logo4.png', 'مصنع وطني للزجاج'),
    ('شركة الألمنيوم المتحدة', 'سرت - ليبيا', '00218-51-8888888', '<EMAIL>', 'https://www.unitedaluminum.ly', '*********', 'LYD', 'https://example.com/logo5.png', 'شركة متخصصة في الألمنيوم');

-- إنشاء RLS (Row Level Security) إذا كنت تريد حماية البيانات
-- ALTER TABLE company_settings ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات RLS (اختياري)
-- CREATE POLICY "Enable read access for all users" ON company_settings FOR SELECT USING (true);
-- CREATE POLICY "Enable insert for authenticated users only" ON company_settings FOR INSERT WITH CHECK (auth.role() = 'authenticated');
-- CREATE POLICY "Enable update for authenticated users only" ON company_settings FOR UPDATE USING (auth.role() = 'authenticated');
-- CREATE POLICY "Enable delete for authenticated users only" ON company_settings FOR DELETE USING (auth.role() = 'authenticated');

-- عرض البيانات المدرجة
SELECT * FROM company_settings; 