/**
 * Performance Test Module - موديول اختبار الأداء
 * ========================================
 */

/**
 * اختبار شامل للأداء
 */
async function runPerformanceTests() {
    console.log('🧪 بدء اختبارات الأداء...');
    
    // اختبار سرعة التحميل
    testLoadSpeed();
    
    // اختبار حفظ البيانات
    await testDataPersistence();
    
    // اختبار استجابة الواجهة
    testUIResponsiveness();
    
    // اختبار استهلاك الذاكرة
    testMemoryUsage();
    
    // اختبار الرسوم البيانية
    testChartsPerformance();
    
    console.log('✅ تم الانتهاء من اختبارات الأداء');
}

/**
 * اختبار سرعة التحميل
 */
function testLoadSpeed() {
    console.log('⏱️ اختبار سرعة التحميل...');
    
    const startTime = performance.now();
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        console.log(`📊 وقت التحميل: ${loadTime.toFixed(2)}ms`);
        
        if (loadTime < 1000) {
            console.log('✅ سرعة التحميل ممتازة');
        } else if (loadTime < 2000) {
            console.log('⚠️ سرعة التحميل جيدة');
        } else {
            console.log('❌ سرعة التحميل بطيئة');
        }
    }, 100);
}

/**
 * اختبار حفظ البيانات
 */
async function testDataPersistence() {
    console.log('💾 اختبار حفظ البيانات...');
    
    const testData = {
        test: true,
        timestamp: new Date().toISOString(),
        random: Math.random()
    };
    
    try {
        // اختبار الحفظ
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // اختبار حفظ بيانات تجريبية في SQLite
            await sqliteDB.saveCompanySettings({
                name: 'شركة اختبار الأداء',
                testData: testData,
                createdAt: new Date().toISOString()
            });
            console.log('✅ نظام الحفظ في SQLite يعمل');
            
            // اختبار القراءة
            const savedData = await sqliteDB.getCompanySettings();
            if (savedData && savedData.testData && savedData.testData.test === testData.test) {
                console.log('✅ قراءة البيانات من SQLite تعمل');
            } else {
                console.log('❌ خطأ في قراءة البيانات من SQLite');
            }
            
            // تنظيف البيانات التجريبية
            await sqliteDB.deleteCompanySettings();
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار حفظ البيانات:', error);
    }
}

/**
 * اختبار استجابة الواجهة
 */
function testUIResponsiveness() {
    console.log('🖱️ اختبار استجابة الواجهة...');
    
    const startTime = performance.now();
    
    // محاكاة حدث النقر
    const testEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
    });
    
    // اختبار استجابة الأزرار
    const buttons = document.querySelectorAll('button');
    let responsiveButtons = 0;
    
    buttons.forEach(button => {
        const clickStart = performance.now();
        button.dispatchEvent(testEvent);
        const clickEnd = performance.now();
        
        if (clickEnd - clickStart < 100) {
            responsiveButtons++;
        }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    console.log(`📊 وقت الاستجابة: ${responseTime.toFixed(2)}ms`);
    console.log(`📊 الأزرار المستجيبة: ${responsiveButtons}/${buttons.length}`);
    
    if (responseTime < 500) {
        console.log('✅ استجابة الواجهة ممتازة');
    } else if (responseTime < 1000) {
        console.log('⚠️ استجابة الواجهة جيدة');
    } else {
        console.log('❌ استجابة الواجهة بطيئة');
    }
}

/**
 * اختبار استهلاك الذاكرة
 */
function testMemoryUsage() {
    console.log('🧠 اختبار استهلاك الذاكرة...');
    
    if (performance.memory) {
        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
        
        console.log(`📊 الذاكرة المستخدمة: ${usedMB.toFixed(2)}MB`);
        console.log(`📊 إجمالي الذاكرة: ${totalMB.toFixed(2)}MB`);
        console.log(`📊 حد الذاكرة: ${limitMB.toFixed(2)}MB`);
        
        const usagePercentage = (usedMB / limitMB) * 100;
        
        if (usagePercentage < 50) {
            console.log('✅ استهلاك الذاكرة ممتاز');
        } else if (usagePercentage < 80) {
            console.log('⚠️ استهلاك الذاكرة جيد');
        } else {
            console.log('❌ استهلاك الذاكرة عالي');
        }
    } else {
        console.log('⚠️ معلومات الذاكرة غير متوفرة');
    }
}

/**
 * اختبار أداء الرسوم البيانية
 */
function testChartsPerformance() {
    console.log('📊 اختبار أداء الرسوم البيانية...');
    
    const chartElements = document.querySelectorAll('canvas');
    console.log(`📊 عدد الرسوم البيانية: ${chartElements.length}`);
    
    if (chartElements.length > 0) {
        chartElements.forEach((canvas, index) => {
            const startTime = performance.now();
            
            // محاكاة تحديث الرسم البياني
            setTimeout(() => {
                const endTime = performance.now();
                const renderTime = endTime - startTime;
                
                console.log(`📊 الرسم البياني ${index + 1}: ${renderTime.toFixed(2)}ms`);
                
                if (renderTime < 100) {
                    console.log(`✅ أداء الرسم البياني ${index + 1} ممتاز`);
                } else if (renderTime < 300) {
                    console.log(`⚠️ أداء الرسم البياني ${index + 1} جيد`);
                } else {
                    console.log(`❌ أداء الرسم البياني ${index + 1} بطيء`);
                }
            }, 50);
        });
    } else {
        console.log('⚠️ لا توجد رسوم بيانية للاختبار');
    }
}

/**
 * اختبار نظام التخزين المؤقت
 */
async function testCacheSystem() {
    console.log('🗄️ اختبار نظام التخزين المؤقت...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const startTime = performance.now();
            
            // اختبار القراءة المتكررة
            for (let i = 0; i < 10; i++) {
                await sqliteDB.getCompanySettings();
            }
            
            const endTime = performance.now();
            const avgTime = (endTime - startTime) / 10;
            
            console.log(`📊 متوسط وقت القراءة: ${avgTime.toFixed(2)}ms`);
            
            if (avgTime < 50) {
                console.log('✅ أداء التخزين المؤقت ممتاز');
            } else if (avgTime < 100) {
                console.log('⚠️ أداء التخزين المؤقت جيد');
            } else {
                console.log('❌ أداء التخزين المؤقت بطيء');
            }
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام التخزين المؤقت:', error);
    }
}

/**
 * اختبار نظام النسخ الاحتياطي
 */
async function testBackupSystem() {
    console.log('💾 اختبار نظام النسخ الاحتياطي...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const startTime = performance.now();
            
            // اختبار إنشاء نسخة احتياطية
            const backup = await sqliteDB.createBackup();
            
            const endTime = performance.now();
            const backupTime = endTime - startTime;
            
            console.log(`📊 وقت إنشاء النسخة الاحتياطية: ${backupTime.toFixed(2)}ms`);
            
            if (backupTime < 1000) {
                console.log('✅ أداء النسخ الاحتياطي ممتاز');
            } else if (backupTime < 3000) {
                console.log('⚠️ أداء النسخ الاحتياطي جيد');
            } else {
                console.log('❌ أداء النسخ الاحتياطي بطيء');
            }
            
            // اختبار استعادة النسخة الاحتياطية
            if (backup) {
                const restoreStart = performance.now();
                await sqliteDB.restoreBackup(backup);
                const restoreEnd = performance.now();
                const restoreTime = restoreEnd - restoreStart;
                
                console.log(`📊 وقت استعادة النسخة الاحتياطية: ${restoreTime.toFixed(2)}ms`);
            }
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام النسخ الاحتياطي:', error);
    }
}

/**
 * اختبار شامل للنظام
 */
async function runFullSystemTest() {
    console.log('🔍 بدء اختبار شامل للنظام...');
    
    const results = {
        loadSpeed: false,
        dataPersistence: false,
        uiResponsiveness: false,
        memoryUsage: false,
        chartsPerformance: false,
        cacheSystem: false,
        backupSystem: false
    };
    
    try {
        // اختبار سرعة التحميل
        const loadStart = performance.now();
        testLoadSpeed();
        const loadEnd = performance.now();
        results.loadSpeed = (loadEnd - loadStart) < 2000;
        
        // اختبار حفظ البيانات
        await testDataPersistence();
        results.dataPersistence = true;
        
        // اختبار استجابة الواجهة
        const uiStart = performance.now();
        testUIResponsiveness();
        const uiEnd = performance.now();
        results.uiResponsiveness = (uiEnd - uiStart) < 1000;
        
        // اختبار استهلاك الذاكرة
        testMemoryUsage();
        results.memoryUsage = true;
        
        // اختبار الرسوم البيانية
        testChartsPerformance();
        results.chartsPerformance = true;
        
        // اختبار نظام التخزين المؤقت
        await testCacheSystem();
        results.cacheSystem = true;
        
        // اختبار نظام النسخ الاحتياطي
        await testBackupSystem();
        results.backupSystem = true;
        
        // تقرير النتائج
        const passedTests = Object.values(results).filter(r => r).length;
        const totalTests = Object.keys(results).length;
        
        console.log(`📊 نتائج الاختبار الشامل: ${passedTests}/${totalTests} اختبارات نجحت`);
        
        if (passedTests === totalTests) {
            console.log('🎉 جميع الاختبارات نجحت! النظام يعمل بشكل ممتاز');
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة النظام');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار الشامل:', error);
    }
}

/**
 * فحص سريع للأداء
 */
async function quickPerformanceCheck() {
    console.log('⚡ فحص سريع للأداء...');
    
    const checks = [];
    
    // فحص قاعدة البيانات
    if (typeof sqliteDB !== 'undefined' && sqliteDB) {
        checks.push('✅ قاعدة بيانات SQLite متوفرة');
    } else {
        checks.push('❌ قاعدة بيانات SQLite غير متوفرة');
    }
    
    // فحص الذاكرة
    if (performance.memory) {
        const usage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
        if (usage < 80) {
            checks.push('✅ استهلاك الذاكرة مقبول');
        } else {
            checks.push('⚠️ استهلاك الذاكرة عالي');
        }
    }
    
    // فحص استجابة الواجهة
    const buttons = document.querySelectorAll('button');
    if (buttons.length > 0) {
        checks.push(`✅ ${buttons.length} زر متاح`);
    }
    
    // فحص الرسوم البيانية
    const charts = document.querySelectorAll('canvas');
    if (charts.length > 0) {
        checks.push(`✅ ${charts.length} رسم بياني متاح`);
    }
    
    console.log('📋 نتائج الفحص السريع:');
    checks.forEach(check => console.log(check));
    
    return checks;
}

/**
 * اختبار نظام الحفظ
 */
async function testSaveSystem() {
    console.log('💾 اختبار نظام الحفظ...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // اختبار حفظ البيانات المختلفة
            const testData = {
                accounts: [
                    { id: '100000', name: 'حساب نقدي', type: 'asset', balance: 10000 },
                    { id: '200000', name: 'حساب دائن', type: 'liability', balance: 5000 }
                ],
                journalEntries: [
                    { id: 'je_001', number: 'JE001', date: new Date().toISOString(), description: 'قيد تجريبي' }
                ],
                settings: {
                    name: 'شركة اختبار',
                    currency: 'SAR',
                    language: 'ar'
                }
            };
            
            const startTime = performance.now();
            
            // حفظ البيانات
            await sqliteDB.saveAllAccounts(testData.accounts);
            await sqliteDB.saveAllJournalEntries(testData.journalEntries);
            await sqliteDB.saveCompanySettings(testData.settings);
            
            const endTime = performance.now();
            const saveTime = endTime - startTime;
            
            console.log(`📊 وقت الحفظ: ${saveTime.toFixed(2)}ms`);
            
            if (saveTime < 500) {
                console.log('✅ أداء الحفظ ممتاز');
            } else if (saveTime < 1000) {
                console.log('⚠️ أداء الحفظ جيد');
            } else {
                console.log('❌ أداء الحفظ بطيء');
            }
            
            // تنظيف البيانات التجريبية
            await sqliteDB.clearAccounts();
            await sqliteDB.clearJournalEntries();
            await sqliteDB.deleteCompanySettings();
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام الحفظ:', error);
    }
}

/**
 * اختبار الحفظ الأساسي
 */
async function testBasicSave() {
    console.log('💾 اختبار الحفظ الأساسي...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const testData = {
                name: 'اختبار الحفظ',
                timestamp: new Date().toISOString(),
                value: Math.random()
            };
            
            await sqliteDB.saveCompanySettings(testData);
            console.log('✅ الحفظ الأساسي يعمل');
            
            // تنظيف
            await sqliteDB.deleteCompanySettings();
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في الحفظ الأساسي:', error);
    }
}

/**
 * اختبار التحميل الأساسي
 */
async function testBasicLoad() {
    console.log('📂 اختبار التحميل الأساسي...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const data = await sqliteDB.getCompanySettings();
            console.log('✅ التحميل الأساسي يعمل');
            return data;
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
            return null;
        }
    } catch (error) {
        console.error('❌ خطأ في التحميل الأساسي:', error);
        return null;
    }
}

/**
 * اختبار حفظ واجهة المستخدم
 */
async function testUISave() {
    console.log('🖥️ اختبار حفظ واجهة المستخدم...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const uiSettings = {
                theme: 'dark',
                language: 'ar',
                layout: 'compact',
                notifications: true,
                autoSave: true,
                updatedAt: new Date().toISOString()
            };
            
            await sqliteDB.saveCompanySettings(uiSettings);
            console.log('✅ حفظ إعدادات واجهة المستخدم يعمل');
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ واجهة المستخدم:', error);
    }
}

/**
 * اختبار تحميل واجهة المستخدم
 */
async function testUILoad() {
    console.log('🖥️ اختبار تحميل واجهة المستخدم...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const uiSettings = await sqliteDB.getCompanySettings();
            
            if (uiSettings && uiSettings.theme) {
                console.log('✅ تحميل إعدادات واجهة المستخدم يعمل');
                console.log('📋 الإعدادات المحملة:', uiSettings);
            } else {
                console.log('⚠️ لا توجد إعدادات واجهة مستخدم محفوظة');
            }
            
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل واجهة المستخدم:', error);
    }
}

/**
 * اختبار الأداء العام
 */
async function testPerformance() {
    console.log('🚀 اختبار الأداء العام...');
    
    const performanceResults = {
        database: false,
        memory: false,
        ui: false,
        overall: false
    };
    
    try {
        // اختبار قاعدة البيانات
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const dbStart = performance.now();
            await sqliteDB.getCompanySettings();
            const dbEnd = performance.now();
            performanceResults.database = (dbEnd - dbStart) < 100;
        }
        
        // اختبار الذاكرة
        if (performance.memory) {
            const usage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
            performanceResults.memory = usage < 80;
        }
        
        // اختبار واجهة المستخدم
        const buttons = document.querySelectorAll('button');
        performanceResults.ui = buttons.length > 0;
        
        // تقييم عام
        const passedChecks = Object.values(performanceResults).filter(r => r).length;
        performanceResults.overall = passedChecks >= 3;
        
        console.log('📊 نتائج اختبار الأداء:');
        console.log(`- قاعدة البيانات: ${performanceResults.database ? '✅' : '❌'}`);
        console.log(`- الذاكرة: ${performanceResults.memory ? '✅' : '❌'}`);
        console.log(`- واجهة المستخدم: ${performanceResults.ui ? '✅' : '❌'}`);
        console.log(`- الأداء العام: ${performanceResults.overall ? '✅ ممتاز' : '⚠️ يحتاج تحسين'}`);
        
        return performanceResults;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الأداء:', error);
        return performanceResults;
    }
}

// تصدير الدوال للاستخدام
window.GlassERP = window.GlassERP || {};
window.GlassERP.Test = {
    runPerformanceTests,
    runFullSystemTest,
    quickPerformanceCheck,
    testLoadSpeed,
    testDataPersistence,
    testUIResponsiveness,
    testMemoryUsage,
    testChartsPerformance,
    testCacheSystem,
    testBackupSystem,
    testSaveSystem,
    testBasicSave,
    testBasicLoad,
    testUISave,
    testUILoad,
    testPerformance
};

// تشغيل اختبار سريع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.location.search.includes('test=performance')) {
            runFullSystemTest();
        } else if (window.location.search.includes('test=quick')) {
            quickPerformanceCheck();
        }
    }, 2000);
});

// تشغيل الاختبارات تلقائياً إذا كان هناك معامل في URL
if (window.location.search.includes('test=true')) {
    console.log('🧪 تشغيل الاختبارات التلقائية...');
    setTimeout(() => {
        testPerformance();
    }, 1000);
} 