/**
 * Performance Test Module - موديول اختبار الأداء
 * ========================================
 */

/**
 * اختبار شامل للأداء
 */
function runPerformanceTests() {
    console.log('🧪 بدء اختبارات الأداء...');
    
    // اختبار سرعة التحميل
    testLoadSpeed();
    
    // اختبار حفظ البيانات
    testDataPersistence();
    
    // اختبار استجابة الواجهة
    testUIResponsiveness();
    
    // اختبار استهلاك الذاكرة
    testMemoryUsage();
    
    // اختبار الرسوم البيانية
    testChartsPerformance();
    
    console.log('✅ تم الانتهاء من اختبارات الأداء');
}

/**
 * اختبار سرعة التحميل
 */
function testLoadSpeed() {
    console.log('⏱️ اختبار سرعة التحميل...');
    
    const startTime = performance.now();
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        console.log(`📊 وقت التحميل: ${loadTime.toFixed(2)}ms`);
        
        if (loadTime < 1000) {
            console.log('✅ سرعة التحميل ممتازة');
        } else if (loadTime < 2000) {
            console.log('⚠️ سرعة التحميل جيدة');
        } else {
            console.log('❌ سرعة التحميل بطيئة');
        }
    }, 100);
}

/**
 * اختبار حفظ البيانات
 */
function testDataPersistence() {
    console.log('💾 اختبار حفظ البيانات...');
    
    const testData = {
        test: true,
        timestamp: new Date().toISOString(),
        random: Math.random()
    };
    
    try {
        // اختبار الحفظ
        if (typeof queueDataSave === 'function') {
            queueDataSave('test_data', testData);
            console.log('✅ نظام الحفظ يعمل');
        } else {
            localStorage.setItem('test_data', JSON.stringify(testData));
            console.log('✅ الحفظ المباشر يعمل');
        }
        
        // اختبار القراءة
        const savedData = localStorage.getItem('test_data');
        if (savedData) {
            const parsed = JSON.parse(savedData);
            if (parsed.test === testData.test) {
                console.log('✅ قراءة البيانات تعمل');
            } else {
                console.log('❌ خطأ في قراءة البيانات');
            }
        } else {
            console.log('❌ البيانات غير محفوظة');
        }
        
        // تنظيف البيانات التجريبية
        localStorage.removeItem('test_data');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار حفظ البيانات:', error);
    }
}

/**
 * اختبار استجابة الواجهة
 */
function testUIResponsiveness() {
    console.log('🖱️ اختبار استجابة الواجهة...');
    
    const startTime = performance.now();
    
    // محاكاة حدث النقر
    const testEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
    });
    
    // اختبار استجابة الأزرار
    const buttons = document.querySelectorAll('button');
    let responsiveButtons = 0;
    
    buttons.forEach(button => {
        const clickStart = performance.now();
        button.dispatchEvent(testEvent);
        const clickEnd = performance.now();
        
        if (clickEnd - clickStart < 100) {
            responsiveButtons++;
        }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    console.log(`📊 وقت الاستجابة: ${responseTime.toFixed(2)}ms`);
    console.log(`📊 الأزرار المستجيبة: ${responsiveButtons}/${buttons.length}`);
    
    if (responseTime < 500) {
        console.log('✅ استجابة الواجهة ممتازة');
    } else if (responseTime < 1000) {
        console.log('⚠️ استجابة الواجهة جيدة');
    } else {
        console.log('❌ استجابة الواجهة بطيئة');
    }
}

/**
 * اختبار استهلاك الذاكرة
 */
function testMemoryUsage() {
    console.log('🧠 اختبار استهلاك الذاكرة...');
    
    if (performance.memory) {
        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
        
        console.log(`📊 الذاكرة المستخدمة: ${usedMB.toFixed(2)}MB`);
        console.log(`📊 إجمالي الذاكرة: ${totalMB.toFixed(2)}MB`);
        console.log(`📊 حد الذاكرة: ${limitMB.toFixed(2)}MB`);
        
        const usagePercentage = (usedMB / limitMB) * 100;
        
        if (usagePercentage < 50) {
            console.log('✅ استهلاك الذاكرة ممتاز');
        } else if (usagePercentage < 80) {
            console.log('⚠️ استهلاك الذاكرة جيد');
        } else {
            console.log('❌ استهلاك الذاكرة عالي');
        }
    } else {
        console.log('⚠️ معلومات الذاكرة غير متوفرة');
    }
}

/**
 * اختبار أداء الرسوم البيانية
 */
function testChartsPerformance() {
    console.log('📊 اختبار أداء الرسوم البيانية...');
    
    const chartElements = document.querySelectorAll('canvas');
    console.log(`📊 عدد الرسوم البيانية: ${chartElements.length}`);
    
    if (chartElements.length > 0) {
        chartElements.forEach((canvas, index) => {
            const startTime = performance.now();
            
            // محاكاة تحديث الرسم البياني
            setTimeout(() => {
                const endTime = performance.now();
                const renderTime = endTime - startTime;
                
                console.log(`📊 الرسم البياني ${index + 1}: ${renderTime.toFixed(2)}ms`);
                
                if (renderTime < 100) {
                    console.log(`✅ أداء الرسم البياني ${index + 1} ممتاز`);
                } else if (renderTime < 300) {
                    console.log(`⚠️ أداء الرسم البياني ${index + 1} جيد`);
                } else {
                    console.log(`❌ أداء الرسم البياني ${index + 1} بطيء`);
                }
            }, 50);
        });
    } else {
        console.log('⚠️ لا توجد رسوم بيانية للاختبار');
    }
}

/**
 * اختبار نظام الـ Cache
 */
function testCacheSystem() {
    console.log('🗄️ اختبار نظام الـ Cache...');
    
    if (typeof performanceCache !== 'undefined') {
        const cacheSize = performanceCache.size;
        console.log(`📊 حجم الـ Cache: ${cacheSize} عنصر`);
        
        // اختبار إضافة عنصر للـ cache
        const testKey = 'test_cache_key';
        const testValue = { test: true, timestamp: Date.now() };
        
        performanceCache.set(testKey, testValue);
        
        // اختبار قراءة من الـ cache
        const cachedValue = performanceCache.get(testKey);
        
        if (cachedValue && cachedValue.test === testValue.test) {
            console.log('✅ نظام الـ Cache يعمل بشكل صحيح');
        } else {
            console.log('❌ خطأ في نظام الـ Cache');
        }
        
        // تنظيف
        performanceCache.delete(testKey);
        
    } else {
        console.log('⚠️ نظام الـ Cache غير متوفر');
    }
}

/**
 * اختبار نظام الـ Backup
 */
function testBackupSystem() {
    console.log('💾 اختبار نظام الـ Backup...');
    
    try {
        // فحص وجود backups
        const keys = Object.keys(localStorage);
        const backupKeys = keys.filter(key => key.startsWith('glassERP_backup_'));
        
        console.log(`📊 عدد النسخ الاحتياطية: ${backupKeys.length}`);
        
        if (backupKeys.length > 0) {
            console.log('✅ نظام الـ Backup يعمل');
            
            // فحص أحدث backup
            const latestBackup = backupKeys.sort().pop();
            const backupData = localStorage.getItem(latestBackup);
            
            if (backupData) {
                const parsed = JSON.parse(backupData);
                console.log(`📊 آخر backup: ${parsed.timestamp}`);
            }
        } else {
            console.log('⚠️ لا توجد نسخ احتياطية');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام الـ Backup:', error);
    }
}

/**
 * اختبار شامل للنظام
 */
function runFullSystemTest() {
    console.log('🔍 بدء اختبار شامل للنظام...');
    
    const tests = [
        testLoadSpeed,
        testDataPersistence,
        testUIResponsiveness,
        testMemoryUsage,
        testChartsPerformance,
        testCacheSystem,
        testBackupSystem
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    tests.forEach((test, index) => {
        try {
            console.log(`\n--- اختبار ${index + 1}/${totalTests} ---`);
            test();
            passedTests++;
        } catch (error) {
            console.error(`❌ فشل في الاختبار ${index + 1}:`, error);
        }
    });
    
    console.log(`\n📊 نتائج الاختبار: ${passedTests}/${totalTests} نجح`);
    
    if (passedTests === totalTests) {
        console.log('🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي');
    } else if (passedTests >= totalTests * 0.8) {
        console.log('✅ معظم الاختبارات نجحت! النظام يعمل بشكل جيد');
    } else {
        console.log('⚠️ بعض الاختبارات فشلت! النظام يحتاج تحسين');
    }
}

/**
 * اختبار سريع للأداء
 */
function quickPerformanceCheck() {
    console.log('⚡ فحص سريع للأداء...');
    
    const startTime = performance.now();
    
    // فحص سرعة التحميل
    const loadTime = performance.now() - startTime;
    
    // فحص استهلاك الذاكرة
    let memoryUsage = 'غير متوفر';
    if (performance.memory) {
        memoryUsage = `${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`;
    }
    
    // فحص عدد العناصر
    const elements = document.querySelectorAll('*').length;
    
    console.log(`📊 وقت التحميل: ${loadTime.toFixed(2)}ms`);
    console.log(`📊 استهلاك الذاكرة: ${memoryUsage}`);
    console.log(`📊 عدد العناصر: ${elements}`);
    
    // تقييم سريع
    let score = 0;
    if (loadTime < 1000) score += 2;
    if (elements < 1000) score += 1;
    if (memoryUsage !== 'غير متوفر') score += 1;
    
    console.log(`📊 النتيجة: ${score}/4`);
    
    if (score >= 3) {
        console.log('✅ الأداء ممتاز');
    } else if (score >= 2) {
        console.log('⚠️ الأداء جيد');
    } else {
        console.log('❌ الأداء يحتاج تحسين');
    }
}

// تصدير الدوال للاستخدام
window.GlassERP = window.GlassERP || {};
window.GlassERP.Test = {
    runPerformanceTests,
    runFullSystemTest,
    quickPerformanceCheck,
    testLoadSpeed,
    testDataPersistence,
    testUIResponsiveness,
    testMemoryUsage,
    testChartsPerformance,
    testCacheSystem,
    testBackupSystem
};

// تشغيل اختبار سريع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.location.search.includes('test=performance')) {
            runFullSystemTest();
        } else if (window.location.search.includes('test=quick')) {
            quickPerformanceCheck();
        }
    }, 2000);
}); 