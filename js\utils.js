/**
 * Utils Module - الدوال المساعدة
 * ========================================
 */

/**
 * دالة مساعدة لتنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0.00';
    }
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatDate(dateString) {
    if (!dateString) return '';
    try {
        return new Date(dateString).toLocaleDateString('ar-SA');
    } catch (error) {
        return dateString;
    }
}

/**
 * دالة مساعدة لعرض رسائل التأكيد
 */
function showConfirm(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * دالة مساعدة لعرض رسائل النجاح
 */
function showSuccess(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert(message);
}

/**
 * دالة مساعدة لعرض رسائل الخطأ
 */
function showError(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert('خطأ: ' + message);
}

/**
 * دالة مساعدة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * دالة مساعدة للتحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
}

/**
 * دالة مساعدة لتنظيف النص من الأحرف الخاصة
 */
function sanitizeText(text) {
    if (typeof text !== 'string') return '';
    return text.replace(/[<>]/g, '');
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان URL صالح
 */
function slugify(text) {
    if (typeof text !== 'string') return '';
    return text
        .toLowerCase()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

/**
 * دالة مساعدة لإنشاء معرف فريد
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * دالة مساعدة لنسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccess('تم نسخ النص بنجاح');
        }).catch(() => {
            showError('فشل في نسخ النص');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('تم نسخ النص بنجاح');
        } catch (err) {
            showError('فشل في نسخ النص');
        }
        document.body.removeChild(textArea);
    }
}

/**
 * دالة مساعدة لتحميل ملف
 */
function downloadFile(content, filename, contentType = 'text/plain') {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

/**
 * دالة مساعدة لتحويل التاريخ إلى نص عربي
 */
function formatDateArabic(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const day = d.getDate();
    const month = months[d.getMonth()];
    const year = d.getFullYear();
    
    return `${day} ${month} ${year}`;
}

/**
 * دالة مساعدة لتحويل الأرقام إلى كلمات عربية
 */
function numberToArabicWords(number) {
    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', 'عشرة', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const hundreds = ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    
    if (number === 0) return 'صفر';
    if (number < 10) return ones[number];
    if (number < 100) {
        if (number < 20) {
            return number === 11 ? 'أحد عشر' : number === 12 ? 'اثنا عشر' : ones[number - 10] + ' عشر';
        }
        return ones[Math.floor(number / 10)] + ' و ' + tens[Math.floor(number / 10)];
    }
    if (number < 1000) {
        return hundreds[Math.floor(number / 100)] + ' و ' + numberToArabicWords(number % 100);
    }
    
    return number.toString();
}

/**
 * دالة مساعدة لتحويل العملة
 */
function convertCurrency(amount, fromCurrency, toCurrency) {
    // هنا يمكن إضافة API لأسعار الصرف
    // حالياً نرجع نفس المبلغ
    return amount;
}

/**
 * دالة مساعدة لتحويل الحجم
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * دالة مساعدة لتحويل الوقت
 */
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

/**
 * دالة مساعدة لتحويل النسبة المئوية
 */
function formatPercentage(value, total) {
    if (total === 0) return '0%';
    const percentage = (value / total) * 100;
    return percentage.toFixed(1) + '%';
}

/**
 * دالة مساعدة لتحويل النص إلى HTML آمن
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * دالة مساعدة لتحويل HTML إلى نص
 */
function unescapeHtml(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان
 */
function capitalizeWords(text) {
    if (typeof text !== 'string') return '';
    return text.replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * دالة مساعدة لتحويل النص إلى جملة
 */
function toSentenceCase(text) {
    if (typeof text !== 'string') return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان URL
 */
function toUrlFriendly(text) {
    if (typeof text !== 'string') return '';
    return text
        .toLowerCase()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

/**
 * دالة مساعدة لتحويل النص إلى كلمات مفصولة
 */
function toWords(text) {
    if (typeof text !== 'string') return '';
    return text
        .replace(/[^\u0600-\u06FF\w\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim()
        .split(' ');
}

/**
 * دالة مساعدة لتحويل النص إلى أحرف
 */
function toCharacters(text) {
    if (typeof text !== 'string') return '';
    return text.split('');
}

/**
 * دالة مساعدة لتحويل النص إلى أسطر
 */
function toLines(text) {
    if (typeof text !== 'string') return '';
    return text.split('\n');
}

/**
 * دالة مساعدة لتحويل النص إلى فقرات
 */
function toParagraphs(text) {
    if (typeof text !== 'string') return '';
    return text.split('\n\n').filter(p => p.trim());
}

/**
 * دالة مساعدة لتحويل النص إلى جمل
 */
function toSentences(text) {
    if (typeof text !== 'string') return '';
    return text.split(/[.!?]+/).filter(s => s.trim());
}

/**
 * دالة مساعدة لتحويل النص إلى كلمات
 */
function toWordCount(text) {
    if (typeof text !== 'string') return 0;
    return text.trim().split(/\s+/).length;
}

/**
 * دالة مساعدة لتحويل النص إلى أحرف
 */
function toCharCount(text) {
    if (typeof text !== 'string') return 0;
    return text.length;
}

/**
 * دالة مساعدة لتحويل النص إلى أسطر
 */
function toLineCount(text) {
    if (typeof text !== 'string') return 0;
    return text.split('\n').length;
}

/**
 * دالة مساعدة لتحويل النص إلى فقرات
 */
function toParagraphCount(text) {
    if (typeof text !== 'string') return 0;
    return text.split('\n\n').filter(p => p.trim()).length;
}

/**
 * دالة مساعدة لتحويل النص إلى جمل
 */
function toSentenceCount(text) {
    if (typeof text !== 'string') return 0;
    return text.split(/[.!?]+/).filter(s => s.trim()).length;
}

/**
 * نظام الترجمة متعدد اللغات
 * ========================================
 */

// متغيرات الترجمة


/**
 * تنسيق الأرقام باللغة الإنجليزية دائماً
 */
function formatNumberEnglish(number, decimals = 2) {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0.00';
    }
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * تنسيق التاريخ بالتقويم الميلادي دائماً
 */
function formatDateGregorian(dateString) {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return dateString;
    }
}

/**
 * تنسيق الوقت
 */
function formatTime(date) {
    if (!date) date = new Date();
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime(date) {
    if (!date) date = new Date();
    return `${formatDateGregorian(date)} ${formatTime(date)}`;
}

/**
 * الحصول على التاريخ والوقت الحالي
 */
function getCurrentDateTime() {
    return new Date();
}

/**
 * تهيئة النظام عند بدء التطبيق
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل نظام Glass ERP بنجاح');
    
    // تهيئة العملة الافتراضية
    updateCurrencyFormat('LYD');
    
    // مسح البيانات التجريبية القديمة
    clearAllTestData();
});

/**
 * تحديث تنسيق العملة
 */
function updateCurrencyFormat(currencyCode) {
    const currencySymbols = {
        'LYD': 'د.ل',
        'USD': '$',
        'EUR': '€',
        'SAR': 'ر.س',
        'AED': 'د.إ',
        'EGP': 'ج.م',
        'JOD': 'د.أ',
        'KWD': 'د.ك',
        'QAR': 'ر.ق',
        'BHD': 'د.ب',
        'GBP': '£'
    };
    
    const symbol = currencySymbols[currencyCode] || currencyCode;
    
    // تحديث تنسيق الأرقام حسب العملة
    window.currentCurrency = {
        code: currencyCode,
        symbol: symbol
    };
    
    console.log(`💰 تم تحديث العملة إلى: ${currencyCode} (${symbol})`);
}

/**
 * تنسيق المبلغ بالعملة الحالية
 */
function formatCurrency(amount, currencyCode = null) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00';
    }
    
    const currency = currencyCode || (window.currentCurrency?.code || 'LYD');
    const symbol = window.currentCurrency?.symbol || 'د.ل';
    
    // تنسيق الرقم باللغة الإنجليزية
    const formattedNumber = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
    
    return `${formattedNumber} ${symbol}`;
}

// تصدير الدوال الجديدة
window.updateCurrencyFormat = updateCurrencyFormat;
window.formatCurrency = formatCurrency;

// تصدير الدوال للاستخدام في الملفات الأخرى
window.Utils = {
    formatNumber,
    formatDate,
    showConfirm,
    showSuccess,
    showError,
    isValidEmail,
    isValidPhone,
    sanitizeText,
    slugify,
    generateId,
    copyToClipboard,
    downloadFile,
    formatDateArabic,
    numberToArabicWords,
    convertCurrency,
    formatFileSize,
    formatDuration,
    formatPercentage,
    escapeHtml,
    unescapeHtml,
    capitalizeWords,
    toSentenceCase,
    toUrlFriendly,
    toWords,
    toCharacters,
    toLines,
    toParagraphs,
    toSentences,
    toWordCount,
    toCharCount,
    toLineCount,
    toParagraphCount,
    toSentenceCount
};

/**
 * دالة مساعدة لقراءة بيانات الشركة من localStorage
 */
function getCompanyData() {
    try {
        const savedSettings = localStorage.getItem('glassERP_settings');
        if (savedSettings) {
            const companySettings = JSON.parse(savedSettings);
            console.log('📋 تم قراءة بيانات الشركة:', companySettings);
            return companySettings;
        } else {
            console.log('⚠️ لا توجد بيانات محفوظة للشركة');
            return {
                name: 'شركة جلاس',
                address: 'عنوان الشركة',
                phone: '--',
                email: '--',
                website: '',
                taxNumber: '',
                currency: 'LYD',
                logo: ''
            };
        }
    } catch (error) {
        console.error('❌ خطأ في قراءة بيانات الشركة:', error);
        return {
            name: 'شركة جلاس',
            address: 'عنوان الشركة',
            phone: '--',
            email: '--',
            website: '',
            taxNumber: '',
            currency: 'LYD',
            logo: ''
        };
    }
}

/**
 * دالة مساعدة لاختبار حفظ بيانات الشركة
 */
function testCompanyDataSave() {
    try {
        const testData = {
            name: 'شركة جلاس للتجارة',
            address: 'طرابلس، ليبيا',
            phone: '+218 21 1234567',
            email: '<EMAIL>',
            website: 'www.glass.ly',
            taxNumber: '*********',
            currency: 'LYD',
            logo: ''
        };
        
        localStorage.setItem('glassERP_settings', JSON.stringify(testData));
        console.log('✅ تم حفظ بيانات تجريبية للشركة:', testData);
        
        // اختبار القراءة
        const readData = getCompanyData();
        console.log('📖 تم قراءة بيانات الشركة:', readData);
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في اختبار حفظ بيانات الشركة:', error);
        return false;
    }
}

/**
 * دالة مساعدة لمسح بيانات الشركة
 */
function clearCompanyData() {
    try {
        localStorage.removeItem('glassERP_settings');
        console.log('🗑️ تم مسح بيانات الشركة');
        return true;
    } catch (error) {
        console.error('❌ خطأ في مسح بيانات الشركة:', error);
        return false;
    }
}

/**
 * دالة اختبار شاملة لنظام بيانات الشركة
 */
function testCompanyDataSystem() {
    console.log('🧪 بدء اختبار نظام بيانات الشركة...');
    
    // اختبار 1: مسح البيانات القديمة
    console.log('📋 اختبار 1: مسح البيانات القديمة');
    clearCompanyData();
    
    // اختبار 2: قراءة البيانات الفارغة
    console.log('📋 اختبار 2: قراءة البيانات الفارغة');
    const emptyData = getCompanyData();
    console.log('البيانات الفارغة:', emptyData);
    
    // اختبار 3: حفظ بيانات تجريبية
    console.log('📋 اختبار 3: حفظ بيانات تجريبية');
    const testResult = testCompanyDataSave();
    if (!testResult) {
        console.error('❌ فشل في اختبار الحفظ');
        return false;
    }
    
    // اختبار 4: قراءة البيانات المحفوظة
    console.log('📋 اختبار 4: قراءة البيانات المحفوظة');
    const savedData = getCompanyData();
    console.log('البيانات المحفوظة:', savedData);
    
    // اختبار 5: التحقق من اسم الشركة
    console.log('📋 اختبار 5: التحقق من اسم الشركة');
    if (savedData.name && savedData.name !== 'شركة جلاس') {
        console.log('✅ اسم الشركة محفوظ بشكل صحيح:', savedData.name);
    } else {
        console.error('❌ مشكلة في حفظ اسم الشركة');
        return false;
    }
    
    // اختبار 6: التحقق من localStorage مباشرة
    console.log('📋 اختبار 6: التحقق من localStorage مباشرة');
    const rawData = localStorage.getItem('glassERP_settings');
    if (rawData) {
        const parsedRawData = JSON.parse(rawData);
        console.log('البيانات الخام من localStorage:', parsedRawData);
        console.log('اسم الشركة من localStorage:', parsedRawData.name);
    } else {
        console.error('❌ لا توجد بيانات في localStorage');
        return false;
    }
    
    console.log('✅ تم إكمال جميع اختبارات نظام بيانات الشركة بنجاح');
    return true;
}

/**
 * دالة لفحص حالة بيانات الشركة الحالية
 */
function checkCompanyDataStatus() {
    console.log('🔍 فحص حالة بيانات الشركة...');
    
    const companyData = getCompanyData();
    console.log('البيانات الحالية:', companyData);
    
    const rawData = localStorage.getItem('glassERP_settings');
    console.log('البيانات الخام في localStorage:', rawData);
    
    if (rawData) {
        try {
            const parsedData = JSON.parse(rawData);
            console.log('البيانات المحللة:', parsedData);
            console.log('اسم الشركة:', parsedData.name);
            console.log('عنوان الشركة:', parsedData.address);
            console.log('هاتف الشركة:', parsedData.phone);
        } catch (error) {
            console.error('❌ خطأ في تحليل البيانات:', error);
        }
    } else {
        console.log('⚠️ لا توجد بيانات في localStorage');
    }
    
    return companyData;
}

/**
 * دالة لمسح جميع البيانات التجريبية من النظام
 */
function clearAllTestData() {
    try {
        console.log('🧹 بدء مسح جميع البيانات التجريبية...');
        
        // قائمة مفاتيح البيانات التجريبية
        const testDataKeys = [
            'glassERP_accounts',
            'glassERP_journalEntries',
            'glassERP_settings',
            'glassERP_notifications',
            'glassERP_dashboardData',
            'glassERP_sampleData',
            'glassERP_testData'
        ];
        
        // مسح كل البيانات التجريبية
        testDataKeys.forEach(key => {
            if (localStorage.getItem(key)) {
                localStorage.removeItem(key);
                console.log(`🗑️ تم مسح: ${key}`);
            }
        });
        
        // مسح أي بيانات أخرى قد تكون تجريبية
        const allKeys = Object.keys(localStorage);
        allKeys.forEach(key => {
            if (key.includes('glassERP') || key.includes('test') || key.includes('sample')) {
                localStorage.removeItem(key);
                console.log(`🗑️ تم مسح: ${key}`);
            }
        });
        
        console.log('✅ تم مسح جميع البيانات التجريبية بنجاح');
        
        // إعادة تعيين المتغيرات العامة
        if (typeof window.accounts !== 'undefined') {
            window.accounts = [];
        }
        if (typeof window.journalEntries !== 'undefined') {
            window.journalEntries = [];
        }
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في مسح البيانات التجريبية:', error);
        return false;
    }
}

/**
 * دالة لإنشاء بيانات تجريبية نظيفة (للاختبار فقط)
 */
function createCleanTestData() {
    try {
        console.log('🧪 إنشاء بيانات تجريبية نظيفة...');
        
        // إنشاء إعدادات شركة تجريبية بسيطة
        const testSettings = {
            name: 'شركة تجريبية',
            address: 'عنوان تجريبي',
            phone: '*********',
            email: '<EMAIL>',
            website: '',
            taxNumber: '',
            currency: 'LYD',
            logo: ''
        };
        
        localStorage.setItem('glassERP_settings', JSON.stringify(testSettings));
        console.log('✅ تم إنشاء بيانات تجريبية نظيفة');
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
        return false;
    }
}

// DataStore: طبقة تخزين بيانات عامة باستخدام IndexedDB
class DataStore {
  constructor(dbName = 'GlassERP', version = 1) {
    this.dbName = dbName;
    this.version = version;
    this.db = null;
  }

  async open() {
    if (this.db) return this.db;
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        // جدول قيود اليومية
        if (!db.objectStoreNames.contains('journalEntries')) {
          const store = db.createObjectStore('journalEntries', { keyPath: 'id' });
          store.createIndex('date', 'date', { unique: false });
        }
        // جدول الحسابات
        if (!db.objectStoreNames.contains('accounts')) {
          const accountsStore = db.createObjectStore('accounts', { keyPath: 'id' });
          accountsStore.createIndex('type', 'type', { unique: false });
          accountsStore.createIndex('parentId', 'parentId', { unique: false });
        }
        // جدول إعدادات الشركة
        if (!db.objectStoreNames.contains('companySettings')) {
          db.createObjectStore('companySettings', { keyPath: 'id' });
        }
      };
      request.onsuccess = (event) => {
        this.db = event.target.result;
        resolve(this.db);
      };
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  // === وظائف الحسابات ===

  // حفظ جميع الحسابات
  async saveAllAccounts(accounts) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('accounts', 'readwrite');
      const store = tx.objectStore('accounts');

      // مسح الحسابات الموجودة أولاً
      const clearReq = store.clear();
      clearReq.onsuccess = () => {
        // إضافة الحسابات الجديدة
        let completed = 0;
        const total = accounts.length;

        if (total === 0) {
          resolve(true);
          return;
        }

        accounts.forEach(account => {
          const addReq = store.add(account);
          addReq.onsuccess = () => {
            completed++;
            if (completed === total) {
              resolve(true);
            }
          };
          addReq.onerror = (e) => reject(e.target.error);
        });
      };
      clearReq.onerror = (e) => reject(e.target.error);
    });
  }

  // جلب جميع الحسابات
  async getAllAccounts() {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('accounts', 'readonly');
      const store = tx.objectStore('accounts');
      const req = store.getAll();
      req.onsuccess = (e) => resolve(e.target.result);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // === وظائف القيود اليومية ===

  // إضافة أو تحديث قيد يومي
  async addOrUpdateJournalEntry(entry) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readwrite');
      const store = tx.objectStore('journalEntries');
      const req = store.put(entry); // put يضيف أو يحدث
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // إضافة قيد يومي جديد
  async addJournalEntry(entry) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readwrite');
      const store = tx.objectStore('journalEntries');
      const req = store.add(entry);
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // تحديث قيد يومي
  async updateJournalEntry(entry) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readwrite');
      const store = tx.objectStore('journalEntries');
      const req = store.put(entry);
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // حذف قيد يومي
  async deleteJournalEntry(id) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readwrite');
      const store = tx.objectStore('journalEntries');
      const req = store.delete(id);
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // جلب قيد يومي واحد
  async getJournalEntry(id) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readonly');
      const store = tx.objectStore('journalEntries');
      const req = store.get(id);
      req.onsuccess = (e) => resolve(e.target.result);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // جلب جميع قيود اليومية
  async getAllJournalEntries() {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('journalEntries', 'readonly');
      const store = tx.objectStore('journalEntries');
      const req = store.getAll();
      req.onsuccess = (e) => resolve(e.target.result);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  // دعم إعدادات الشركة في DataStore
  async addOrUpdateCompanySettings(settings) {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('companySettings', 'readwrite');
      const store = tx.objectStore('companySettings');
      const req = store.put({ ...settings, id: 'main' });
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  async getCompanySettings() {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('companySettings', 'readonly');
      const store = tx.objectStore('companySettings');
      const req = store.get('main');
      req.onsuccess = (e) => resolve(e.target.result);
      req.onerror = (e) => reject(e.target.error);
    });
  }

  async deleteCompanySettings() {
    const db = await this.open();
    return new Promise((resolve, reject) => {
      const tx = db.transaction('companySettings', 'readwrite');
      const store = tx.objectStore('companySettings');
      const req = store.delete('main');
      req.onsuccess = () => resolve(true);
      req.onerror = (e) => reject(e.target.error);
    });
  }
}

// مثال استخدام (للتوثيق فقط، لا تضعه في الإنتاج):
// const store = new DataStore();
// await store.addJournalEntry({id: 'JE001', date: '2025-07-02', description: '...', lines: [...] });
// const all = await store.getAllJournalEntries();
// ...
// ... existing code ... 