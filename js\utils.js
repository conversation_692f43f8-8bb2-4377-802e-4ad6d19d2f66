/**
 * Utils Module - الدوال المساعدة
 * ========================================
 */

/**
 * دالة مساعدة لتنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0.00';
    }
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatDate(dateString) {
    if (!dateString) return '';
    try {
        return new Date(dateString).toLocaleDateString('ar-SA');
    } catch (error) {
        return dateString;
    }
}

/**
 * دالة مساعدة لعرض رسائل التأكيد
 */
function showConfirm(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * دالة مساعدة لعرض رسائل النجاح
 */
function showSuccess(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert(message);
}

/**
 * دالة مساعدة لعرض رسائل الخطأ
 */
function showError(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert('خطأ: ' + message);
}

/**
 * دالة مساعدة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * دالة مساعدة للتحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
}

/**
 * دالة مساعدة لتنظيف النص من الأحرف الخاصة
 */
function sanitizeText(text) {
    if (typeof text !== 'string') return '';
    return text.replace(/[<>]/g, '');
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان URL صالح
 */
function slugify(text) {
    if (typeof text !== 'string') return '';
    return text
        .toLowerCase()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

/**
 * دالة مساعدة لإنشاء معرف فريد
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * دالة مساعدة لنسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccess('تم نسخ النص بنجاح');
        }).catch(() => {
            showError('فشل في نسخ النص');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('تم نسخ النص بنجاح');
        } catch (err) {
            showError('فشل في نسخ النص');
        }
        document.body.removeChild(textArea);
    }
}

/**
 * دالة مساعدة لتحميل ملف
 */
function downloadFile(content, filename, contentType = 'text/plain') {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

/**
 * دالة مساعدة لتحويل التاريخ إلى نص عربي
 */
function formatDateArabic(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const day = d.getDate();
    const month = months[d.getMonth()];
    const year = d.getFullYear();
    
    return `${day} ${month} ${year}`;
}

/**
 * دالة مساعدة لتحويل الأرقام إلى كلمات عربية
 */
function numberToArabicWords(number) {
    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', 'عشرة', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const hundreds = ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    
    if (number === 0) return 'صفر';
    if (number < 10) return ones[number];
    if (number < 100) {
        if (number < 20) {
            return number === 11 ? 'أحد عشر' : number === 12 ? 'اثنا عشر' : ones[number - 10] + ' عشر';
        }
        return ones[Math.floor(number / 10)] + ' و ' + tens[Math.floor(number / 10)];
    }
    if (number < 1000) {
        return hundreds[Math.floor(number / 100)] + ' و ' + numberToArabicWords(number % 100);
    }
    
    return number.toString();
}

/**
 * دالة مساعدة لتحويل العملة
 */
function convertCurrency(amount, fromCurrency, toCurrency) {
    // هنا يمكن إضافة API لأسعار الصرف
    // حالياً نرجع نفس المبلغ
    return amount;
}

/**
 * دالة مساعدة لتحويل الحجم
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * دالة مساعدة لتحويل المدة الزمنية
 */
function formatDuration(seconds) {
    if (seconds < 60) return `${seconds} ثانية`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} دقيقة`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} ساعة`;
    return `${Math.floor(seconds / 86400)} يوم`;
}

/**
 * دالة مساعدة لتحويل النسبة المئوية
 */
function formatPercentage(value, total) {
    if (total === 0) return '0%';
    const percentage = (value / total) * 100;
    return percentage.toFixed(1) + '%';
}

/**
 * دالة مساعدة لتحويل HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * دالة مساعدة لتحويل HTML
 */
function unescapeHtml(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent;
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان
 */
function capitalizeWords(text) {
    return text.replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * دالة مساعدة لتحويل النص إلى جملة
 */
function toSentenceCase(text) {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

/**
 * دالة مساعدة لتحويل النص إلى عنوان URL
 */
function toUrlFriendly(text) {
    return text
        .toLowerCase()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

/**
 * دالة مساعدة لتحويل النص إلى كلمات
 */
function toWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0);
}

/**
 * دالة مساعدة لتحويل النص إلى أحرف
 */
function toCharacters(text) {
    return text.split('');
}

/**
 * دالة مساعدة لتحويل النص إلى أسطر
 */
function toLines(text) {
    return text.split('\n');
}

/**
 * دالة مساعدة لتحويل النص إلى فقرات
 */
function toParagraphs(text) {
    return text.split('\n\n');
}

/**
 * دالة مساعدة لتحويل النص إلى جمل
 */
function toSentences(text) {
    return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
}

/**
 * دالة مساعدة لحساب عدد الكلمات
 */
function toWordCount(text) {
    return toWords(text).length;
}

/**
 * دالة مساعدة لحساب عدد الأحرف
 */
function toCharCount(text) {
    return text.length;
}

/**
 * دالة مساعدة لحساب عدد الأسطر
 */
function toLineCount(text) {
    return toLines(text).length;
}

/**
 * دالة مساعدة لحساب عدد الفقرات
 */
function toParagraphCount(text) {
    return toParagraphs(text).length;
}

/**
 * دالة مساعدة لحساب عدد الجمل
 */
function toSentenceCount(text) {
    return toSentences(text).length;
}

/**
 * دالة مساعدة لتنسيق الأرقام بالإنجليزية
 */
function formatNumberEnglish(number, decimals = 2) {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0.00';
    }
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * دالة مساعدة لتنسيق التاريخ الميلادي
 */
function formatDateGregorian(dateString) {
    if (!dateString) return '';
    try {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return dateString;
    }
}

/**
 * دالة مساعدة لتنسيق الوقت
 */
function formatTime(date) {
    if (!date) return '';
    try {
        return new Date(date).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return '';
    }
}

/**
 * دالة مساعدة لتنسيق التاريخ والوقت
 */
function formatDateTime(date) {
    return formatDate(date) + ' ' + formatTime(date);
}

/**
 * دالة مساعدة للحصول على التاريخ والوقت الحالي
 */
function getCurrentDateTime() {
    const now = new Date();
    return {
        date: now.toISOString().split('T')[0],
        time: now.toTimeString().split(' ')[0],
        datetime: now.toISOString(),
        timestamp: now.getTime()
    };
}

/**
 * دالة مساعدة لتحديث تنسيق العملة
 */
function updateCurrencyFormat(currencyCode) {
    const currencyFormats = {
        'SAR': { symbol: 'ر.س', position: 'right', decimal: 2 },
        'USD': { symbol: '$', position: 'left', decimal: 2 },
        'EUR': { symbol: '€', position: 'left', decimal: 2 },
        'GBP': { symbol: '£', position: 'left', decimal: 2 },
        'AED': { symbol: 'د.إ', position: 'right', decimal: 2 },
        'KWD': { symbol: 'د.ك', position: 'right', decimal: 3 },
        'QAR': { symbol: 'ر.ق', position: 'right', decimal: 2 },
        'BHD': { symbol: 'د.ب', position: 'right', decimal: 3 },
        'OMR': { symbol: 'ر.ع', position: 'right', decimal: 3 }
    };
    
    return currencyFormats[currencyCode] || currencyFormats['SAR'];
}

/**
 * دالة مساعدة لتنسيق العملة
 */
function formatCurrency(amount, currencyCode = null) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00';
    }
    
    // الحصول على تنسيق العملة
    let format = updateCurrencyFormat(currencyCode || 'SAR');
    
    // تنسيق الرقم
    const formattedNumber = new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: format.decimal,
        maximumFractionDigits: format.decimal
    }).format(amount);
    
    // إضافة رمز العملة
    if (format.position === 'right') {
        return formattedNumber + ' ' + format.symbol;
    } else {
        return format.symbol + ' ' + formattedNumber;
    }
}

/**
 * دالة مساعدة للحصول على بيانات الشركة من SQLite
 */
async function getCompanyData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const settings = await sqliteDB.getCompanySettings();
            return settings || {};
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
            return {};
        }
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات الشركة:', error);
        return {};
    }
}

/**
 * دالة مساعدة لاختبار حفظ بيانات الشركة
 */
async function testCompanyDataSave() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const testData = {
                name: 'شركة تجريبية',
                address: 'عنوان تجريبي',
                phone: '*********',
                email: '<EMAIL>',
                website: 'www.test.com',
                taxNumber: '*********',
                currency: 'SAR',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                fiscalYearStart: '01-01',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            await sqliteDB.saveCompanySettings(testData);
            console.log('✅ تم حفظ بيانات الشركة التجريبية بنجاح');
            return true;
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ بيانات الشركة:', error);
        return false;
    }
}

/**
 * دالة مساعدة لمسح بيانات الشركة
 */
async function clearCompanyData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.deleteCompanySettings();
            console.log('✅ تم مسح بيانات الشركة بنجاح');
            return true;
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في مسح بيانات الشركة:', error);
        return false;
    }
}

/**
 * دالة مساعدة لاختبار نظام بيانات الشركة
 */
async function testCompanyDataSystem() {
    console.log('🧪 بدء اختبار نظام بيانات الشركة...');
    
    try {
        // اختبار الحفظ
        const saveResult = await testCompanyDataSave();
        if (!saveResult) {
            console.error('❌ فشل في اختبار الحفظ');
            return false;
        }
        
        // اختبار القراءة
        const companyData = await getCompanyData();
        if (!companyData || !companyData.name) {
            console.error('❌ فشل في اختبار القراءة');
            return false;
        }
        
        console.log('📋 بيانات الشركة المحفوظة:', companyData);
        
        // اختبار المسح
        const clearResult = await clearCompanyData();
        if (!clearResult) {
            console.error('❌ فشل في اختبار المسح');
            return false;
        }
        
        // التحقق من المسح
        const emptyData = await getCompanyData();
        if (emptyData && Object.keys(emptyData).length > 0) {
            console.error('❌ فشل في التحقق من المسح');
            return false;
        }
        
        console.log('✅ تم اجتياز جميع اختبارات نظام بيانات الشركة');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام بيانات الشركة:', error);
        return false;
    }
}

/**
 * دالة مساعدة للتحقق من حالة بيانات الشركة
 */
async function checkCompanyDataStatus() {
    try {
        const companyData = await getCompanyData();
        
        if (!companyData || Object.keys(companyData).length === 0) {
            console.log('📋 لا توجد بيانات شركة محفوظة');
            return 'empty';
        } else {
            console.log('📋 بيانات الشركة موجودة:', companyData.name);
            return 'exists';
        }
    } catch (error) {
        console.error('❌ خطأ في التحقق من حالة بيانات الشركة:', error);
        return 'error';
    }
}

/**
 * دالة مساعدة لمسح جميع البيانات التجريبية
 */
async function clearAllTestData() {
    console.log('🧹 بدء مسح جميع البيانات التجريبية...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // مسح بيانات الشركة
            await sqliteDB.deleteCompanySettings();
            
            // مسح الحسابات
            await sqliteDB.clearAccounts();
            
            // مسح القيود اليومية
            await sqliteDB.clearJournalEntries();
            
            // مسح الإشعارات
            await sqliteDB.clearNotifications();
            
            // مسح المستخدمين
            await sqliteDB.clearUsers();
            
            // مسح الصلاحيات
            await sqliteDB.clearPermissions();
            
            console.log('✅ تم مسح جميع البيانات التجريبية بنجاح');
            return true;
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في مسح البيانات التجريبية:', error);
        return false;
    }
}

/**
 * دالة مساعدة لإنشاء بيانات تجريبية نظيفة
 */
async function createCleanTestData() {
    console.log('🧪 بدء إنشاء بيانات تجريبية نظيفة...');
    
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // إنشاء بيانات شركة تجريبية
            const companyData = {
                name: 'شركة زجاج التجارية',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '966-11-1234567',
                email: '<EMAIL>',
                website: 'www.glass-company.com',
                taxNumber: '300*********',
                currency: 'SAR',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                fiscalYearStart: '01-01',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            await sqliteDB.saveCompanySettings(companyData);
            console.log('✅ تم إنشاء بيانات تجريبية نظيفة بنجاح');
            return true;
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
        return false;
    }
} 