/**
 * Glass ERP System - Main JavaScript
 * الوظائف الرئيسية للنظام
 * ========================================
 */

// متغيرات عامة للنظام
let currentModule = 'dashboard';
let isInitialized = false;

/**
 * التحسينات الجديدة للنظام
 * ========================================
 */

// متغيرات التحسينات
let sidebarVisible = true;
let lastLoginTime = null;

/**
 * تهيئة النظام عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة نظام Glass ERP...');
    
    // تهيئة التنقل
    initializeNavigation();
    
    // تهيئة الأحداث العامة
    initializeGlobalEvents();
    
    // تحميل البيانات المحفوظة
    loadSystemData();
    
    // تحديث الواجهة
    updateUI();
    
    // انتظار تحميل الترجمة أولاً
    setTimeout(() => {
        initializeEnhancements();
    }, 100);
    
    isInitialized = true;
    console.log('✅ تم تهيئة النظام بنجاح');
});

/**
 * تهيئة نظام التنقل بين الموديولات
 */
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const modules = document.querySelectorAll('.module');
    const pageTitle = document.getElementById('page-title');
    
    navItems.forEach(item => {
        item.addEventListener('click', async function(e) {
            e.preventDefault();

            const moduleName = this.dataset.module;
            if (moduleName === currentModule) return;

            // إزالة الفئة النشطة من جميع العناصر
            navItems.forEach(i => i.classList.remove('active'));
            modules.forEach(m => m.classList.remove('active'));

            // إضافة الفئة النشطة للعنصر المحدد
            this.classList.add('active');
            document.getElementById(moduleName).classList.add('active');

            // تحديث عنوان الصفحة
            const linkText = this.querySelector('.nav-link span').textContent;
            pageTitle.textContent = linkText;

            // تحديث الموديول الحالي
            currentModule = moduleName;

            // تشغيل وظائف خاصة بالموديول
            await handleModuleChange(moduleName);

            console.log(`📱 تم الانتقال إلى موديول: ${moduleName}`);
        });
    });
}

/**
 * معالجة تغيير الموديول
 */
async function handleModuleChange(moduleName) {
    switch(moduleName) {
        case 'dashboard':
            if (typeof initializeDashboardEnhanced === 'function') {
                initializeDashboardEnhanced();
            } else if (typeof initializeDashboard === 'function') {
                initializeDashboard();
            } else if (typeof updateDashboard === 'function') {
                updateDashboard();
            }
            break;
        case 'accounts':
            if (typeof initializeAccounts === 'function') {
                await initializeAccounts();
            } else if (typeof showAccountTree === 'function') {
                showAccountTree();
            }
            break;
        case 'settings':
            if (typeof initializeSettings === 'function') {
                initializeSettings();
            } else if (typeof loadSettings === 'function') {
                loadSettings();
            }
            break;
        default:
            console.log(`📋 موديول ${moduleName} جاهز`);
    }
}

/**
 * تهيئة الأحداث العامة
 */
function initializeGlobalEvents() {
    // إغلاق النوافذ المنبثقة عند النقر خارجها
    window.addEventListener('click', function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // إغلاق النوافذ المنبثقة عند النقر على X
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    // معالجة مفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (modal.style.display === 'block') {
                    modal.style.display = 'none';
                }
            });
        }
    });
    
    // حفظ البيانات عند إغلاق الصفحة
    window.addEventListener('beforeunload', function (e) {
        // حفظ جميع بيانات النظام تلقائياً عند الخروج أو تحديث الصفحة
        if (typeof saveSystemData === 'function') {
            saveSystemData();
        }
    });
}

/**
 * تحميل بيانات النظام
 */
function loadSystemData() {
    try {
        console.log('📊 بدء تحميل بيانات النظام...');
        
        // تحميل إعدادات الشركة
        if (typeof loadSettings === 'function') {
            loadSettings();
            console.log('⚙️ تم تحميل إعدادات الشركة');
        }
        
        // تطبيق الإعدادات المحفوظة
        applySavedSettings();
        
        // ملاحظة: تم نقل تحميل بيانات الحسابات والقيود إلى accounts.js
        // حيث يتم تهيئة DataStore بشكل صحيح قبل تحميل البيانات
        
        // تحميل إعدادات النظام
        const systemSettingsData = localStorage.getItem('glassERP_systemSettings');
        if (systemSettingsData) {
            try {
                const systemSettings = JSON.parse(systemSettingsData);
                currentModule = systemSettings.currentModule || 'dashboard';
                sidebarVisible = systemSettings.sidebarVisible !== undefined ? systemSettings.sidebarVisible : true;
                lastLoginTime = systemSettings.lastLoginTime;
                console.log('⚙️ تم تحميل إعدادات النظام');
            } catch (error) {
                console.warn('⚠️ خطأ في تحميل إعدادات النظام:', error);
            }
        }
        
        console.log('✅ تم تحميل جميع بيانات النظام بنجاح');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات النظام:', error);
        return false;
    }
}

/**
 * تطبيق الإعدادات المحفوظة
 */
function applySavedSettings() {
    try {
        const savedSettings = localStorage.getItem('glassERP_settings');
        if (!savedSettings) {
            console.log('⚠️ لا توجد إعدادات محفوظة');
            return;
        }
        
        const settings = JSON.parse(savedSettings);
        console.log('📋 الإعدادات المحفوظة:', settings);
        
        // تطبيق العملة
        if (settings.currency && typeof updateCurrencyFormat === 'function') {
            updateCurrencyFormat(settings.currency);
            console.log('💰 تم تطبيق العملة:', settings.currency);
        }
        
        // تطبيق معلومات الشركة
        if (settings.name && typeof updateCompanyInfo === 'function') {
            updateCompanyInfo();
            console.log('🏢 تم تطبيق معلومات الشركة');
        }
        
        console.log('⚙️ تم تطبيق الإعدادات المحفوظة بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تطبيق الإعدادات المحفوظة:', error);
    }
}

/**
 * تحديث واجهة المستخدم
 */
function updateUI() {
    // تحديث الإشعارات
    updateNotifications();
    
    // تحديث معلومات المستخدم
    updateUserInfo();
    
    // تحديث التاريخ والوقت
    updateDateTime();
}

/**
 * تحديث الإشعارات
 */
function updateNotifications() {
    const notificationBadge = document.querySelector('.notification-badge');
    if (notificationBadge) {
        // يمكن إضافة منطق لحساب عدد الإشعارات
        const notificationCount = 3; // مثال
        notificationBadge.textContent = notificationCount;
        
        if (notificationCount === 0) {
            notificationBadge.style.display = 'none';
        } else {
            notificationBadge.style.display = 'flex';
        }
    }
}

/**
 * تحديث معلومات المستخدم
 */
function updateUserInfo() {
    const userProfile = document.querySelector('.user-profile span');
    if (userProfile) {
        // يمكن جلب اسم المستخدم من localStorage
        const userName = localStorage.getItem('glassERP_userName') || 'المدير';
        userProfile.textContent = userName;
    }
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    const now = new Date();
    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // يمكن إضافة عنصر لعرض التاريخ في الواجهة
    const dateElement = document.getElementById('current-date');
    if (dateElement) {
        dateElement.textContent = dateString;
    }
}

/**
 * حفظ بيانات النظام
 */
function saveSystemData() {
    try {
        console.log('💾 بدء حفظ بيانات النظام فوراً...');
        
        // حفظ إعدادات الشركة
        if (typeof saveSettingsAuto === 'function') {
            const settingsSaved = saveSettingsAuto();
            console.log('⚙️ حفظ الإعدادات:', settingsSaved ? 'نجح' : 'فشل');
        }
        
        // حفظ بيانات الحسابات
        if (typeof saveAccountsData === 'function') {
            const accountsSaved = saveAccountsData();
            console.log('📊 حفظ الحسابات:', accountsSaved ? 'نجح' : 'فشل');
        }
        
        // حفظ القيود اليومية
        if (typeof saveJournalEntries === 'function') {
            const journalSaved = saveJournalEntries();
            console.log('📝 حفظ القيود اليومية:', journalSaved ? 'نجح' : 'فشل');
        }
        
        // حفظ إعدادات النظام
        const systemSettings = {
            lastSaveTime: new Date().toISOString(),
            currentModule: currentModule,
            sidebarVisible: sidebarVisible,
            lastLoginTime: lastLoginTime
        };
        localStorage.setItem('glassERP_systemSettings', JSON.stringify(systemSettings));
        
        // تأكيد الحفظ
        const savedSystemSettings = localStorage.getItem('glassERP_systemSettings');
        if (!savedSystemSettings) {
            console.error('❌ فشل في حفظ إعدادات النظام');
            return false;
        }
        
        console.log('✅ تم حفظ جميع بيانات النظام فوراً');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في حفظ بيانات النظام:', error);
        return false;
    }
}

/**
 * تصدير بيانات النظام
 */
function exportSystemData() {
    try {
        const data = {
            settings: localStorage.getItem('glassERP_settings'),
            accounts: localStorage.getItem('glassERP_accounts'),
            journalEntries: localStorage.getItem('glassERP_journalEntries'),
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `glass-erp-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        console.log('📤 تم تصدير بيانات النظام');
    } catch (error) {
        console.error('❌ خطأ في تصدير البيانات:', error);
    }
}

/**
 * استيراد بيانات النظام
 */
function importSystemData(file) {
    try {
        const reader = new FileReader();
        reader.onload = function(e) {
            const data = JSON.parse(e.target.result);
            
            // استيراد البيانات
            if (data.settings) {
                localStorage.setItem('glassERP_settings', data.settings);
            }
            if (data.accounts) {
                localStorage.setItem('glassERP_accounts', data.accounts);
            }
            if (data.journalEntries) {
                localStorage.setItem('glassERP_journalEntries', data.journalEntries);
            }
            
            // إعادة تحميل البيانات
            loadSystemData();
            
            // تحديث الواجهة
            updateUI();
            
            console.log('📥 تم استيراد بيانات النظام');
            alert('تم استيراد البيانات بنجاح');
        };
        reader.readAsText(file);
    } catch (error) {
        console.error('❌ خطأ في استيراد البيانات:', error);
        alert('خطأ في استيراد البيانات');
    }
}

/**
 * تنظيف بيانات النظام
 */
function clearSystemData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        try {
            // حذف جميع البيانات المحفوظة
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('glassERP_')) {
                    localStorage.removeItem(key);
                }
            });
            
            // إعادة تحميل الصفحة
            location.reload();
            
            console.log('🗑️ تم حذف جميع بيانات النظام');
        } catch (error) {
            console.error('❌ خطأ في حذف البيانات:', error);
        }
    }
}

/**
 * دالة مساعدة لتنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

/**
 * دالة مساعدة لعرض رسائل التأكيد
 */
function showConfirm(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * دالة مساعدة لعرض رسائل النجاح
 */
function showSuccess(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert(message);
}

/**
 * دالة مساعدة لعرض رسائل الخطأ
 */
function showError(message) {
    // يمكن إضافة مكتبة لعرض الإشعارات
    alert('خطأ: ' + message);
}

/**
 * تهيئة التحسينات الجديدة
 */
function initializeEnhancements() {
    // تهيئة زر إخفاء/إظهار القائمة الجانبية
    initializeSidebarToggle();
    

    
    // تهيئة الساعة والتاريخ
    initializeDateTime();
    
    // تحميل معلومات الشركة
    loadCompanyInfo();
    
    // تسجيل وقت الدخول
    recordLoginTime();
    
    console.log('✨ تم تهيئة التحسينات الجديدة');
}

/**
 * تهيئة زر إخفاء/إظهار القائمة الجانبية
 */
function initializeSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const main = document.querySelector('.main');
    
    if (sidebarToggle && sidebar && main) {
        sidebarToggle.addEventListener('click', function() {
            sidebarVisible = !sidebarVisible;
            
            if (sidebarVisible) {
                sidebar.classList.remove('collapsed');
                main.classList.remove('expanded');
                sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
            } else {
                sidebar.classList.add('collapsed');
                main.classList.add('expanded');
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
            
            // حفظ حالة القائمة الجانبية
            localStorage.setItem('glassERP_sidebarVisible', sidebarVisible);
        });
        
        // استعادة حالة القائمة الجانبية
        const savedState = localStorage.getItem('glassERP_sidebarVisible');
        if (savedState === 'false') {
            sidebarVisible = false;
            sidebar.classList.add('collapsed');
            main.classList.add('expanded');
            sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
        }
    }
}



/**
 * تنسيق الوقت
 */
function formatTime(date) {
    return date.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime(date) {
    return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
}

/**
 * تهيئة الساعة والتاريخ
 */
function initializeDateTime() {
    const currentTimeElement = document.getElementById('current-time');
    
    if (currentTimeElement) {
        // تحديث الوقت كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = formatTime(now);
            currentTimeElement.textContent = timeString;
        }
        
        // تحديث فوري
        updateTime();
        
        // تحديث كل ثانية
        setInterval(updateTime, 1000);
        
        console.log('⏰ تم تهيئة الساعة والتاريخ');
    } else {
        console.error('❌ لم يتم العثور على عنصر الوقت');
    }
}

/**
 * تحميل معلومات الشركة
 */
function loadCompanyInfo() {
    const companyNameElement = document.getElementById('company-name');
    if (companyNameElement) {
        try {
            // جلب اسم الشركة من الإعدادات
            const savedSettings = localStorage.getItem('glassERP_settings');
            if (!savedSettings) {
                console.log('⚠️ لا توجد إعدادات محفوظة للشركة');
                companyNameElement.textContent = '';
                return;
            }
            const settings = JSON.parse(savedSettings);
            const companyName = settings.name || '';
            companyNameElement.textContent = companyName;
            console.log('🏢 تم تحميل معلومات الشركة:', companyName);
        } catch (error) {
            console.error('❌ خطأ في تحميل معلومات الشركة:', error);
            companyNameElement.textContent = '';
        }
    } else {
        console.error('❌ لم يتم العثور على عنصر اسم الشركة');
    }
}

/**
 * تسجيل وقت الدخول
 */
function recordLoginTime() {
    const now = new Date();
    lastLoginTime = now;
    
    // حفظ وقت آخر دخول
    localStorage.setItem('glassERP_lastLogin', now.toISOString());
    
    console.log(`🕐 تم تسجيل وقت الدخول: ${formatDateTime(now)}`);
}

/**
 * الحصول على وقت آخر دخول
 */
function getLastLoginTime() {
    const lastLogin = localStorage.getItem('glassERP_lastLogin');
    return lastLogin ? new Date(lastLogin) : null;
}

/**
 * عرض معلومات الجلسة
 */
function showSessionInfo() {
    const lastLogin = getLastLoginTime();
    const currentTime = new Date();
    
    let message = `الوقت الحالي: ${formatDateTime(currentTime)}\n`;
    
    if (lastLogin) {
        const timeDiff = Math.floor((currentTime - lastLogin) / (1000 * 60)); // بالدقائق
        message += `آخر دخول: ${formatDateTime(lastLogin)}\n`;
        message += `مدة الجلسة: ${timeDiff} دقيقة`;
    }
    
    alert(message);
}

/**
 * تحديث معلومات الشركة في الواجهة
 */
function updateCompanyInfo() {
    const companyNameElement = document.getElementById('company-name');
    if (companyNameElement) {
        try {
            const savedSettings = localStorage.getItem('glassERP_settings');
            if (!savedSettings) {
                console.log('⚠️ لا توجد إعدادات محفوظة للشركة');
                companyNameElement.textContent = '';
                return;
            }
            const settings = JSON.parse(savedSettings);
            const companyName = settings.name || '';
            companyNameElement.textContent = companyName;
            console.log('🏢 تم تحديث معلومات الشركة في الواجهة:', companyName);
        } catch (error) {
            console.error('❌ خطأ في تحديث معلومات الشركة:', error);
            companyNameElement.textContent = '';
        }
    } else {
        console.error('❌ لم يتم العثور على عنصر اسم الشركة');
    }
}

// تمت إزالة المراقبة التلقائية (Proxy) لتقليل الثقل وتحسين الأداء
// سيتم الاعتماد على الحفظ اليدوي عند الضغط على زر "حفظ" أو عند انتهاء التعديل فقط

// تصدير الدوال للاستخدام في الملفات الأخرى
window.GlassERP = {
    formatNumber,
    formatDate,
    showConfirm,
    showSuccess,
    showError,
    exportSystemData,
    importSystemData,
    clearSystemData
};

// تصدير الدوال الجديدة
window.initializeEnhancements = initializeEnhancements;
window.showSessionInfo = showSessionInfo;
window.updateCompanyInfo = updateCompanyInfo;
window.getLastLoginTime = getLastLoginTime;