# تعليمات استخدام نظام Glass ERP

## الإصلاحات الجديدة - ديسمبر 2024

### ✅ تم إصلاح المشاكل التالية:

1. **مشكلة زر "إعدادات الشركة"**: الآن يعمل فوراً عند فتح التطبيق
2. **مشكلة الحفظ**: تم توحيد نظام الحفظ وإصلاح جميع المشاكل

---

## كيفية الاختبار السريع

### 1. اختبار زر الإعدادات
1. افتح التطبيق في المتصفح
2. اضغط مباشرة على "إعدادات الشركة" من القائمة الجانبية
3. يجب أن يفتح موديول الإعدادات فوراً

### 2. اختبار الحفظ
1. اذهب إلى "إعدادات الشركة"
2. أدخل بيانات الشركة (مثل: اسم الشركة، العنوان، الهاتف)
3. اضغط على "حفظ الإعدادات"
4. اضغط على "اختبار الحفظ" للتحقق من نجاح الحفظ

### 3. اختبار من وحدة التحكم (F12)
```javascript
// اختبار سريع للحفظ
testSave();

// اختبار سريع للتحميل
testLoad();

// اختبار شامل
TestModule.testPerformance();
```

---

## الميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات المبيعات والمشتريات
- رسوم بيانية تفاعلية
- معلومات الشركة المحدثة

### ⚙️ إعدادات الشركة
- حفظ معلومات الشركة الأساسية
- إعداد العملة الافتراضية
- رفع شعار الشركة
- حفظ تلقائي عند التغيير

### 📈 الحسابات
- شجرة الحسابات
- القيود اليومية
- تقارير مالية

### 🔔 الإشعارات
- إشعارات النظام
- تنبيهات مهمة
- رسائل الحالة

---

## دوال الاختبار المتاحة

### اختبارات سريعة:
```javascript
// اختبار الحفظ
testSave();

// اختبار التحميل
testLoad();

// اختبار شامل
TestModule.testPerformance();
```

### اختبارات متقدمة:
```javascript
// اختبار نظام الحفظ
TestModule.testSaveSystem();

// اختبار الأداء
TestModule.testPerformance();

// اختبار الرسوم البيانية
TestModule.testChartsPerformance();
```

---

## استكشاف الأخطاء

### إذا لم يعمل زر الإعدادات:
1. تأكد من تحميل الصفحة بالكامل
2. تحقق من وحدة التحكم (F12) للأخطاء
3. جرب تحديث الصفحة

### إذا لم يعمل الحفظ:
1. تأكد من إدخال اسم الشركة (مطلوب)
2. اضغط على "اختبار الحفظ" للتحقق
3. تحقق من وحدة التحكم للأخطاء

### إذا كانت الرسوم البيانية لا تظهر:
1. انتظر بضع ثوانٍ لتحميل البيانات
2. تحقق من اتصال الإنترنت (لتحميل Chart.js)
3. جرب تحديث الصفحة

---

## الملفات المهمة

### ملفات JavaScript:
- `js/main.js`: الوظائف الرئيسية للنظام
- `js/settings.js`: إعدادات الشركة والحفظ
- `js/dashboard.js`: لوحة التحكم والرسوم البيانية
- `js/accounts.js`: نظام الحسابات
- `js/test-performance.js`: دوال الاختبار

### ملفات CSS:
- `css/main.css`: التصميم الرئيسي
- `css/dashboard.css`: تصميم لوحة التحكم
- `css/settings.css`: تصميم الإعدادات
- `css/performance.css`: تحسينات الأداء

---

## معلومات تقنية

### المتصفحات المدعومة:
- Chrome (موصى به)
- Firefox
- Safari
- Edge

### المتطلبات:
- JavaScript مفعل
- اتصال إنترنت (لتحميل المكتبات الخارجية)
- مساحة تخزين محلية (localStorage)

### المفاتيح المستخدمة في التخزين:
- `glassERP_companySettings`: إعدادات الشركة
- `glassERP_accounts`: بيانات الحسابات
- `glassERP_journalEntries`: القيود اليومية
- `glassERP_systemSettings`: إعدادات النظام

---

## الدعم والمساعدة

إذا واجهت أي مشاكل:

1. **تحقق من وحدة التحكم**: افتح أدوات المطور (F12) وتحقق من وجود أخطاء
2. **اختبر الوظائف**: استخدم دوال الاختبار المدمجة
3. **أبلغ عن المشكلة**: وصف تفصيلي للمشكلة مع خطوات التكرار

---

*آخر تحديث: ديسمبر 2024* 