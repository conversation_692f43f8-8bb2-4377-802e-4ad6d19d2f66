/**
 * Accounts Module - موديول الحسابات الشامل
 * ========================================
 */

// متغيرات النظام المحاسبي
let accounts = [];
let journalEntries = [];
let currentFiscalYear = new Date().getFullYear();
let currentPeriod = new Date().getMonth() + 1;

// إنشاء مثيل DataStore للحسابات والقيود اليومية
let accountsDataStore = null;

// تهيئة DataStore
async function initializeAccountsDataStore() {
    try {
        if (typeof DataStore === 'undefined') {
            console.warn('⚠️ DataStore غير متاح، سيتم استخدام localStorage كبديل');
            return false;
        }

        accountsDataStore = new DataStore('GlassERP_Accounts', 1);
        await accountsDataStore.open();
        console.log('✅ تم تهيئة DataStore للحسابات بنجاح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في تهيئة DataStore للحسابات:', error);
        return false;
    }
}

// هيكل شجرة الحسابات الكاملة
const defaultChartOfAccounts = [
    // الأصول (1000)
    { id: '1000', name: 'الأصول', type: 'asset', parentId: null, level: 1, balance: 0, isActive: true },
    
    // الأصول المتداولة (1100)
    { id: '1100', name: 'الأصول المتداولة', type: 'asset', parentId: '1000', level: 2, balance: 0, isActive: true },
    
    // النقدية والبنوك (1110)
    { id: '1110', name: 'النقدية والبنوك', type: 'asset', parentId: '1100', level: 3, balance: 0, isActive: true },
    { id: '1111', name: 'البنك', type: 'asset', parentId: '1110', level: 4, balance: 0, isActive: true },
    { id: '1112', name: 'عهدة مدير', type: 'asset', parentId: '1110', level: 4, balance: 0, isActive: true },
    { id: '1113', name: 'عهدة موظفين', type: 'asset', parentId: '1110', level: 4, balance: 0, isActive: true },
    { id: '1114', name: 'الخزينة', type: 'asset', parentId: '1110', level: 4, balance: 0, isActive: true },
    
    // العملاء (1120)
    { id: '1120', name: 'العملاء', type: 'asset', parentId: '1100', level: 3, balance: 0, isActive: true },
    
    // المخزون (1130)
    { id: '1130', name: 'المخزون', type: 'asset', parentId: '1100', level: 3, balance: 0, isActive: true },
    { id: '1131', name: 'مخزون المواد الخام', type: 'asset', parentId: '1130', level: 4, balance: 0, isActive: true },
    { id: '1132', name: 'مخزون الإنتاج التام', type: 'asset', parentId: '1130', level: 4, balance: 0, isActive: true },
    { id: '1133', name: 'مخزون تحت التشغيل', type: 'asset', parentId: '1130', level: 4, balance: 0, isActive: true },
    
    // سلف موظفين (1140)
    { id: '1140', name: 'سلف موظفين', type: 'asset', parentId: '1100', level: 3, balance: 0, isActive: true },
    
    // الأصول الثابتة (1200)
    { id: '1200', name: 'الأصول الثابتة', type: 'asset', parentId: '1000', level: 2, balance: 0, isActive: true },
    { id: '1210', name: 'الأراضي والمباني', type: 'asset', parentId: '1200', level: 3, balance: 0, isActive: true },
    { id: '1220', name: 'المعدات والآلات', type: 'asset', parentId: '1200', level: 3, balance: 0, isActive: true },
    { id: '1230', name: 'وسائل النقل', type: 'asset', parentId: '1200', level: 3, balance: 0, isActive: true },
    
    // الخصوم (2000)
    { id: '2000', name: 'الخصوم', type: 'liability', parentId: null, level: 1, balance: 0, isActive: true },
    
    // الخصوم المتداولة (2100)
    { id: '2100', name: 'الخصوم المتداولة', type: 'liability', parentId: '2000', level: 2, balance: 0, isActive: true },
    { id: '2110', name: 'الموردين', type: 'liability', parentId: '2100', level: 3, balance: 0, isActive: true },
    { id: '2120', name: 'مصروفات مستحقة', type: 'liability', parentId: '2100', level: 3, balance: 0, isActive: true },
    { id: '2130', name: 'ضرائب مستحقة', type: 'liability', parentId: '2100', level: 3, balance: 0, isActive: true },
    { id: '2140', name: 'أجور مستحقة', type: 'liability', parentId: '2100', level: 3, balance: 0, isActive: true },
    
    // الخصوم طويلة الأجل (2200)
    { id: '2200', name: 'الخصوم طويلة الأجل', type: 'liability', parentId: '2000', level: 2, balance: 0, isActive: true },
    { id: '2210', name: 'قروض طويلة الأجل', type: 'liability', parentId: '2200', level: 3, balance: 0, isActive: true },
    
    // حقوق الملكية (3000)
    { id: '3000', name: 'حقوق الملكية', type: 'equity', parentId: null, level: 1, balance: 0, isActive: true },
    { id: '3100', name: 'رأس المال', type: 'equity', parentId: '3000', level: 2, balance: 0, isActive: true },
    { id: '3200', name: 'الأرباح المحتجزة', type: 'equity', parentId: '3000', level: 2, balance: 0, isActive: true },
    { id: '3300', name: 'أرباح العام الحالي', type: 'equity', parentId: '3000', level: 2, balance: 0, isActive: true },
    
    // الإيرادات (4000)
    { id: '4000', name: 'الإيرادات', type: 'revenue', parentId: null, level: 1, balance: 0, isActive: true },
    
    // إيرادات المبيعات (4100)
    { id: '4100', name: 'إيرادات المبيعات', type: 'revenue', parentId: '4000', level: 2, balance: 0, isActive: true },
    { id: '4110', name: 'مبيعات المنتجات', type: 'revenue', parentId: '4100', level: 3, balance: 0, isActive: true },
    { id: '4120', name: 'مبيعات الخدمات', type: 'revenue', parentId: '4100', level: 3, balance: 0, isActive: true },
    { id: '4130', name: 'مردودات المبيعات', type: 'revenue', parentId: '4100', level: 3, balance: 0, isActive: true },
    
    // إيرادات أخرى (4200)
    { id: '4200', name: 'إيرادات أخرى', type: 'revenue', parentId: '4000', level: 2, balance: 0, isActive: true },
    
    // المصروفات (5000)
    { id: '5000', name: 'المصروفات', type: 'expense', parentId: null, level: 1, balance: 0, isActive: true },
    
    // تكلفة البضاعة المباعة (5100)
    { id: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense', parentId: '5000', level: 2, balance: 0, isActive: true },
    
    // مصروفات التشغيل (5200)
    { id: '5200', name: 'مصروفات التشغيل', type: 'expense', parentId: '5000', level: 2, balance: 0, isActive: true },
    { id: '5210', name: 'مصروفات الرواتب', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5211', name: 'مصروفات المكافأة', type: 'expense', parentId: '5210', level: 4, balance: 0, isActive: true },
    { id: '5212', name: 'مصروفات الوقت الإضافي', type: 'expense', parentId: '5210', level: 4, balance: 0, isActive: true },
    { id: '5220', name: 'الإيجارات', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5230', name: 'الكهرباء والمياه', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5240', name: 'مصروفات الصيانة', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5250', name: 'مصروفات المشاريع', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5260', name: 'مصروفات عامة متنوعة', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5270', name: 'مصروفات غذائية', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5280', name: 'مصروفات المطعم', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    { id: '5290', name: 'مصروفات التسويق', type: 'expense', parentId: '5200', level: 3, balance: 0, isActive: true },
    
    // مصروفات إدارية (5300)
    { id: '5300', name: 'مصروفات إدارية', type: 'expense', parentId: '5000', level: 2, balance: 0, isActive: true }
];

/**
 * تهيئة موديول الحسابات
 */
async function initializeAccounts() {
    console.log('📊 تهيئة موديول الحسابات الشامل...');

    // تهيئة DataStore
    await initializeAccountsDataStore();

    // تحميل البيانات المحفوظة
    await loadAccountsData();

    // إنشاء واجهة الحسابات
    createAccountsInterface();

    console.log('✅ تم تهيئة موديول الحسابات بنجاح');
}

/**
 * تحميل بيانات الحسابات من IndexedDB أو localStorage
 */
async function loadAccountsData() {
    try {
        // محاولة تحميل من IndexedDB أولاً
        if (accountsDataStore) {
            const accountsFromDB = await accountsDataStore.getAllAccounts();
            if (accountsFromDB && accountsFromDB.length > 0) {
                accounts = accountsFromDB;
                console.log('✅ تم تحميل الحسابات من IndexedDB:', accounts.length, 'حساب');
            } else {
                // تحميل من localStorage كبديل
                const data = localStorage.getItem('glassERP_accounts');
                accounts = data ? JSON.parse(data) : [];
                console.log('📦 تم تحميل الحسابات من localStorage:', accounts.length, 'حساب');
            }
        } else {
            // استخدام localStorage فقط
            const data = localStorage.getItem('glassERP_accounts');
            accounts = data ? JSON.parse(data) : [];
            console.log('📦 تم تحميل الحسابات من localStorage:', accounts.length, 'حساب');
        }

        // تحميل القيود اليومية
        await loadJournalEntriesData();

        // إذا كانت الحسابات فارغة، قم بإنشاء الشجرة الافتراضية
        if (accounts.length === 0) {
            accounts = createDefaultChartOfAccounts();
            await saveAccountsData();
            console.log('✅ تم إنشاء شجرة الحسابات الافتراضية');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات الحسابات:', error);
        // استخدام localStorage كبديل في حالة الخطأ
        const data = localStorage.getItem('glassERP_accounts');
        accounts = data ? JSON.parse(data) : [];
    }
}

/**
 * إنشاء شجرة الحسابات الافتراضية
 */
function createDefaultChartOfAccounts() {
    return [
        // الأصول (1000)
        { id: '1000', name: 'الأصول', type: 'header', parentId: null },
        
        // الأصول المتداولة (1100)
        { id: '1100', name: 'الأصول المتداولة', type: 'header', parentId: '1000' },
        
        // النقدية والبنوك (1110)
        { id: '1110', name: 'النقدية والبنوك', type: 'header', parentId: '1100' },
        { id: '1111', name: 'النقدية', type: 'asset', parentId: '1110' },
        { id: '1112', name: 'البنك', type: 'asset', parentId: '1110' },
        { id: '1113', name: 'عهدة مدير', type: 'asset', parentId: '1110' },
        { id: '1114', name: 'عهدة موظفين', type: 'asset', parentId: '1110' },
        { id: '1115', name: 'عهدة مدير مسؤولين', type: 'asset', parentId: '1110' },
        
        // العملاء (1120)
        { id: '1120', name: 'العملاء', type: 'asset', parentId: '1100' },
        
        // المخزون (1130)
        { id: '1130', name: 'المخزون', type: 'header', parentId: '1100' },
        { id: '1131', name: 'مخزون المواد الخام', type: 'asset', parentId: '1130' },
        { id: '1132', name: 'مخزون الإنتاج التام', type: 'asset', parentId: '1130' },
        { id: '1133', name: 'مخزون تحت التشغيل', type: 'asset', parentId: '1130' },
        
        // سلف موظفين (1140)
        { id: '1140', name: 'سلف موظفين', type: 'asset', parentId: '1100' },
        
        // الأصول الثابتة (1200)
        { id: '1200', name: 'الأصول الثابتة', type: 'header', parentId: '1000' },
        { id: '1210', name: 'الأراضي والمباني', type: 'asset', parentId: '1200' },
        { id: '1220', name: 'المعدات والآلات', type: 'asset', parentId: '1200' },
        { id: '1230', name: 'وسائل النقل', type: 'asset', parentId: '1200' },
        
        // الخصوم (2000)
        { id: '2000', name: 'الخصوم', type: 'header', parentId: null },
        
        // الخصوم المتداولة (2100)
        { id: '2100', name: 'الخصوم المتداولة', type: 'header', parentId: '2000' },
        { id: '2110', name: 'الموردين', type: 'liability', parentId: '2100' },
        { id: '2120', name: 'مصروفات مستحقة', type: 'liability', parentId: '2100' },
        { id: '2130', name: 'ضرائب مستحقة', type: 'liability', parentId: '2100' },
        { id: '2140', name: 'أجور مستحقة', type: 'liability', parentId: '2100' },
        
        // الخصوم طويلة الأجل (2200)
        { id: '2200', name: 'الخصوم طويلة الأجل', type: 'header', parentId: '2000' },
        { id: '2210', name: 'قروض طويلة الأجل', type: 'liability', parentId: '2200' },
        
        // حقوق الملكية (3000)
        { id: '3000', name: 'حقوق الملكية', type: 'header', parentId: null },
        { id: '3100', name: 'رأس المال', type: 'equity', parentId: '3000' },
        { id: '3200', name: 'الأرباح المحتجزة', type: 'equity', parentId: '3000' },
        { id: '3300', name: 'أرباح العام الحالي', type: 'equity', parentId: '3000' },
        
        // الإيرادات (4000)
        { id: '4000', name: 'الإيرادات', type: 'header', parentId: null },
        
        // إيرادات المبيعات (4100)
        { id: '4100', name: 'إيرادات المبيعات', type: 'header', parentId: '4000' },
        { id: '4110', name: 'مبيعات المنتجات', type: 'revenue', parentId: '4100' },
        { id: '4120', name: 'مبيعات الخدمات', type: 'revenue', parentId: '4100' },
        { id: '4130', name: 'مردودات المبيعات', type: 'revenue', parentId: '4100' },
        
        // إيرادات أخرى (4200)
        { id: '4200', name: 'إيرادات أخرى', type: 'revenue', parentId: '4000' },
        
        // المصروفات (5000)
        { id: '5000', name: 'المصروفات', type: 'header', parentId: null },
        
        // تكلفة البضاعة المباعة (5100)
        { id: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense', parentId: '5000' },
        
        // مصروفات التشغيل (5200)
        { id: '5200', name: 'مصروفات التشغيل', type: 'header', parentId: '5000' },
        { id: '5210', name: 'مصروفات الرواتب', type: 'expense', parentId: '5200' },
        { id: '5211', name: 'مصروفات المكافأة', type: 'expense', parentId: '5200' },
        { id: '5212', name: 'مصروفات الوقت الإضافي', type: 'expense', parentId: '5200' },
        { id: '5220', name: 'الإيجارات', type: 'expense', parentId: '5200' },
        { id: '5230', name: 'الكهرباء والمياه', type: 'expense', parentId: '5200' },
        { id: '5240', name: 'مصروفات الصيانة', type: 'expense', parentId: '5200' },
        { id: '5250', name: 'مصروفات المشاريع', type: 'expense', parentId: '5200' },
        { id: '5260', name: 'مصروفات عامة متنوعة', type: 'expense', parentId: '5200' },
        { id: '5270', name: 'مصروفات غذائية', type: 'expense', parentId: '5200' },
        { id: '5280', name: 'مصروفات المطعم', type: 'expense', parentId: '5200' },
        { id: '5290', name: 'مصروفات التسويق', type: 'expense', parentId: '5200' },
        
        // مصروفات إدارية (5300)
        { id: '5300', name: 'مصروفات إدارية', type: 'header', parentId: '5000' },
        { id: '5310', name: 'مصروفات غذائية', type: 'expense', parentId: '5300' },
        { id: '5320', name: 'مصروفات مطعم', type: 'expense', parentId: '5300' },
        { id: '5330', name: 'مصروفات شخصية', type: 'expense', parentId: '5300' },
        { id: '5340', name: 'مصروفات خاصة', type: 'expense', parentId: '5300' }
    ];
}

/**
 * حفظ بيانات الحسابات في IndexedDB و localStorage
 */
async function saveAccountsData() {
    try {
        // التحقق من صحة البيانات
        if (!Array.isArray(accounts)) {
            console.error('❌ بيانات الحسابات غير صحيحة');
            return false;
        }

        let savedToIndexedDB = false;

        // محاولة الحفظ في IndexedDB أولاً
        if (accountsDataStore) {
            try {
                await accountsDataStore.saveAllAccounts(accounts);
                savedToIndexedDB = true;
                console.log('💾 تم حفظ بيانات الحسابات في IndexedDB:', accounts.length, 'حساب');
            } catch (error) {
                console.warn('⚠️ فشل في حفظ الحسابات في IndexedDB:', error);
            }
        }

        // حفظ في localStorage كنسخة احتياطية
        localStorage.setItem('glassERP_accounts', JSON.stringify(accounts));

        // تأكيد الحفظ
        const savedData = localStorage.getItem('glassERP_accounts');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            console.log('💾 تم حفظ بيانات الحسابات في localStorage:', parsedData.length, 'حساب');
            return true;
        } else {
            console.error('❌ فشل في حفظ بيانات الحسابات');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ بيانات الحسابات:', error);
        showSimpleNotification('خطأ في حفظ بيانات الحسابات', 'error');
        return false;
    }
}

/**
 * تحميل بيانات القيود اليومية من IndexedDB أو localStorage
 */
async function loadJournalEntriesData() {
    try {
        // محاولة تحميل من IndexedDB أولاً
        if (accountsDataStore) {
            try {
                const entriesFromDB = await accountsDataStore.getAllJournalEntries();
                if (entriesFromDB && entriesFromDB.length > 0) {
                    journalEntries = entriesFromDB;
                    console.log('✅ تم تحميل القيود اليومية من IndexedDB:', journalEntries.length, 'قيد');
                    return true;
                }
            } catch (error) {
                console.warn('⚠️ فشل في تحميل القيود اليومية من IndexedDB:', error);
            }
        }

        // تحميل من localStorage كبديل
        const data = localStorage.getItem('glassERP_journalEntries');
        if (data) {
            journalEntries = JSON.parse(data);
            console.log('📦 تم تحميل القيود اليومية من localStorage:', journalEntries.length, 'قيد');
            return true;
        } else {
            journalEntries = [];
            console.log('⚠️ لا توجد قيود يومية محفوظة، تم إنشاء مصفوفة فارغة');
            return true;
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل القيود اليومية:', error);
        journalEntries = [];
        return false;
    }
}

/**
 * حفظ بيانات القيود اليومية في IndexedDB و localStorage
 */
async function saveJournalEntries() {
    try {
        // التحقق من صحة البيانات
        if (!Array.isArray(journalEntries)) {
            console.error('❌ بيانات القيود اليومية غير صحيحة');
            return false;
        }

        // محاولة الحفظ في IndexedDB أولاً
        if (accountsDataStore) {
            try {
                // حفظ كل قيد على حدة في IndexedDB
                for (const entry of journalEntries) {
                    await accountsDataStore.addOrUpdateJournalEntry(entry);
                }
                console.log('💾 تم حفظ القيود اليومية في IndexedDB:', journalEntries.length, 'قيد');
            } catch (error) {
                console.warn('⚠️ فشل في حفظ القيود اليومية في IndexedDB:', error);
            }
        }

        // حفظ في localStorage كنسخة احتياطية
        localStorage.setItem('glassERP_journalEntries', JSON.stringify(journalEntries));

        // تأكيد الحفظ
        const savedData = localStorage.getItem('glassERP_journalEntries');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            console.log('💾 تم حفظ القيود اليومية في localStorage:', parsedData.length, 'قيد');
            return true;
        } else {
            console.error('❌ فشل في حفظ القيود اليومية');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ القيود اليومية:', error);
        showSimpleNotification('خطأ في حفظ القيود اليومية', 'error');
        return false;
    }
}

/**
 * إنشاء واجهة الحسابات
 */
function createAccountsInterface() {
    const accountsContainer = document.getElementById('accounts');
    
    accountsContainer.innerHTML = `
        <div class="accounts-dashboard">
            <!-- شريط الأدوات العلوي -->
            <div class="accounts-toolbar">
                <div class="toolbar-left">
                    <h2><i class="fas fa-chart-line"></i> نظام الحسابات المتكامل</h2>
                    <div class="fiscal-info">
                        <span class="fiscal-year">السنة المالية: ${currentFiscalYear}</span>
                        <span class="fiscal-period">الفترة: ${currentPeriod}</span>
                    </div>
                </div>
                <div class="toolbar-right">
                    <button class="btn-primary" onclick="showAddAccountModal()">
                        <i class="fas fa-plus"></i> إضافة حساب
                    </button>
                    <button class="btn-secondary" onclick="showAddJournalModal()">
                        <i class="fas fa-book"></i> قيد يومي
                    </button>
                    <button class="btn-success" onclick="saveAllData()" title="حفظ جميع البيانات">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button class="btn-warning" onclick="loadAllData()" title="إعادة تحميل البيانات">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                    <button class="btn-danger" onclick="clearAllData()" title="مسح جميع البيانات">
                        <i class="fas fa-trash"></i> مسح
                    </button>
                    <button class="btn-info" onclick="exportAccountsData()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <!-- البطاقات الإحصائية -->
            <div class="accounts-stats">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إجمالي الحسابات</h3>
                        <p class="stat-value">${accounts.length}</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h3>القيود اليومية</h3>
                        <p class="stat-value">${journalEntries.length}</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إجمالي الأصول</h3>
                        <p class="stat-value" id="total-assets-value">0.00</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إجمالي الخصوم</h3>
                        <p class="stat-value" id="total-liabilities-value">0.00</p>
                    </div>
                </div>
            </div>

            <!-- تبويبات النظام المحاسبي -->
            <div class="accounts-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" data-tab="chart">
                        <i class="fas fa-sitemap"></i> شجرة الحسابات
                    </button>
                    <button class="tab-btn" data-tab="journal">
                        <i class="fas fa-book"></i> دفتر اليومية
                    </button>
                    <button class="tab-btn" data-tab="ledger">
                        <i class="fas fa-list"></i> دفتر الأستاذ
                    </button>
                    <button class="tab-btn" data-tab="trial">
                        <i class="fas fa-balance-scale"></i> ميزان المراجعة
                    </button>
                    <button class="tab-btn" data-tab="income">
                        <i class="fas fa-chart-line"></i> قائمة الدخل
                    </button>
                    <button class="tab-btn" data-tab="balance">
                        <i class="fas fa-chart-pie"></i> الميزانية العمومية
                    </button>
                </div>
                
                <div class="tab-content">
                    <!-- شجرة الحسابات -->
                    <div id="chart-tab" class="tab-pane active">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>شجرة الحسابات</h3>
                                <div class="chart-actions">
                                    <button class="btn-sm" onclick="expandAllAccounts()">
                                        <i class="fas fa-expand"></i> توسيع الكل
                                    </button>
                                    <button class="btn-sm" onclick="collapseAllAccounts()">
                                        <i class="fas fa-compress"></i> طي الكل
                                    </button>
                                    <button class="btn-sm" onclick="refreshChartOfAccounts()">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                </div>
                            </div>
                            <div id="chart-tree" class="chart-tree"></div>
                        </div>
                    </div>
                    
                    <!-- دفتر اليومية -->
                    <div id="journal-tab" class="tab-pane">
                        <div class="journal-container">
                            <div class="journal-header">
                                <h3>دفتر اليومية</h3>
                                <div class="journal-actions">
                                    <button class="btn-sm" onclick="showAddJournalModal()">
                                        <i class="fas fa-plus"></i> قيد جديد
                                    </button>
                                    <button class="btn-sm" onclick="exportJournalEntries()">
                                        <i class="fas fa-download"></i> تصدير
                                    </button>
                                </div>
                            </div>
                            <div id="journal-entries" class="journal-entries"></div>
                        </div>
                    </div>
                    
                    <!-- دفتر الأستاذ -->
                    <div id="ledger-tab" class="tab-pane">
                        <div class="ledger-container">
                            <div class="ledger-header">
                                <h3>دفتر الأستاذ التفصيلي</h3>
                                <div class="ledger-filters">
                                    <select id="ledger-account-filter">
                                        <option value="">جميع الحسابات</option>
                                    </select>
                                    <input type="date" id="ledger-date-from" placeholder="من تاريخ">
                                    <input type="date" id="ledger-date-to" placeholder="إلى تاريخ">
                                    <button class="btn-sm" onclick="filterLedger()">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                            </div>
                            <div id="ledger-entries" class="ledger-entries"></div>
                        </div>
                    </div>
                    
                    <!-- ميزان المراجعة -->
                    <div id="trial-tab" class="tab-pane">
                        <div class="trial-container">
                            <div class="trial-header">
                                <h3>ميزان المراجعة</h3>
                                <div class="trial-actions">
                                    <button class="btn-sm" onclick="generateTrialBalance()">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                    <button class="btn-sm" onclick="exportTrialBalance()">
                                        <i class="fas fa-download"></i> تصدير
                                    </button>
                                </div>
                            </div>
                            <div id="trial-balance" class="trial-balance"></div>
                        </div>
                    </div>
                    
                    <!-- قائمة الدخل -->
                    <div id="income-tab" class="tab-pane">
                        <div class="income-container">
                            <div class="income-header">
                                <h3>قائمة الدخل</h3>
                                <div class="income-actions">
                                    <button class="btn-sm" onclick="generateIncomeStatement()">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                    <button class="btn-sm" onclick="printIncomeStatement()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                </div>
                            </div>
                            <div id="income-statement" class="income-statement"></div>
                        </div>
                    </div>
                    
                    <!-- الميزانية العمومية -->
                    <div id="balance-tab" class="tab-pane">
                        <div class="balance-container">
                            <div class="balance-header">
                                <h3>الميزانية العمومية</h3>
                                <div class="balance-actions">
                                    <button class="btn-sm" onclick="generateBalanceSheet()">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                    <button class="btn-sm" onclick="printBalanceSheet()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                </div>
                            </div>
                            <div id="balance-sheet" class="balance-sheet"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // ربط أحداث التبويبات
    bindTabEvents();
    
    // تحميل المحتوى الأولي
    loadChartOfAccounts();
    loadJournalEntries();
    updateAccountStats();
}

/**
 * ربط أحداث التبويبات
 */
function bindTabEvents() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    if (tabButtons && tabButtons.length > 0) {
        tabButtons.forEach(button => {
            if (button) {
                button.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // إزالة الفئة النشطة من جميع الأزرار والتبويبات
                    if (tabButtons && tabButtons.length > 0) {
                        tabButtons.forEach(btn => {
                            if (btn) btn.classList.remove('active');
                        });
                    }
                    if (tabPanes && tabPanes.length > 0) {
                        tabPanes.forEach(pane => {
                            if (pane) pane.classList.remove('active');
                        });
                    }
                    
                    // إضافة الفئة النشطة للزر والتبويب المحدد
                    this.classList.add('active');
                    const targetPane = document.getElementById(`${targetTab}-tab`);
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }
                    
                    // تحميل المحتوى حسب التبويب
                    switch(targetTab) {
                        case 'chart':
                            loadChartOfAccounts();
                            break;
                        case 'journal':
                            loadJournalEntries();
                            break;
                        case 'ledger':
                            loadLedgerEntries();
                            break;
                        case 'trial':
                            generateTrialBalance();
                            break;
                        case 'income':
                            generateIncomeStatement();
                            break;
                        case 'balance':
                            generateBalanceSheet();
                            break;
                    }
                });
            }
        });
    }
}

/**
 * تحميل شجرة الحسابات
 */
function loadChartOfAccounts() {
    const chartTree = document.getElementById('chart-tree');
    if (!chartTree) return;
    
    // التحقق من وجود بيانات الحسابات
    if (!accounts || !Array.isArray(accounts)) {
        console.warn('⚠️ بيانات الحسابات غير متوفرة أو غير صحيحة');
        chartTree.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>لا توجد بيانات حسابات</h3>
                <p>يرجى التأكد من تحميل بيانات الحسابات</p>
            </div>
        `;
        return;
    }
    
    chartTree.innerHTML = `
        <div class="chart-tree-header">
            <div class="tree-column">رقم الحساب</div>
            <div class="tree-column">اسم الحساب</div>
            <div class="tree-column">النوع</div>
            <div class="tree-column">الرصيد</div>
            <div class="tree-column">الإجراءات</div>
        </div>
        <div class="chart-tree-body" id="chart-tree-body"></div>
    `;
    
    const treeBody = document.getElementById('chart-tree-body');
    const rootAccounts = accounts.filter(acc => acc && !acc.parentId);
    
    if (rootAccounts.length === 0) {
        treeBody.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>لا توجد حسابات</h3>
                <p>قم بإضافة حسابات جديدة لبدء العمل</p>
            </div>
        `;
        return;
    }
    
    rootAccounts.forEach(account => {
        treeBody.appendChild(createAccountTreeItem(account));
    });
}

/**
 * إنشاء عنصر شجرة الحسابات
 */
function createAccountTreeItem(account) {
    if (!account) {
        console.warn('⚠️ محاولة إنشاء عنصر شجرة لحساب غير محدد');
        return document.createElement('div');
    }
    
    const item = document.createElement('div');
    item.className = 'tree-item';
    item.style.paddingLeft = `${((account.level || 1) - 1) * 20}px`;
    
    const children = accounts.filter(acc => acc && acc.parentId === account.id);
    const hasChildren = children.length > 0;
    
    item.innerHTML = `
        <div class="tree-row ${hasChildren ? 'has-children' : ''}">
            <div class="tree-cell account-id">
                ${hasChildren ? '<i class="fas fa-chevron-down expand-icon"></i>' : ''}
                ${account.id}
            </div>
            <div class="tree-cell account-name">${account.name}</div>
            <div class="tree-cell account-type">
                <span class="type-badge type-${account.type}">${getAccountTypeName(account.type)}</span>
            </div>
            <div class="tree-cell account-balance">
                <span class="balance-amount ${account.balance >= 0 ? 'positive' : 'negative'}">
                    ${formatCurrency(account.balance)}
                </span>
            </div>
            <div class="tree-cell account-actions">
                <button class="btn-icon" onclick="editAccount('${account.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="deleteAccount('${account.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        ${hasChildren ? '<div class="tree-children" style="display: none;"></div>' : ''}
    `;
    
    // إضافة الأحداث
    const expandIcon = item.querySelector('.expand-icon');
    if (expandIcon) {
        expandIcon.addEventListener('click', function() {
            const childrenContainer = item.querySelector('.tree-children');
            const isExpanded = childrenContainer.style.display !== 'none';
            
            if (isExpanded) {
                childrenContainer.style.display = 'none';
                this.className = 'fas fa-chevron-right expand-icon';
            } else {
                childrenContainer.style.display = 'block';
                this.className = 'fas fa-chevron-down expand-icon';
                
                // تحميل الحسابات الفرعية
                if (childrenContainer.children.length === 0 && Array.isArray(children)) {
                    children.forEach(child => {
                        if (child) {
                            childrenContainer.appendChild(createAccountTreeItem(child));
                        }
                    });
                }
            }
        });
    }
    
    return item;
}

/**
 * تعديل حساب
 */
function editAccount(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) {
        showSimpleNotification('الحساب غير موجود', 'error');
        return;
    }
    
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> تعديل الحساب</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="edit-account-form">
                    <div class="form-group">
                        <label for="edit-account-id">رقم الحساب *</label>
                        <input type="text" id="edit-account-id" value="${account.id}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-account-name">اسم الحساب *</label>
                        <input type="text" id="edit-account-name" value="${account.name}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-account-type">نوع الحساب *</label>
                        <select id="edit-account-type" required>
                            <option value="header" ${account.type === 'header' ? 'selected' : ''}>حساب رئيسي</option>
                            <option value="asset" ${account.type === 'asset' ? 'selected' : ''}>أصول</option>
                            <option value="liability" ${account.type === 'liability' ? 'selected' : ''}>خصوم</option>
                            <option value="equity" ${account.type === 'equity' ? 'selected' : ''}>حقوق ملكية</option>
                            <option value="revenue" ${account.type === 'revenue' ? 'selected' : ''}>إيرادات</option>
                            <option value="expense" ${account.type === 'expense' ? 'selected' : ''}>مصروفات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-account-parent">الحساب الأب</label>
                        <select id="edit-account-parent">
                            <option value="">بدون أب (حساب رئيسي)</option>
                            ${accounts
                                .filter(acc => acc.id !== accountId && acc.type === 'header')
                                .map(acc => `<option value="${acc.id}" ${account.parentId === acc.id ? 'selected' : ''}>${acc.id} - ${acc.name}</option>`)
                                .join('')}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal()">إلغاء</button>
                <button class="btn-primary" onclick="saveAccountEdit('${accountId}')">حفظ التعديل</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

/**
 * حفظ تعديل الحساب
 */
function saveAccountEdit(accountId) {
    const id = document.getElementById('edit-account-id').value.trim();
    const name = document.getElementById('edit-account-name').value.trim();
    const type = document.getElementById('edit-account-type').value;
    const parentId = document.getElementById('edit-account-parent').value || null;
    
    if (!id || !name) {
        showSimpleNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // التحقق من عدم تكرار رقم الحساب
    const existingAccount = accounts.find(acc => acc.id === id && acc.id !== accountId);
    if (existingAccount) {
        showSimpleNotification('رقم الحساب موجود مسبقاً', 'error');
        return;
    }
    
    // التحقق من عدم وجود حسابات فرعية إذا تم تغيير النوع إلى فرعي
    const hasChildren = accounts.some(acc => acc.parentId === accountId);
    if (hasChildren && type !== 'header') {
        showSimpleNotification('لا يمكن تغيير نوع الحساب إلى فرعي لأنه يحتوي على حسابات فرعية', 'error');
        return;
    }
    
    // تحديث الحساب
    const accountIndex = accounts.findIndex(acc => acc.id === accountId);
    if (accountIndex === -1) {
        showSimpleNotification('الحساب غير موجود', 'error');
        return;
    }
    
    accounts[accountIndex] = {
        ...accounts[accountIndex],
        id,
        name,
        type,
        parentId
    };
    
    // حفظ البيانات
    saveAccountsData();
    
    // إغلاق النافذة
    closeModal();
    
    // تحديث الواجهة
    loadChartOfAccounts();
    updateAccountStats();
    
    showSimpleNotification('تم تعديل الحساب بنجاح', 'success');
}

/**
 * حذف حساب
 */
function deleteAccount(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) {
        showSimpleNotification('الحساب غير موجود', 'error');
        return;
    }
    
    // التحقق من وجود حسابات فرعية
    const hasChildren = accounts.some(acc => acc.parentId === accountId);
    if (hasChildren) {
        if (!confirm('هذا الحساب يحتوي على حسابات فرعية. هل تريد حذف جميع الحسابات الفرعية أيضاً؟')) {
            return;
        }
    }
    
    // التحقق من وجود قيود مرتبطة
    const hasTransactions = journalEntries.some(entry => 
        entry.entries && entry.entries.some(line => line.accountId === accountId)
    );
    
    if (hasTransactions) {
        if (!confirm('هذا الحساب يحتوي على قيود محاسبية. حذفه سيؤثر على التقارير المالية. هل تريد المتابعة؟')) {
            return;
        }
    }
    
    // حذف الحساب وجميع الحسابات الفرعية
    const accountsToDelete = [accountId];
    if (hasChildren) {
        const getChildrenIds = (parentId) => {
            const children = accounts.filter(acc => acc.parentId === parentId);
            children.forEach(child => {
                accountsToDelete.push(child.id);
                getChildrenIds(child.id);
            });
        };
        getChildrenIds(accountId);
    }
    
    // حذف الحسابات
    accounts = accounts.filter(acc => !accountsToDelete.includes(acc.id));
    
    // حفظ البيانات
    saveAccountsData();
    
    // تحديث الواجهة
    loadChartOfAccounts();
    updateAccountStats();
    
    showSimpleNotification('تم حذف الحساب بنجاح', 'success');
}

/**
 * الحصول على اسم نوع الحساب
 */
function getAccountTypeName(type) {
    const typeNames = {
        'header': 'حساب رئيسي',
        'asset': 'أصول',
        'liability': 'خصوم',
        'equity': 'حقوق ملكية',
        'revenue': 'إيرادات',
        'expense': 'مصروفات'
    };
    return typeNames[type] || type;
}

/**
 * تحديث إحصائيات الحسابات
 */
function updateAccountStats() {
    // التحقق من وجود بيانات الحسابات
    if (!accounts || !Array.isArray(accounts)) {
        console.warn('⚠️ بيانات الحسابات غير متوفرة لتحديث الإحصائيات');
        return;
    }
    
    const totalAssets = accounts
        .filter(acc => acc && acc.type === 'asset')
        .reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    const totalLiabilities = accounts
        .filter(acc => acc && acc.type === 'liability')
        .reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    const totalAssetsElement = document.getElementById('total-assets-value');
    const totalLiabilitiesElement = document.getElementById('total-liabilities-value');
    
    if (totalAssetsElement) {
        totalAssetsElement.textContent = formatCurrency(totalAssets);
    }
    
    if (totalLiabilitiesElement) {
        totalLiabilitiesElement.textContent = formatCurrency(totalLiabilities);
    }
}

/**
 * حساب أرصدة الحسابات
 */
function calculateAccountBalances() {
    // التحقق من وجود بيانات الحسابات
    if (!accounts || !Array.isArray(accounts)) {
        console.warn('⚠️ بيانات الحسابات غير متوفرة لحساب الأرصدة');
        return;
    }
    
    // إعادة تعيين جميع الأرصدة
    accounts.forEach(account => {
        if (account) {
            account.balance = 0;
        }
    });
    
    // التحقق من وجود القيود اليومية
    if (!journalEntries || !Array.isArray(journalEntries)) {
        console.warn('⚠️ بيانات القيود اليومية غير متوفرة لحساب الأرصدة');
        return;
    }
    
    // حساب الأرصدة من القيود اليومية
    journalEntries.forEach(entry => {
        if (entry && entry.entries && Array.isArray(entry.entries)) {
            entry.entries.forEach(entryItem => {
                if (entryItem && entryItem.accountId) {
                    const account = accounts.find(acc => acc && acc.id === entryItem.accountId);
                    if (account) {
                        if (account.type === 'asset' || account.type === 'expense') {
                            account.balance += (entryItem.debit || 0) - (entryItem.credit || 0);
                        } else {
                            account.balance += (entryItem.credit || 0) - (entryItem.debit || 0);
                        }
                    }
                }
            });
        }
    });
    
    // حساب الأرصدة المجمعة للحسابات الأب
    calculateParentBalances();
    
    // تحديث الإحصائيات
    updateAccountStats();
}

/**
 * حساب الأرصدة المجمعة للحسابات الأب
 */
function calculateParentBalances() {
    // التحقق من وجود بيانات الحسابات
    if (!accounts || !Array.isArray(accounts)) {
        console.warn('⚠️ بيانات الحسابات غير متوفرة لحساب الأرصدة المجمعة');
        return;
    }
    
    // ترتيب الحسابات حسب المستوى (من الأعلى إلى الأقل)
    const sortedAccounts = [...accounts].sort((a, b) => (b.level || 1) - (a.level || 1));
    
    sortedAccounts.forEach(account => {
        if (account && account.parentId) {
            const parent = accounts.find(acc => acc && acc.id === account.parentId);
            if (parent) {
                parent.balance += (account.balance || 0);
            }
        }
    });
}

/**
 * تحميل القيود اليومية
 */
function loadJournalEntries() {
    const journalEntriesContainer = document.getElementById('journal-entries');
    if (!journalEntriesContainer) return;
    
    // التحقق من وجود بيانات القيود اليومية
    if (!journalEntries || !Array.isArray(journalEntries)) {
        console.warn('⚠️ بيانات القيود اليومية غير متوفرة أو غير صحيحة');
        journalEntriesContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>لا توجد بيانات قيود يومية</h3>
                <p>يرجى التأكد من تحميل بيانات القيود اليومية</p>
            </div>
        `;
        return;
    }
    
    if (journalEntries.length === 0) {
        journalEntriesContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-book-open"></i>
                <h3>لا توجد قيود يومية</h3>
                <p>قم بإضافة قيد يومي جديد لبدء العمل</p>
                <button class="btn-primary" onclick="showAddJournalModal()">
                    <i class="fas fa-plus"></i> إضافة قيد جديد
                </button>
            </div>
        `;
    } else {
        // عرض القيود اليومية في جدول منظم
        let tableRows = '';

        journalEntries.forEach(entry => {
            if (!entry || !entry.entries || !Array.isArray(entry.entries)) return;

            const entryDate = entry.date ? new Date(entry.date).toLocaleDateString('ar-SA') : 'غير محدد';
            const entryNumber = entry.number || 'غير محدد';
            const entryDescription = entry.description || 'غير محدد';

            // إنشاء صف لكل حساب في القيد
            entry.entries.forEach((item, index) => {
                if (!item) return;

                const accountName = getAccountName(item.accountId) || 'حساب غير معروف';
                const debitAmount = item.debit || 0;
                const creditAmount = item.credit || 0;
                const amount = debitAmount > 0 ? debitAmount : creditAmount;

                tableRows += `
                    <tr class="journal-entry-row" data-entry-id="${entry.id}">
                        <td class="entry-number">${index === 0 ? entryNumber : ''}</td>
                        <td class="entry-date">${index === 0 ? entryDate : ''}</td>
                        <td class="entry-description">${index === 0 ? entryDescription : ''}</td>
                        <td class="account-debit">${debitAmount > 0 ? accountName : ''}</td>
                        <td class="account-credit">${creditAmount > 0 ? accountName : ''}</td>
                        <td class="entry-amount">${formatCurrency(amount)}</td>
                        <td class="entry-actions">
                            ${index === 0 ? `
                                <button class="btn-delete-entry" onclick="deleteJournalEntry('${entry.id}')" title="حذف القيد">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            });
        });

        journalEntriesContainer.innerHTML = `
            <div class="journal-entries-header">
                <h3><i class="fas fa-book"></i> القيود اليومية المحفوظة</h3>
                <button class="btn-primary" onclick="showAddJournalModal()">
                    <i class="fas fa-plus"></i> إضافة قيد جديد
                </button>
            </div>
            <div class="journal-entries-table-container">
                <table class="journal-entries-table">
                    <thead>
                        <tr>
                            <th>رقم القيد</th>
                            <th>التاريخ</th>
                            <th>الوصف</th>
                            <th>الحساب المدين</th>
                            <th>الحساب الدائن</th>
                            <th>القيمة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        `;
    }
}

/**
 * تحميل دفتر الأستاذ
 */
function loadLedgerEntries() {
    const ledgerContainer = document.getElementById('ledger-tab');
    if (!ledgerContainer) return;

    // واجهة اختيار الحساب وفلترة التاريخ
    ledgerContainer.innerHTML = `
        <div class="ledger-filters">
            <div class="filter-group">
                <label for="ledger-account-select">الحساب:</label>
                <select id="ledger-account-select" class="ledger-select">
                    <option value="">اختر الحساب</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="ledger-date-from">من:</label>
                <input type="date" id="ledger-date-from" class="ledger-date">
            </div>
            <div class="filter-group">
                <label for="ledger-date-to">إلى:</label>
                <input type="date" id="ledger-date-to" class="ledger-date">
            </div>
            <button class="btn-primary" id="ledger-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="ledger-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="ledger-table-container">
            <table class="ledger-table">
                <thead>
                    <tr>
                        <th>رقم القيد</th>
                        <th>التاريخ</th>
                        <th>الوصف</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد التراكمي</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="ledger-table-body">
                    <!-- سيتم تعبئة القيود هنا -->
                </tbody>
                <tfoot>
                    <tr class="ledger-summary-row">
                        <td colspan="3"><strong>الإجمالي</strong></td>
                        <td id="ledger-total-debit">0.00</td>
                        <td id="ledger-total-credit">0.00</td>
                        <td id="ledger-total-balance">0.00</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    // تعبئة قائمة الحسابات
    const accountSelect = document.getElementById('ledger-account-select');
    if (accountSelect && Array.isArray(accounts)) {
        accounts.filter(acc => acc.type !== 'header').forEach(acc => {
            const option = document.createElement('option');
            option.value = acc.id;
            option.textContent = `${acc.id} - ${acc.name}`;
            accountSelect.appendChild(option);
        });
    }

    // ربط الأحداث
    document.getElementById('ledger-filter-btn').onclick = filterLedgerEntries;
    document.getElementById('ledger-print-btn').onclick = printLedgerTable;
    if (accountSelect) accountSelect.onchange = filterLedgerEntries;
    document.getElementById('ledger-date-from').onchange = filterLedgerEntries;
    document.getElementById('ledger-date-to').onchange = filterLedgerEntries;

    // عرض القيود الأولية
    filterLedgerEntries();
}

function filterLedgerEntries() {
    const accountId = document.getElementById('ledger-account-select').value;
    const dateFrom = document.getElementById('ledger-date-from').value;
    const dateTo = document.getElementById('ledger-date-to').value;
    const tbody = document.getElementById('ledger-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    if (!accountId) {
        tbody.innerHTML = '<tr><td colspan="7" class="empty-row">يرجى اختيار حساب لعرض القيود</td></tr>';
        updateLedgerSummary([]);
        return;
    }

    // جلب القيود لهذا الحساب
    const entries = getLedgerEntriesForAccount(accountId, dateFrom, dateTo);
    let runningBalance = 0;
    let totalDebit = 0;
    let totalCredit = 0;

    entries.forEach(entry => {
        runningBalance += entry.debit - entry.credit;
        totalDebit += entry.debit;
        totalCredit += entry.credit;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${entry.journalId}</td>
            <td>${formatLedgerDate(entry.date)}</td>
            <td>${entry.description}</td>
            <td class="debit">${entry.debit > 0 ? formatCurrency(entry.debit) : ''}</td>
            <td class="credit">${entry.credit > 0 ? formatCurrency(entry.credit) : ''}</td>
            <td class="balance">${formatCurrency(runningBalance)}</td>
            <td>
                <button class="btn-icon" title="حذف" onclick="deleteJournalEntry('${entry.journalId}')"><i class="fas fa-trash"></i></button>
                <button class="btn-icon" title="طباعة" onclick="printSingleLedgerEntry('${entry.journalId}')"><i class="fas fa-print"></i></button>
                <button class="btn-icon" title="PDF" onclick="saveLedgerEntryPDF('${entry.journalId}')"><i class="fas fa-file-pdf"></i></button>
            </td>
        `;
        tbody.appendChild(tr);
    });
    updateLedgerSummary(entries, totalDebit, totalCredit, runningBalance);
}

function updateLedgerSummary(entries, totalDebit = 0, totalCredit = 0, lastBalance = 0) {
    document.getElementById('ledger-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('ledger-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('ledger-total-balance').textContent = formatCurrency(lastBalance);
}

function getLedgerEntriesForAccount(accountId, dateFrom, dateTo) {
    // جلب جميع القيود لهذا الحساب مع الفلترة بالتاريخ
    if (!Array.isArray(journalEntries)) return [];
    let entries = [];
    journalEntries.forEach(journal => {
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                // فلترة بالتاريخ
                if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
                if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
                entries.push({
                    journalId: journal.id,
                    date: journal.date,
                    description: journal.description,
                    debit: line.debit,
                    credit: line.credit
                });
            }
        });
    });
    // ترتيب حسب التاريخ
    entries.sort((a, b) => new Date(a.date) - new Date(b.date));
    return entries;
}

function formatLedgerDate(dateStr) {
    // التاريخ الميلادي بالإنجليزية فقط
    const d = new Date(dateStr);
    return d.toLocaleDateString('en-GB');
}

function printLedgerTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول القيود
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

function printSingleLedgerEntry(journalId) {
    // نافذة طباعة قيد منفرد تشمل بيانات الشركة والشعار
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ طباعة القيد قريبًا');
}

function saveLedgerEntryPDF(journalId) {
    // حفظ PDF لقيد منفرد
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ حفظ PDF قريبًا');
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * الحصول على اسم الحساب
 */
function getAccountName(accountId) {
    if (!accountId || !accounts || !Array.isArray(accounts)) {
        return 'حساب غير معروف';
    }
    
    const account = accounts.find(acc => acc && acc.id === accountId);
    return account ? account.name : 'حساب غير معروف';
}

/**
 * توسيع جميع الحسابات
 */
function expandAllAccounts() {
    const expandIcons = document.querySelectorAll('.expand-icon');
    if (expandIcons && expandIcons.length > 0) {
        expandIcons.forEach(icon => {
            if (icon) {
                const treeItem = icon.closest('.tree-item');
                if (treeItem) {
                    const childrenContainer = treeItem.querySelector('.tree-children');
                    if (childrenContainer) {
                        childrenContainer.style.display = 'block';
                        icon.className = 'fas fa-chevron-down expand-icon';
                    }
                }
            }
        });
    }
}

/**
 * طي جميع الحسابات
 */
function collapseAllAccounts() {
    const expandIcons = document.querySelectorAll('.expand-icon');
    if (expandIcons && expandIcons.length > 0) {
        expandIcons.forEach(icon => {
            if (icon) {
                const treeItem = icon.closest('.tree-item');
                if (treeItem) {
                    const childrenContainer = treeItem.querySelector('.tree-children');
                    if (childrenContainer) {
                        childrenContainer.style.display = 'none';
                        icon.className = 'fas fa-chevron-right expand-icon';
                    }
                }
            }
        });
    }
}

/**
 * تحديث شجرة الحسابات
 */
function refreshChartOfAccounts() {
    loadChartOfAccounts();
    updateAccountStats();
}

/**
 * عرض نافذة إضافة حساب
 */
function showAddAccountModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> إضافة حساب جديد</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-group">
                        <label for="account-id">رقم الحساب *</label>
                        <input type="text" id="account-id" required placeholder="مثال: 1115">
                    </div>
                    
                    <div class="form-group">
                        <label for="account-name">اسم الحساب *</label>
                        <input type="text" id="account-name" required placeholder="أدخل اسم الحساب">
                    </div>
                    
                    <div class="form-group">
                        <label for="account-type">نوع الحساب *</label>
                        <select id="account-type" required>
                            <option value="">اختر نوع الحساب</option>
                            <option value="asset">أصول</option>
                            <option value="liability">خصوم</option>
                            <option value="equity">حقوق ملكية</option>
                            <option value="revenue">إيرادات</option>
                            <option value="expense">مصروفات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="parent-account">الحساب الأب</label>
                        <select id="parent-account">
                            <option value="">بدون حساب أب</option>
                            ${accounts && Array.isArray(accounts) ? accounts.map(acc => {
                                if (!acc) return '';
                                return `<option value="${acc.id || ''}">${acc.id || ''} - ${acc.name || ''}</option>`;
                            }).join('') : ''}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="opening-balance">الرصيد الافتتاحي</label>
                        <input type="number" id="opening-balance" step="0.01" placeholder="0.00">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal()">إلغاء</button>
                <button class="btn-primary" onclick="saveNewAccount()">حفظ الحساب</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

/**
 * إغلاق النافذة المنبثقة
 */
function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

/**
 * حفظ حساب جديد
 */
function saveNewAccount() {
    const form = document.getElementById('add-account-form');
    const formData = new FormData(form);
    
    const accountId = document.getElementById('account-id').value;
    const accountName = document.getElementById('account-name').value;
    const accountType = document.getElementById('account-type').value;
    const parentAccount = document.getElementById('parent-account').value;
    const openingBalance = parseFloat(document.getElementById('opening-balance').value) || 0;
    
    // التحقق من صحة البيانات
    if (!accountId || !accountName || !accountType) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من عدم تكرار رقم الحساب
    if (accounts.find(acc => acc.id === accountId)) {
        alert('رقم الحساب موجود مسبقاً');
        return;
    }
    
    // تحديد المستوى
    const parentLevel = parentAccount ? accounts.find(acc => acc.id === parentAccount)?.level || 1 : 1;
    const newLevel = parentLevel + 1;
    
    // إنشاء الحساب الجديد
    const newAccount = {
        id: accountId,
        name: accountName,
        type: accountType,
        parentId: parentAccount || null,
        level: newLevel,
        balance: openingBalance,
        isActive: true
    };
    
    // إضافة الحساب
    accounts.push(newAccount);
    
    // حفظ فوري للبيانات
    const saved = saveAccountsData();
    if (!saved) {
        alert('خطأ في حفظ الحساب');
        return;
    }
    
    // إغلاق النافذة
    closeModal();
    
    // تحديث الواجهة
    loadChartOfAccounts();
    updateAccountStats();
    
    console.log('✅ تم إضافة الحساب الجديد:', newAccount);
    
    // إضافة إشعار
    if (window.NotificationsModule && window.NotificationsModule.addNotification) {
        window.NotificationsModule.addNotification(
            'تم إضافة حساب جديد',
            `تم إضافة الحساب ${newAccount.id} - ${newAccount.name} بنجاح`,
            'success',
            'accounts'
        );
    }
    
    alert('تم إضافة الحساب بنجاح');
}

/**
 * عرض نافذة إضافة قيد يومي
 */
function showAddJournalModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-book"></i> إضافة قيد يومي جديد</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-journal-form">
                    <div class="journal-header-info">
                        <div class="form-group">
                            <label for="journal-date">التاريخ *</label>
                            <input type="date" id="journal-date" required value="${new Date().toISOString().split('T')[0]}">
                        </div>
                        
                        <div class="form-group">
                            <label for="journal-number">رقم القيد</label>
                            <input type="text" id="journal-number" value="JE${String(journalEntries.length + 1).padStart(3, '0')}" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label for="journal-description">الوصف *</label>
                            <input type="text" id="journal-description" required placeholder="وصف القيد">
                        </div>
                    </div>
                    
                    <div class="journal-entries-container">
                        <h4>تفاصيل القيد</h4>
                        <div class="journal-entries-header">
                            <span>الحساب</span>
                            <span>مدين</span>
                            <span>دائن</span>
                            <span>الوصف</span>
                            <span>إجراءات</span>
                        </div>
                        <div id="journal-entries-list">
                            <div class="journal-entry-line">
                                <select class="account-select" required>
                                    <option value="">اختر الحساب</option>
                                    ${accounts && Array.isArray(accounts) ? accounts
                                        .filter(acc => acc && acc.type !== 'header') // استبعاد الحسابات الرئيسية
                                        .map(acc => {
                                            return `<option value="${acc.id || ''}">${acc.id || ''} - ${acc.name || ''}</option>`;
                                        }).join('') : ''}
                                </select>
                                <input type="number" class="debit-amount" step="0.01" placeholder="0.00">
                                <input type="number" class="credit-amount" step="0.01" placeholder="0.00">
                                <input type="text" class="entry-description" placeholder="وصف البند">
                                <button type="button" class="btn-icon" onclick="removeJournalEntry(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <button type="button" class="btn-secondary" onclick="addJournalEntryLine()">
                            <i class="fas fa-plus"></i> إضافة بند
                        </button>
                    </div>
                    
                    <div class="journal-totals">
                        <div class="total-item">
                            <span>إجمالي المدين:</span>
                            <span id="total-debit">0.00</span>
                        </div>
                        <div class="total-item">
                            <span>إجمالي الدائن:</span>
                            <span id="total-credit">0.00</span>
                        </div>
                        <div class="total-item balance">
                            <span>الفرق:</span>
                            <span id="balance-difference">0.00</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal()">إلغاء</button>
                <button class="btn-primary" onclick="saveNewJournalEntry()">حفظ القيد</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.style.display = 'flex';
    
    // إضافة مستمعي الأحداث لحساب المجاميع
    addJournalEntryListeners();
}

/**
 * إضافة بند جديد للقيد
 */
function addJournalEntryLine() {
    const entriesList = document.getElementById('journal-entries-list');
    const newLine = document.createElement('div');
    newLine.className = 'journal-entry-line';
    newLine.innerHTML = `
        <select class="account-select" required>
            <option value="">اختر الحساب</option>
            ${accounts && Array.isArray(accounts) ? accounts
                .filter(acc => acc && acc.type !== 'header') // استبعاد الحسابات الرئيسية
                .map(acc => {
                    return `<option value="${acc.id || ''}">${acc.id || ''} - ${acc.name || ''}</option>`;
                }).join('') : ''}
        </select>
        <input type="number" class="debit-amount" step="0.01" placeholder="0.00">
        <input type="number" class="credit-amount" step="0.01" placeholder="0.00">
        <input type="text" class="entry-description" placeholder="وصف البند">
        <button type="button" class="btn-icon" onclick="removeJournalEntry(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    
    entriesList.appendChild(newLine);
    addJournalEntryListeners();
}

/**
 * إزالة بند من القيد
 */
function removeJournalEntry(button) {
    const entryLine = button.closest('.journal-entry-line');
    if (document.querySelectorAll('.journal-entry-line').length > 1) {
        entryLine.remove();
        calculateJournalTotals();
    } else {
        alert('يجب أن يحتوي القيد على بند واحد على الأقل');
    }
}

/**
 * إضافة مستمعي الأحداث لحساب المجاميع
 */
function addJournalEntryListeners() {
    const debitInputs = document.querySelectorAll('.debit-amount');
    const creditInputs = document.querySelectorAll('.credit-amount');
    
    if (debitInputs && debitInputs.length > 0) {
        debitInputs.forEach(input => {
            if (input) {
                input.addEventListener('input', calculateJournalTotals);
            }
        });
    }
    
    if (creditInputs && creditInputs.length > 0) {
        creditInputs.forEach(input => {
            if (input) {
                input.addEventListener('input', calculateJournalTotals);
            }
        });
    }
}

/**
 * حساب مجاميع القيد
 */
function calculateJournalTotals() {
    let totalDebit = 0;
    let totalCredit = 0;
    
    const debitInputs = document.querySelectorAll('.debit-amount');
    const creditInputs = document.querySelectorAll('.credit-amount');
    
    if (debitInputs && debitInputs.length > 0) {
        debitInputs.forEach(input => {
            if (input) {
                totalDebit += parseFloat(input.value) || 0;
            }
        });
    }
    
    if (creditInputs && creditInputs.length > 0) {
        creditInputs.forEach(input => {
            if (input) {
                totalCredit += parseFloat(input.value) || 0;
            }
        });
    }
    
    const difference = totalDebit - totalCredit;
    
    document.getElementById('total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('balance-difference').textContent = formatCurrency(difference);
    
    // تغيير لون الفرق
    const balanceElement = document.getElementById('balance-difference');
    if (Math.abs(difference) < 0.01) {
        balanceElement.style.color = '#059669';
        balanceElement.textContent = 'متوازن';
    } else {
        balanceElement.style.color = '#dc2626';
    }
}

/**
 * حذف قيد يومي
 */
async function deleteJournalEntry(entryId) {
    if (!entryId) {
        console.error('❌ معرف القيد مطلوب للحذف');
        return false;
    }

    // تأكيد الحذف
    const confirmDelete = confirm('هل أنت متأكد من حذف هذا القيد؟\nلا يمكن التراجع عن هذا الإجراء.');
    if (!confirmDelete) {
        return false;
    }

    try {
        // حذف من IndexedDB أولاً
        let deletedFromDB = false;
        if (accountsDataStore) {
            deletedFromDB = await accountsDataStore.deleteJournalEntry(entryId);
            if (deletedFromDB) {
                console.log('✅ تم حذف القيد من IndexedDB:', entryId);
            }
        }

        // حذف من المصفوفة المحلية
        const entryIndex = journalEntries.findIndex(entry => entry.id === entryId);
        if (entryIndex !== -1) {
            const deletedEntry = journalEntries.splice(entryIndex, 1)[0];
            console.log('✅ تم حذف القيد من المصفوفة المحلية:', deletedEntry.number);
        }

        // حفظ التحديث في localStorage كنسخة احتياطية
        try {
            localStorage.setItem('journalEntries', JSON.stringify(journalEntries));
        } catch (error) {
            console.warn('⚠️ تعذر تحديث localStorage:', error);
        }

        // تحديث الواجهة
        loadJournalEntries();
        calculateAccountBalances();
        updateAccountStats();

        // إضافة إشعار
        if (window.NotificationsModule && window.NotificationsModule.addNotification) {
            window.NotificationsModule.addNotification(
                'تم حذف قيد محاسبي',
                'تم حذف القيد اليومي بنجاح',
                'success',
                'accounts'
            );
        }

        console.log('✅ تم حذف القيد بنجاح:', entryId);
        return true;

    } catch (error) {
        console.error('❌ خطأ في حذف القيد:', error);
        alert('حدث خطأ أثناء حذف القيد. يرجى المحاولة مرة أخرى.');
        return false;
    }
}

/**
 * حفظ قيد يومي جديد
 */
async function saveNewJournalEntry() {
    const date = document.getElementById('journal-date').value;
    const number = document.getElementById('journal-number').value;
    const description = document.getElementById('journal-description').value;
    
    if (!date || !description) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const entries = [];
    const entryLines = document.querySelectorAll('.journal-entry-line');
    
    for (let line of entryLines) {
        const accountId = line.querySelector('.account-select').value;
        const debit = parseFloat(line.querySelector('.debit-amount').value) || 0;
        const credit = parseFloat(line.querySelector('.credit-amount').value) || 0;
        const entryDescription = line.querySelector('.entry-description').value;
        
        if (!accountId) {
            alert('يرجى اختيار جميع الحسابات');
            return;
        }
        
        if (debit === 0 && credit === 0) {
            alert('يرجى إدخال مبلغ في المدين أو الدائن');
            return;
        }
        
        entries.push({
            accountId,
            debit,
            credit,
            description: entryDescription
        });
    }
    
    // التحقق من توازن القيد
    const totalDebit = entries.reduce((sum, entry) => sum + entry.debit, 0);
    const totalCredit = entries.reduce((sum, entry) => sum + entry.credit, 0);
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
        alert('القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
        return;
    }
    
    // إنشاء القيد الجديد
    const newEntry = {
        id: `JE${Date.now()}`,
        number,
        date: new Date(date).toISOString(),
        description,
        entries
    };
    
    // إضافة القيد
    journalEntries.push(newEntry);

    // حفظ فوري للبيانات
    const saved = await saveJournalEntries();
    if (!saved) {
        alert('خطأ في حفظ القيد');
        return;
    }
    
    // إغلاق النافذة
    closeModal();
    
    // تحديث الواجهة
    loadJournalEntries();
    calculateAccountBalances();
    updateAccountStats();
    
    console.log('✅ تم إضافة القيد الجديد:', newEntry);
    
    // إضافة إشعار
    if (window.NotificationsModule && window.NotificationsModule.addNotification) {
        window.NotificationsModule.addNotification(
            'تم إضافة قيد محاسبي',
            `تم إضافة قيد يومي جديد برقم ${newEntry.number}`,
            'success',
            'accounts'
        );
    }
    
    // إشعار بسيط بدلاً من alert
    showSimpleNotification('تم حفظ القيد بنجاح', 'success');
}

/**
 * عرض إشعار بسيط
 */
function showSimpleNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `simple-notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إخفاء الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * تصدير بيانات الحسابات
 */
function exportAccountsData() {
    try {
        // إنشاء بيانات التصدير
        const exportData = {
            accounts: accounts,
            journalEntries: journalEntries,
            exportDate: new Date().toISOString(),
            fiscalYear: currentFiscalYear,
            period: currentPeriod
        };
        
        // تحويل البيانات إلى JSON
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        // إنشاء رابط التحميل
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `glass-erp-accounts-${new Date().toISOString().split('T')[0]}.json`;
        
        // تحميل الملف
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // تنظيف الذاكرة
        URL.revokeObjectURL(url);
        
        console.log('📤 تم تصدير بيانات الحسابات بنجاح');
        alert('تم تصدير بيانات الحسابات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تصدير البيانات:', error);
        alert('خطأ في تصدير البيانات');
    }
}

/**
 * تصدير القيود اليومية
 */
function exportJournalEntries() {
    localStorage.setItem('glassERP_journalEntries', JSON.stringify(journalEntries));
}

/**
 * الحصول على اسم نوع الحساب
 */
function getAccountTypeName(type) {
    const typeNames = {
        'asset': 'أصول',
        'liability': 'خصوم',
        'equity': 'حقوق ملكية',
        'revenue': 'إيرادات',
        'expense': 'مصروفات'
    };
    return typeNames[type] || type;
}

/**
 * تصدير ميزان المراجعة
 */
function exportTrialBalance() {
    try {
        // حساب الأرصدة
        calculateAccountBalances();
        
        // إنشاء بيانات ميزان المراجعة
        const trialBalanceData = accounts
            .filter(acc => acc.balance !== 0)
            .map(account => ({
                accountId: account.id,
                accountName: account.name,
                accountType: getAccountTypeName(account.type),
                debit: account.type === 'asset' || account.type === 'expense' ? account.balance : 0,
                credit: account.type === 'liability' || account.type === 'equity' || account.type === 'revenue' ? account.balance : 0
            }));
        
        // إنشاء HTML للتقرير
        const htmlContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>ميزان المراجعة - ${new Date().toLocaleDateString('ar-SA')}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .date { font-size: 14px; color: #666; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                    th { background-color: #f8f9fa; font-weight: bold; }
                    .debit { color: #059669; font-weight: bold; }
                    .credit { color: #dc2626; font-weight: bold; }
                    .totals { margin-top: 20px; text-align: left; }
                    .total-row { font-weight: bold; background-color: #f8f9fa; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="title">ميزان المراجعة</div>
                    <div class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>رقم الحساب</th>
                            <th>اسم الحساب</th>
                            <th>النوع</th>
                            <th>مدين</th>
                            <th>دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${trialBalanceData.map(item => `
                            <tr>
                                <td>${item.accountId}</td>
                                <td>${item.accountName}</td>
                                <td>${item.accountType}</td>
                                <td class="debit">${item.debit.toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</td>
                                <td class="credit">${item.credit.toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="3"><strong>الإجمالي</strong></td>
                            <td class="debit"><strong>${trialBalanceData.reduce((sum, item) => sum + item.debit, 0).toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</strong></td>
                            <td class="credit"><strong>${trialBalanceData.reduce((sum, item) => sum + item.credit, 0).toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </body>
            </html>
        `;
        
        // إنشاء ملف HTML للتحميل
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `trial-balance-${new Date().toISOString().split('T')[0]}.html`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        console.log('📤 تم تصدير ميزان المراجعة بنجاح');
        alert('تم تصدير ميزان المراجعة بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تصدير ميزان المراجعة:', error);
        alert('خطأ في تصدير ميزان المراجعة');
    }
}

/**
 * تصفية دفتر الأستاذ
 */
function filterLedger() {
    alert('سيتم إضافة هذه الميزة قريباً');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء ميزان المراجعة
 */
function generateTrialBalance() {
    const trialTab = document.getElementById('trial-tab');
    if (!trialTab) return;

    // واجهة الفلترة
    trialTab.innerHTML = `
        <div class="trial-filters">
            <div class="filter-group">
                <label for="trial-date-from">من:</label>
                <input type="date" id="trial-date-from" class="trial-date">
            </div>
            <div class="filter-group">
                <label for="trial-date-to">إلى:</label>
                <input type="date" id="trial-date-to" class="trial-date">
            </div>
            <button class="btn-primary" id="trial-filter-btn"><i class="fas fa-filter"></i> فلترة</button>
            <button class="btn-secondary" id="trial-print-btn"><i class="fas fa-print"></i> طباعة/حفظ PDF</button>
        </div>
        <div class="trial-table-container">
            <table class="trial-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody id="trial-table-body">
                    <!-- سيتم تعبئة الحسابات هنا -->
                </tbody>
                <tfoot>
                    <tr class="trial-summary-row">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td id="trial-total-debit">0.00</td>
                        <td id="trial-total-credit">0.00</td>
                        <td id="trial-total-balance">0.00</td>
                    </tr>
                    <tr class="trial-balance-check-row">
                        <td colspan="5" id="trial-balance-check"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('trial-filter-btn').onclick = filterTrialBalance;
    document.getElementById('trial-print-btn').onclick = printTrialBalanceTable;
    document.getElementById('trial-date-from').onchange = filterTrialBalance;
    document.getElementById('trial-date-to').onchange = filterTrialBalance;

    filterTrialBalance();
}

function filterTrialBalance() {
    const dateFrom = document.getElementById('trial-date-from').value;
    const dateTo = document.getElementById('trial-date-to').value;
    const tbody = document.getElementById('trial-table-body');
    if (!tbody) return;
    tbody.innerHTML = '';

    // جلب الحسابات النهائية فقط (التي ليس لها أبناء)
    const finalAccounts = accounts.filter(acc => !accounts.some(child => child.parentId === acc.id));

    let totalDebit = 0;
    let totalCredit = 0;
    let totalBalance = 0;

    finalAccounts.forEach(acc => {
        // حساب الرصيد في الفترة
        const {debit, credit, balance} = getAccountTrialBalance(acc.id, dateFrom, dateTo);
        totalDebit += debit;
        totalCredit += credit;
        totalBalance += balance;
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${acc.id}</td>
            <td>${acc.name}</td>
            <td class="debit">${debit > 0 ? formatCurrency(debit) : ''}</td>
            <td class="credit">${credit > 0 ? formatCurrency(credit) : ''}</td>
            <td class="balance">${formatCurrency(balance)}</td>
        `;
        tbody.appendChild(tr);
    });
    document.getElementById('trial-total-debit').textContent = formatCurrency(totalDebit);
    document.getElementById('trial-total-credit').textContent = formatCurrency(totalCredit);
    document.getElementById('trial-total-balance').textContent = formatCurrency(totalBalance);
    // تحقق التوازن
    const check = Math.abs(totalDebit - totalCredit) < 0.01 ? '✔️ متوازن' : '❌ غير متوازن';
    document.getElementById('trial-balance-check').textContent = check;
}

function getAccountTrialBalance(accountId, dateFrom, dateTo) {
    // حساب مجموع المدين والدائن والرصيد لهذا الحساب في الفترة
    if (!Array.isArray(journalEntries)) return {debit: 0, credit: 0, balance: 0};
    let debit = 0, credit = 0;
    journalEntries.forEach(journal => {
        if (dateFrom && new Date(journal.date) < new Date(dateFrom)) return;
        if (dateTo && new Date(journal.date) > new Date(dateTo)) return;
        journal.entries.forEach(line => {
            if (line.accountId === accountId) {
                debit += line.debit;
                credit += line.credit;
            }
        });
    });
    return {debit, credit, balance: debit - credit};
}

function printTrialBalanceTable() {
    // نافذة طباعة مخصصة تشمل بيانات الشركة والشعار وجدول ميزان المراجعة
    // سيتم تنفيذها لاحقًا
    alert('سيتم تنفيذ نافذة الطباعة قريبًا');
}

/**
 * إنشاء الميزانية العمومية
 */
function generateBalanceSheet() {
    try {
        const balanceSheet = document.getElementById('balance-sheet');
        if (!balanceSheet) {
            console.error('❌ عنصر الميزانية العمومية غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        // تحديث العنصر مباشرة
        balanceSheet.innerHTML = `
            <div class="report-section">
                <div class="section-title">الميزانية العمومية</div>
                
                <div class="balance-sheet-layout">
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الأصول</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${assets.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الأصول</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="balance-column">
                        <div class="balance-section">
                            <h4>الخصوم</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${liabilities.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="negative">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي الخصوم</strong></td>
                                        <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <h4>حقوق الملكية</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${equity.map(account => `
                                        <tr>
                                            <td>${account.name}</td>
                                            <td class="positive">${formatCurrency(account.balance)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>إجمالي حقوق الملكية</strong></td>
                                        <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="balance-section">
                            <table>
                                <tbody>
                                    <tr class="grand-total">
                                        <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                        <td><strong>${formatCurrency(totalLiabilities + totalEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">التحقق من التوازن</div>
                    <div class="summary-value ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'positive' : 'negative'}">
                        ${Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? 'متوازن' : 'غير متوازن'}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء الميزانية العمومية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الميزانية العمومية:', error);
        if (balanceSheet) {
            balanceSheet.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء قائمة الدخل
 */
function generateIncomeStatement() {
    try {
        const incomeStatement = document.getElementById('income-statement');
        if (!incomeStatement) {
            console.error('❌ عنصر قائمة الدخل غير موجود');
            return;
        }
        
        // حساب الأرصدة
        calculateAccountBalances();
        
        // تصنيف الحسابات
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        // حساب المجاميع
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        // تحديث العنصر مباشرة
        incomeStatement.innerHTML = `
            <div class="report-section">
                <div class="section-title">قائمة الدخل</div>
                
                <div class="income-section">
                    <h4>الإيرادات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${revenues.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="positive">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="income-section">
                    <h4>المصروفات</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>الحساب</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(account => `
                                <tr>
                                    <td>${account.name}</td>
                                    <td class="negative">${formatCurrency(account.balance)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>إجمالي المصروفات</strong></td>
                                <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary-card">
                    <div class="summary-title">صافي الدخل</div>
                    <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                        ${formatCurrency(netIncome)}
                    </div>
                </div>
            </div>
        `;
        
        console.log('✅ تم إنشاء قائمة الدخل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة الدخل:', error);
        if (incomeStatement) {
            incomeStatement.innerHTML = '<div class="error">خطأ في إنشاء التقرير</div>';
        }
    }
}

/**
 * إنشاء دفتر الأستاذ العام
 */
function generateGeneralLedger() {
    try {
        // حساب الأرصدة
        calculateAccountBalances();
        
        // الحصول على جميع الحسابات
        const allAccounts = accounts.filter(acc => acc.type !== 'header');
        
        // إنشاء HTML للتقرير
        let htmlContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>الأستاذ العام - ${new Date().toLocaleDateString('ar-SA')}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .date { font-size: 14px; color: #666; }
                    .ledger { max-width: 1200px; margin: 0 auto; }
                    .account-section { margin-bottom: 40px; page-break-inside: avoid; }
                    .account-header { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
                    .account-title { font-size: 18px; font-weight: bold; color: #333; }
                    .account-info { font-size: 14px; color: #666; margin-top: 5px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 10px; text-align: right; font-size: 14px; }
                    th { background-color: #e9ecef; font-weight: bold; }
                    .debit { color: #28a745; }
                    .credit { color: #dc3545; }
                    .balance { font-weight: bold; }
                    .positive { color: #28a745; }
                    .negative { color: #dc3545; }
                    .no-transactions { text-align: center; color: #666; font-style: italic; padding: 20px; }
                    @media print { .account-section { page-break-inside: avoid; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="title">الأستاذ العام</div>
                    <div class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
                </div>
                
                <div class="ledger">
        `;
        
        // إضافة كل حساب
        allAccounts.forEach(account => {
            // الحصول على جميع المعاملات للحساب
            const accountTransactions = journalEntries.flatMap(entry => 
                entry.entries.filter(transaction => transaction.accountId === account.id)
                .map(transaction => ({
                    date: entry.date,
                    description: entry.description,
                    reference: entry.id,
                    debit: transaction.debit,
                    credit: transaction.credit
                }))
            );
            
            // حساب الرصيد الجاري
            let runningBalance = 0;
            const transactionsWithBalance = accountTransactions.map(transaction => {
                if (account.type === 'asset' || account.type === 'expense') {
                    runningBalance += transaction.debit - transaction.credit;
                } else {
                    runningBalance += transaction.credit - transaction.debit;
                }
                return { ...transaction, balance: runningBalance };
            });
            
            htmlContent += `
                <div class="account-section">
                    <div class="account-header">
                        <div class="account-title">${account.name}</div>
                        <div class="account-info">
                            رقم الحساب: ${account.id} | نوع الحساب: ${getAccountTypeName(account.type)} | 
                            الرصيد الحالي: <span class="${account.balance >= 0 ? 'positive' : 'negative'}">${account.balance.toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</span>
                        </div>
                    </div>
                    
                    ${transactionsWithBalance.length > 0 ? `
                        <table>
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>المرجع</th>
                                    <th>مدين</th>
                                    <th>دائن</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactionsWithBalance.map(transaction => `
                                    <tr>
                                        <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                                        <td>${transaction.description}</td>
                                        <td>${transaction.reference}</td>
                                        <td class="debit">${transaction.debit > 0 ? transaction.debit.toLocaleString('ar-SA', { minimumFractionDigits: 2 }) : '-'}</td>
                                        <td class="credit">${transaction.credit > 0 ? transaction.credit.toLocaleString('ar-SA', { minimumFractionDigits: 2 }) : '-'}</td>
                                        <td class="balance ${transaction.balance >= 0 ? 'positive' : 'negative'}">${transaction.balance.toLocaleString('ar-SA', { minimumFractionDigits: 2 })}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : `
                        <div class="no-transactions">لا توجد معاملات لهذا الحساب</div>
                    `}
                </div>
            `;
        });
        
        htmlContent += `
                </div>
            </body>
            </html>
        `;
        
        // إنشاء ملف HTML للتحميل
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `general-ledger-${new Date().toISOString().split('T')[0]}.html`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        console.log('📊 تم إنشاء الأستاذ العام بنجاح');
        alert('تم إنشاء الأستاذ العام بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الأستاذ العام:', error);
        alert('خطأ في إنشاء الأستاذ العام');
    }
}

/**
 * عرض التقرير في نافذة منبثقة
 */
function showReportModal(reportType) {
    try {
        // الحصول على بيانات الشركة باستخدام الدالة المساعدة
        const companySettings = getCompanyData();
        const companyName = companySettings.name;
        const companyLogo = companySettings.logo;
        
        console.log('🏢 اسم الشركة المستخدم في التقرير:', companyName);
        console.log('🖼️ شعار الشركة:', companyLogo);
        
        // إنشاء محتوى التقرير
        let reportTitle = '';
        let reportContent = '';
        let reportData = null;
        
        switch(reportType) {
            case 'balance-sheet':
                reportTitle = 'الميزانية العمومية';
                reportData = generateBalanceSheetData();
                break;
            case 'income-statement':
                reportTitle = 'قائمة الدخل';
                reportData = generateIncomeStatementData();
                break;
            case 'cash-flow':
                reportTitle = 'قائمة التدفق النقدي';
                reportData = generateCashFlowData();
                break;
            case 'trial-balance':
                reportTitle = 'ميزان المراجعة';
                reportData = generateTrialBalanceData();
                break;
            case 'general-ledger':
                reportTitle = 'الأستاذ العام';
                reportData = generateGeneralLedgerData();
                break;
            default:
                console.error('❌ نوع تقرير غير معروف:', reportType);
                return;
        }
        
        // إنشاء النافذة المنبثقة
        const modal = document.createElement('div');
        modal.className = 'report-modal';
        modal.innerHTML = `
            <div class="report-modal-content">
                <div class="report-modal-header">
                    <div class="report-header-info">
                        <h2><i class="fas fa-chart-line"></i> ${reportTitle}</h2>
                        <div class="report-meta">
                            <span><i class="fas fa-calendar"></i> ${new Date().toLocaleDateString('ar-SA')}</span>
                            <span><i class="fas fa-clock"></i> ${new Date().toLocaleTimeString('ar-SA')}</span>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="btn-report" onclick="printReportFromModal('${reportType}')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn-report" onclick="downloadReportAsPDF('${reportType}')" title="تحميل PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                        <button class="btn-report" onclick="downloadReportAsExcel('${reportType}')" title="تحميل Excel">
                            <i class="fas fa-file-excel"></i>
                        </button>
                        <button class="btn-report close-btn" onclick="closeReportModal()" title="إغلاق">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="report-modal-body">
                    <div class="report-company-header">
                        ${companyLogo ? `<img src="${companyLogo}" alt="شعار الشركة" class="company-logo">` : ''}
                        <div class="company-details">
                            <h3>${companyName}</h3>
                            <p>${companySettings.address || 'عنوان الشركة'}</p>
                            <p>هاتف: ${companySettings.phone || '--'} | بريد: ${companySettings.email || '--'}</p>
                        </div>
                    </div>
                    
                    <div class="report-content" id="report-content">
                        ${reportData}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // إضافة تأثير الظهور
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        console.log(`📊 تم فتح تقرير ${reportTitle} بنجاح`);
        
    } catch (error) {
        console.error('❌ خطأ في عرض التقرير:', error);
        alert('خطأ في عرض التقرير');
    }
}

/**
 * إغلاق نافذة التقرير
 */
function closeReportModal() {
    const modal = document.querySelector('.report-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

/**
 * طباعة التقرير من النافذة المنبثقة
 */
function printReportFromModal(reportType) {
    try {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;
        
        const printWindow = window.open('', '_blank');
        
        // قراءة بيانات الشركة باستخدام الدالة المساعدة
        const companySettings = getCompanyData();
        const companyName = companySettings.name;
        console.log('🏢 اسم الشركة المستخدم في الطباعة:', companyName);
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير مالي - ${companyName}</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
                    
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Cairo', sans-serif;
                        direction: rtl;
                        background: white;
                        color: #1e293b;
                        line-height: 1.6;
                    }
                    
                    .print-header {
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #e2e8f0;
                    }
                    
                    .print-title {
                        font-size: 24px;
                        font-weight: 800;
                        color: #1e293b;
                        margin-bottom: 10px;
                    }
                    
                    .print-meta {
                        font-size: 14px;
                        color: #64748b;
                        margin-bottom: 15px;
                    }
                    
                    .company-info {
                        background: #f8fafc;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 20px;
                    }
                    
                    .company-name {
                        font-size: 18px;
                        font-weight: 700;
                        color: #1e293b;
                        margin-bottom: 5px;
                    }
                    
                    .company-details {
                        font-size: 14px;
                        color: #64748b;
                    }
                    
                    .report-section {
                        margin-bottom: 30px;
                    }
                    
                    .section-title {
                        font-size: 18px;
                        font-weight: 700;
                        color: #1e293b;
                        margin-bottom: 15px;
                        padding-bottom: 8px;
                        border-bottom: 2px solid #e2e8f0;
                    }
                    
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                        font-size: 14px;
                    }
                    
                    th, td {
                        border: 1px solid #e2e8f0;
                        padding: 12px;
                        text-align: right;
                    }
                    
                    th {
                        background: #f1f5f9;
                        font-weight: 600;
                        color: #1e293b;
                    }
                    
                    .debit { color: #059669; font-weight: 600; }
                    .credit { color: #dc2626; font-weight: 600; }
                    .positive { color: #059669; }
                    .negative { color: #dc2626; }
                    .total-row { 
                        background: #f8fafc; 
                        font-weight: 700; 
                        color: #1e293b;
                    }
                    
                    .summary-card {
                        background: #f8fafc;
                        border: 1px solid #e2e8f0;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 15px;
                    }
                    
                    .summary-title {
                        font-size: 16px;
                        font-weight: 600;
                        color: #1e293b;
                        margin-bottom: 10px;
                    }
                    
                    .summary-value {
                        font-size: 18px;
                        font-weight: 700;
                        color: #059669;
                    }
                    
                    .footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 12px;
                        color: #64748b;
                        border-top: 1px solid #e2e8f0;
                        padding-top: 20px;
                    }
                    
                    @media print {
                        body { margin: 20px; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <div class="print-title">تقرير مالي</div>
                    <div class="print-meta">
                        تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} | 
                        وقت الطباعة: ${new Date().toLocaleTimeString('ar-SA')}
                    </div>
                </div>
                
                <div class="company-info">
                    <div class="company-name">${companyName}</div>
                    <div class="company-details">
                        ${companySettings.address || 'عنوان الشركة'} | 
                        هاتف: ${companySettings.phone || '--'} | 
                        بريد: ${companySettings.email || '--'}
                    </div>
                </div>
                
                <div class="report-content">
                    ${reportContent.innerHTML}
                </div>
                
                <div class="footer">
                    <div>تم إنشاء هذا التقرير بواسطة Glass ERP System</div>
                    <div>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</div>
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
        
        console.log('🖨️ تم فتح نافذة الطباعة بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في الطباعة:', error);
        alert('خطأ في الطباعة');
    }
}

/**
 * تحميل التقرير كملف PDF
 */
function downloadReportAsPDF(reportType) {
    try {
        // هنا يمكن إضافة مكتبة لتحويل HTML إلى PDF
        // مثل jsPDF أو html2pdf.js
        alert('سيتم إضافة ميزة تحميل PDF قريباً');
        console.log('📄 تحميل PDF:', reportType);
    } catch (error) {
        console.error('❌ خطأ في تحميل PDF:', error);
        alert('خطأ في تحميل PDF');
    }
}

/**
 * تحميل التقرير كملف Excel
 */
function downloadReportAsExcel(reportType) {
    try {
        // هنا يمكن إضافة مكتبة لتحويل البيانات إلى Excel
        // مثل SheetJS أو xlsx
        alert('سيتم إضافة ميزة تحميل Excel قريباً');
        console.log('📊 تحميل Excel:', reportType);
    } catch (error) {
        console.error('❌ خطأ في تحميل Excel:', error);
        alert('خطأ في تحميل Excel');
    }
}

/**
 * إنشاء بيانات الميزانية العمومية
 */
function generateBalanceSheetData() {
    try {
        calculateAccountBalances();
        
        const assets = accounts.filter(acc => acc.type === 'asset' && acc.balance !== 0);
        const liabilities = accounts.filter(acc => acc.type === 'liability' && acc.balance !== 0);
        const equity = accounts.filter(acc => acc.type === 'equity' && acc.balance !== 0);
        
        const totalAssets = assets.reduce((sum, acc) => sum + acc.balance, 0);
        const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.balance, 0);
        const totalEquity = equity.reduce((sum, acc) => sum + acc.balance, 0);
        
        return `
            <div class="report-section">
                <div class="section-title">الأصول</div>
                <table>
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${assets.map(acc => `
                            <tr>
                                <td>${acc.name}</td>
                                <td class="positive">${formatCurrency(acc.balance)}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>إجمالي الأصول</strong></td>
                            <td><strong class="positive">${formatCurrency(totalAssets)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="report-section">
                <div class="section-title">الخصوم</div>
                <table>
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${liabilities.map(acc => `
                            <tr>
                                <td>${acc.name}</td>
                                <td class="negative">${formatCurrency(acc.balance)}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>إجمالي الخصوم</strong></td>
                            <td><strong class="negative">${formatCurrency(totalLiabilities)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="report-section">
                <div class="section-title">حقوق الملكية</div>
                <table>
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${equity.map(acc => `
                            <tr>
                                <td>${acc.name}</td>
                                <td class="positive">${formatCurrency(acc.balance)}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>إجمالي حقوق الملكية</strong></td>
                            <td><strong class="positive">${formatCurrency(totalEquity)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="summary-card">
                <div class="summary-title">التحقق من التوازن</div>
                <div class="summary-value">
                    الأصول = الخصوم + حقوق الملكية<br>
                    ${formatCurrency(totalAssets)} = ${formatCurrency(totalLiabilities)} + ${formatCurrency(totalEquity)}
                </div>
            </div>
        `;
    } catch (error) {
        console.error('❌ خطأ في إنشاء بيانات الميزانية العمومية:', error);
        return '<div class="error">خطأ في إنشاء التقرير</div>';
    }
}

/**
 * إنشاء بيانات قائمة الدخل
 */
function generateIncomeStatementData() {
    try {
        calculateAccountBalances();
        
        const revenues = accounts.filter(acc => acc.type === 'revenue' && acc.balance !== 0);
        const expenses = accounts.filter(acc => acc.type === 'expense' && acc.balance !== 0);
        
        const totalRevenue = revenues.reduce((sum, acc) => sum + acc.balance, 0);
        const totalExpenses = expenses.reduce((sum, acc) => sum + acc.balance, 0);
        const netIncome = totalRevenue - totalExpenses;
        
        return `
            <div class="report-section">
                <div class="section-title">الإيرادات</div>
                <table>
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${revenues.map(acc => `
                            <tr>
                                <td>${acc.name}</td>
                                <td class="positive">${formatCurrency(acc.balance)}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>إجمالي الإيرادات</strong></td>
                            <td><strong class="positive">${formatCurrency(totalRevenue)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="report-section">
                <div class="section-title">المصروفات</div>
                <table>
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${expenses.map(acc => `
                            <tr>
                                <td>${acc.name}</td>
                                <td class="negative">${formatCurrency(acc.balance)}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>إجمالي المصروفات</strong></td>
                            <td><strong class="negative">${formatCurrency(totalExpenses)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="summary-card">
                <div class="summary-title">صافي الدخل</div>
                <div class="summary-value ${netIncome >= 0 ? 'positive' : 'negative'}">
                    ${formatCurrency(Math.abs(netIncome))} ${netIncome >= 0 ? '(ربح)' : '(خسارة)'}
                </div>
            </div>
        `;
    } catch (error) {
        console.error('❌ خطأ في إنشاء بيانات قائمة الدخل:', error);
        return '<div class="error">خطأ في إنشاء التقرير</div>';
    }
}

/**
 * إنشاء بيانات قائمة التدفق النقدي
 */
function generateCashFlowData() {
    try {
        calculateAccountBalances();
        
        // تحليل المعاملات النقدية
        const cashTransactions = journalEntries.flatMap(entry => 
            entry.entries.filter(transaction => 
                transaction.accountId === '1111' // النقد
            ).map(transaction => ({
                date: entry.date,
                description: entry.description,
                type: transaction.debit > 0 ? 'inflow' : 'outflow',
                amount: transaction.debit > 0 ? transaction.debit : transaction.credit
            }))
        );
        
        const operatingActivities = cashTransactions.filter(t => 
            t.description.includes('مبيعات') || 
            t.description.includes('مصروفات') || 
            t.description.includes('رواتب') ||
            t.description.includes('إيجار')
        );
        
        const investingActivities = cashTransactions.filter(t => 
            t.description.includes('معدات') || 
            t.description.includes('مباني') ||
            t.description.includes('استثمار')
        );
        
        const financingActivities = cashTransactions.filter(t => 
            t.description.includes('رأس المال') || 
            t.description.includes('قرض') ||
            t.description.includes('توزيع')
        );
        
        const totalOperating = operatingActivities.reduce((sum, t) => 
            sum + (t.type === 'inflow' ? t.amount : -t.amount), 0
        );
        
        const totalInvesting = investingActivities.reduce((sum, t) => 
            sum + (t.type === 'inflow' ? t.amount : -t.amount), 0
        );
        
        const totalFinancing = financingActivities.reduce((sum, t) => 
            sum + (t.type === 'inflow' ? t.amount : -t.amount), 0
        );
        
        const netCashFlow = totalOperating + totalInvesting + totalFinancing;
        
        return `
            <div class="report-section">
                <div class="section-title">الأنشطة التشغيلية</div>
                <table>
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${operatingActivities.map(activity => `
                            <tr>
                                <td>${activity.description}</td>
                                <td>${new Date(activity.date).toLocaleDateString('ar-SA')}</td>
                                <td class="${activity.type === 'inflow' ? 'positive' : 'negative'}">
                                    ${activity.type === 'inflow' ? '+' : '-'}${formatCurrency(activity.amount)}
                                </td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="2"><strong>صافي التدفق النقدي من الأنشطة التشغيلية</strong></td>
                            <td><strong class="${totalOperating >= 0 ? 'positive' : 'negative'}">${formatCurrency(totalOperating)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="report-section">
                <div class="section-title">الأنشطة الاستثمارية</div>
                <table>
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${investingActivities.map(activity => `
                            <tr>
                                <td>${activity.description}</td>
                                <td>${new Date(activity.date).toLocaleDateString('ar-SA')}</td>
                                <td class="${activity.type === 'inflow' ? 'positive' : 'negative'}">
                                    ${activity.type === 'inflow' ? '+' : '-'}${formatCurrency(activity.amount)}
                                </td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="2"><strong>صافي التدفق النقدي من الأنشطة الاستثمارية</strong></td>
                            <td><strong class="${totalInvesting >= 0 ? 'positive' : 'negative'}">${formatCurrency(totalInvesting)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="report-section">
                <div class="section-title">الأنشطة التمويلية</div>
                <table>
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${financingActivities.map(activity => `
                            <tr>
                                <td>${activity.description}</td>
                                <td>${new Date(activity.date).toLocaleDateString('ar-SA')}</td>
                                <td class="${activity.type === 'inflow' ? 'positive' : 'negative'}">
                                    ${activity.type === 'inflow' ? '+' : '-'}${formatCurrency(activity.amount)}
                                </td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="2"><strong>صافي التدفق النقدي من الأنشطة التمويلية</strong></td>
                            <td><strong class="${totalFinancing >= 0 ? 'positive' : 'negative'}">${formatCurrency(totalFinancing)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="summary-card">
                <div class="summary-title">صافي التدفق النقدي</div>
                <div class="summary-value ${netCashFlow >= 0 ? 'positive' : 'negative'}">
                    ${formatCurrency(Math.abs(netCashFlow))} ${netCashFlow >= 0 ? '(زيادة)' : '(نقصان)'}
                </div>
            </div>
        `;
    } catch (error) {
        console.error('❌ خطأ في إنشاء بيانات قائمة التدفق النقدي:', error);
        return '<div class="error">خطأ في إنشاء التقرير</div>';
    }
}

/**
 * حذف قيد يومي
 */
function deleteJournalEntry(journalId) {
    if (!confirm('هل أنت متأكد من حذف هذا القيد؟')) {
        return;
    }
    
    // البحث عن القيد وحذفه
    const entryIndex = journalEntries.findIndex(entry => entry.id === journalId);
    if (entryIndex === -1) {
        showSimpleNotification('القيد غير موجود', 'error');
        return;
    }
    
    // حذف القيد
    journalEntries.splice(entryIndex, 1);
    
    // حفظ البيانات فوراً
    saveJournalEntries();
    
    // تحديث الواجهة
    loadJournalEntries();
    calculateAccountBalances();
    updateAccountStats();
    
    showSimpleNotification('تم حذف القيد بنجاح', 'success');
    console.log('🗑️ تم حذف القيد:', journalId);
}

/**
 * حفظ جميع البيانات
 */
function saveAllData() {
    const accountsSaved = saveAccountsData();
    const journalSaved = saveJournalEntries();
    
    if (accountsSaved && journalSaved) {
        console.log('✅ تم حفظ جميع البيانات بنجاح');
        return true;
    } else {
        console.error('❌ فشل في حفظ بعض البيانات');
        return false;
    }
}

/**
 * تحميل جميع البيانات
 */
function loadAllData() {
    const accountsLoaded = loadAccountsData();
    const journalLoaded = loadJournalEntriesData();
    
    if (accountsLoaded && journalLoaded) {
        console.log('✅ تم تحميل جميع البيانات بنجاح');
        return true;
    } else {
        console.error('❌ فشل في تحميل بعض البيانات');
        return false;
    }
}

/**
 * مسح جميع البيانات (مع تأكيد)
 */
function clearAllData() {
    if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }
    
    if (!confirm('تأكيد نهائي: هل تريد مسح جميع البيانات؟')) {
        return;
    }
    
    try {
        localStorage.removeItem('glassERP_accounts');
        localStorage.removeItem('glassERP_journalEntries');
        
        accounts = [];
        journalEntries = [];
        
        // إعادة إنشاء شجرة الحسابات الافتراضية
        accounts = createDefaultChartOfAccounts();
        saveAccountsData();
        
        // تحديث الواجهة
        loadChartOfAccounts();
        loadJournalEntries();
        updateAccountStats();
        
        showSimpleNotification('تم مسح جميع البيانات وإعادة تعيين النظام', 'success');
        console.log('🗑️ تم مسح جميع البيانات');
    } catch (error) {
        console.error('❌ خطأ في مسح البيانات:', error);
        showSimpleNotification('خطأ في مسح البيانات', 'error');
    }
}