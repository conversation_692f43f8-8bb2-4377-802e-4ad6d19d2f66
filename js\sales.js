/**
 * Sales Module - موديول المبيعات
 * ========================================
 */

// متغيرات المبيعات
let salesData = {
    invoices: [],
    customers: [],
    services: [],
    nextInvoiceNumber: 1,
    defaultAccounts: {
        customers: '120000', // ح/ العملاء
        sales: '400000',     // ح/ المبيعات
        salesReturns: '400100' // ح/ مردودات المبيعات
    }
};

// تهيئة قاعدة بيانات SQLite للمبيعات
let salesDB = null;
async function initSalesDB() {
    if (salesDB) return salesDB;
    if (!window.initSqlJs) throw new Error('sql.js library is not loaded!');
    const SQL = await window.initSqlJs({ locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}` });
    salesDB = new SQL.Database();
    // إنشاء الجداول المطلوبة (مثال: invoices)
    salesDB.run(`CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT,
        invoiceNumber TEXT,
        customer TEXT,
        total REAL
    );`);
    return salesDB;
}
window.initSalesDB = initSalesDB;

/**
 * تهيئة موديول المبيعات
 */
async function initializeSales() {
    console.log('🛒 تهيئة موديول المبيعات...');
    
    // تحميل البيانات المحفوظة
    await loadSalesData();
    
    // إنشاء محتوى المبيعات
    createSalesContent();
    
    // ربط الأحداث
    bindSalesEvents();
    
    console.log('✅ تم تهيئة موديول المبيعات بنجاح');
}

/**
 * تحميل بيانات المبيعات
 */
async function loadSalesData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // تحميل الفواتير
            const savedInvoices = await sqliteDB.getAllInvoices();
            if (savedInvoices) {
                salesData.invoices = savedInvoices;
                console.log('📋 تم تحميل الفواتير:', salesData.invoices.length);
            }
            
            // تحميل العملاء
            const savedCustomers = await sqliteDB.getAllCustomers();
            if (savedCustomers) {
                salesData.customers = savedCustomers;
                console.log('👥 تم تحميل العملاء:', salesData.customers.length);
            }
            
            // تحميل الخدمات
            const savedServices = await sqliteDB.getAllServices();
            if (savedServices) {
                salesData.services = savedServices;
                console.log('🔧 تم تحميل الخدمات:', salesData.services.length);
            }
            
            // حساب رقم الفاتورة التالي
            if (salesData.invoices.length > 0) {
                const lastInvoice = salesData.invoices[salesData.invoices.length - 1];
                const lastNumber = parseInt(lastInvoice.number.replace('S-', ''));
                salesData.nextInvoiceNumber = lastNumber + 1;
            }
            
            console.log('✅ تم تحميل بيانات المبيعات بنجاح');
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات المبيعات:', error);
    }
}

/**
 * حفظ بيانات المبيعات
 */
async function saveSalesData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.saveAllInvoices(salesData.invoices);
            await sqliteDB.saveAllCustomers(salesData.customers);
            await sqliteDB.saveAllServices(salesData.services);
            console.log('💾 تم حفظ بيانات المبيعات');
            return true;
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ بيانات المبيعات:', error);
        return false;
    }
}

/**
 * إنشاء محتوى المبيعات
 */
function createSalesContent() {
    const salesContainer = document.getElementById('sales');
    salesContainer.innerHTML = '';
}

function getCompanyDefaultAccountsOptions() {
    let accounts = [];
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // سيتم تحديث هذه الدالة لاستخدام SQLite
            console.log('⚠️ تحتاج إلى تحديث getCompanyDefaultAccountsOptions لاستخدام SQLite');
        }
    } catch ( error) { 
        console.error('❌ خطأ في جلب الحسابات:', error);
    }
    return accounts.map(acc => `<option value="${acc.id}">${acc.id} - ${acc.name}</option>`).join('');
}

function getInvoiceFormHTML() {
    // جلب الحسابات الافتراضية من الإعدادات
    let settings = {};
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // سيتم تحديث هذه الدالة لاستخدام SQLite
            console.log('⚠️ تحتاج إلى تحديث getInvoiceFormHTML لاستخدام SQLite');
        }
    } catch { settings = {}; }
    const accountOptions = getCompanyDefaultAccountsOptions();
    return `
    <div class="invoice-form">
        <!-- ... باقي بيانات الفاتورة ... -->
        <div class="invoice-accounts">
            <h4><i class="fas fa-university"></i> حسابات الترحيل المبيعات</h4>
            <div class="accounts-fields">
                <div class="form-group">
                    <label>من ح/</label>
                    <select id="invoice-sales-posting-from">${accountOptions}</select>
                </div>
                <div class="form-group">
                    <label>إلى ح/</label>
                    <select id="invoice-sales-posting-to">${accountOptions}</select>
                </div>
            </div>
            <h4><i class="fas fa-random"></i> حسابات الترحيل مرتجعات المبيعات</h4>
            <div class="accounts-fields">
                <div class="form-group">
                    <label>من ح/</label>
                    <select id="invoice-sales-returns-posting-from">${accountOptions}</select>
                </div>
                <div class="form-group">
                    <label>إلى ح/</label>
                    <select id="invoice-sales-returns-posting-to">${accountOptions}</select>
                </div>
            </div>
        </div>
        <!-- ... باقي واجهة الفاتورة ... -->
    </div>
    `;
}

// عند فتح تبويب الفاتورة أو إنشاء فاتورة جديدة، عيّن القيم الافتراضية من الإعدادات
async function setInvoiceDefaultAccounts() {
    let settings = {};
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            settings = await sqliteDB.getCompanySettings();
        }
    } catch { settings = {}; }
    if (document.getElementById('invoice-sales-posting-from'))
        document.getElementById('invoice-sales-posting-from').value = settings.salesPostingFrom || '';
    if (document.getElementById('invoice-sales-posting-to'))
        document.getElementById('invoice-sales-posting-to').value = settings.salesPostingTo || '';
    if (document.getElementById('invoice-sales-returns-posting-from'))
        document.getElementById('invoice-sales-returns-posting-from').value = settings.salesReturnsPostingFrom || '';
    if (document.getElementById('invoice-sales-returns-posting-to'))
        document.getElementById('invoice-sales-returns-posting-to').value = settings.salesReturnsPostingTo || '';
}

function getServicesTableHTML() {
    return `
    <div class="services-section">
        <div class="services-controls">
            <input type="text" id="searchService" placeholder="بحث عن خدمة...">
            <button id="addServiceBtn">إضافة خدمة</button>
        </div>
        <table id="servicesTable">
            <thead>
                <tr>
                    <th>اسم الخدمة</th>
                    <th>الوصف</th>
                    <th>السعر</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <!-- سيتم تعبئة الخدمات هنا -->
            </tbody>
        </table>
    </div>
    `;
}

function getServiceModalHTML() {
    return `
    <div id="serviceModal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close" id="closeServiceModal">&times;</span>
            <h2 id="serviceModalTitle">إضافة خدمة</h2>
            <form id="serviceForm">
                <input type="hidden" id="serviceId">
                <label>اسم الخدمة:</label>
                <input type="text" id="serviceName" required>
                <label>الوصف:</label>
                <input type="text" id="serviceDesc">
                <label>السعر:</label>
                <input type="number" id="servicePrice" min="0" step="0.01">
                <button type="submit" id="saveServiceBtn">حفظ</button>
            </form>
        </div>
    </div>
    `;
}

function getCustomersTableHTML() {
    return `
    <div class="customers-section">
        <div class="customers-controls">
            <input type="text" id="searchCustomer" placeholder="بحث عن عميل...">
            <button id="addCustomerBtn">إضافة عميل</button>
        </div>
        <table id="customersTable">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>العنوان</th>
                    <th>البريد الإلكتروني</th>
                    <th>ملاحظات</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <!-- سيتم تعبئة العملاء هنا -->
            </tbody>
        </table>
    </div>
    `;
}

function getCustomerModalHTML() {
    return `
    <div id="customerModal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close" id="closeCustomerModal">&times;</span>
            <h2 id="customerModalTitle">إضافة عميل</h2>
            <form id="customerForm">
                <input type="hidden" id="customerId">
                <label>الاسم:</label>
                <input type="text" id="customerName" required>
                <label>الهاتف:</label>
                <input type="text" id="customerPhone">
                <label>العنوان:</label>
                <input type="text" id="customerAddress">
                <label>البريد الإلكتروني:</label>
                <input type="email" id="customerEmail">
                <label>ملاحظات:</label>
                <textarea id="customerNotes"></textarea>
                <button type="submit" id="saveCustomerBtn">حفظ</button>
            </form>
        </div>
    </div>
    `;
}

/**
 * ربط أحداث المبيعات
 */
function bindSalesEvents() {
    // أحداث نموذج العميل
    const customerForm = document.getElementById('customer-form');
    if (customerForm) {
        customerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveCustomer();
        });
    }
    
    // أحداث نموذج الخدمة
    const serviceForm = document.getElementById('service-form');
    if (serviceForm) {
        serviceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveService();
        });
    }
    
    console.log('✅ تم ربط أحداث المبيعات');
}

/**
 * تحميل قائمة الفواتير
 */
function loadInvoicesList() {
    const invoicesList = document.getElementById('invoices-list');
    if (!invoicesList) return;
    
    if (salesData.invoices.length === 0) {
        invoicesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-file-invoice"></i>
                <h3>لا توجد فواتير</h3>
                <p>ابدأ بإنشاء فاتورة جديدة</p>
                <button class="btn-primary" onclick="createNewInvoice()">
                    <i class="fas fa-plus"></i> إنشاء فاتورة
                </button>
            </div>
        `;
        return;
    }
    
    const invoicesHTML = salesData.invoices.map(invoice => `
        <div class="invoice-card" onclick="openInvoice('${invoice.id}')">
            <div class="invoice-header">
                <h4>${invoice.number}</h4>
                <span class="status ${invoice.status}">${getStatusText(invoice.status)}</span>
            </div>
            <div class="invoice-details">
                <p><strong>العميل:</strong> ${invoice.customerName}</p>
                <p><strong>التاريخ:</strong> ${formatDate(invoice.date)}</p>
                <p><strong>المبلغ:</strong> ${formatNumber(invoice.total)}</p>
                <p><strong>المدفوع:</strong> ${formatNumber(invoice.paid)}</p>
            </div>
            <div class="invoice-actions">
                <button class="btn-small" onclick="event.stopPropagation(); printInvoice('${invoice.id}')">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn-small" onclick="event.stopPropagation(); editInvoice('${invoice.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-small danger" onclick="event.stopPropagation(); deleteInvoice('${invoice.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    invoicesList.innerHTML = invoicesHTML;
}

/**
 * إنشاء فاتورة جديدة
 */
function createNewInvoice() {
    const modal = document.getElementById('invoice-modal');
    if (modal) {
        modal.style.display = 'block';
        
        // تعيين رقم الفاتورة
        const invoiceNumber = document.getElementById('invoice-number');
        if (invoiceNumber) {
            invoiceNumber.value = `S-${String(salesData.nextInvoiceNumber).padStart(6, '0')}`;
        }
        
        // إعادة تعيين النموذج
        resetInvoiceForm();
    }
}

/**
 * إعادة تعيين نموذج الفاتورة
 */
function resetInvoiceForm() {
    // إعادة تعيين الحقول
    document.getElementById('invoice-date').value = new Date().toISOString().split('T')[0];
    document.getElementById('invoice-status').value = 'draft';
    document.getElementById('invoice-customer').value = '';
    document.getElementById('customer-details').innerHTML = '';
    
    // مسح الجدول
    document.getElementById('items-tbody').innerHTML = '';
    
    // مسح الخدمات
    document.getElementById('services-list').innerHTML = '';
    
    // إعادة تعيين الحسابات
    calculateInvoiceTotal();
    
    // عبئ القوائم من الإعدادات
    fillInvoiceAccountsDropdowns();
}

/**
 * إضافة صف إلى جدول الأصناف
 */
function addInvoiceItem() {
    const tbody = document.getElementById('items-tbody');
    const itemId = Date.now();
    // جلب الخدمات من localStorage أو salesData
    let services = [];
    try {
        services = JSON.parse(localStorage.getItem('glassERP_services') || '[]');
    } catch { services = []; }
    if (!services.length && salesData.services) services = salesData.services;
    // بناء خيارات الخدمات
    const serviceOptions = services.map(s => `<option value="${s.name}">${s.name}</option>`).join('');
    // بناء خيارات السمك
    let thicknessOptions = '';
    for (let i = 1; i <= 100; i += 0.5) {
        thicknessOptions += `<option value="${i.toFixed(1)}">${i.toFixed(1)} مم</option>`;
    }
    const row = `
        <tr id="item-${itemId}">
            <td><input type="text" class="item-code" placeholder="كود الصنف"></td>
            <td>
                <select class="item-service">
                    <option value="">اختر الخدمة</option>
                    ${serviceOptions}
                </select>
            </td>
            <td><input type="number" class="item-length" min="0" step="1" onchange="calculateItemArea(${itemId})"></td>
            <td><input type="number" class="item-width" min="0" step="1" onchange="calculateItemArea(${itemId})"></td>
            <td>
                <select class="item-thickness">
                    <option value="">سمك الزجاج</option>
                    ${thicknessOptions}
                </select>
            </td>
            <td><input type="number" class="item-quantity" min="1" value="1" onchange="calculateItemArea(${itemId})"></td>
            <td><span class="item-area">0.00</span></td>
            <td><input type="number" class="item-price" min="0" step="0.01" onchange="calculateItemTotal(${itemId})"></td>
            <td><span class="item-total">0.00</span></td>
            <td><input type="text" class="item-notes" placeholder="ملاحظات"></td>
            <td>
                <button type="button" class="btn-small danger" onclick="removeInvoiceItem(${itemId})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
        <tr class="item-desc-row"><td colspan="11"><input type="text" class="item-desc" placeholder="وصف إضافي لهذا الصنف" style="width:100%;font-size:15px;"></td></tr>
    `;
    tbody.insertAdjacentHTML('beforeend', row);
}

/**
 * حساب مساحة الصنف
 */
function calculateItemArea(itemId) {
    const row = document.getElementById(`item-${itemId}`);
    if (!row) return;
    
    const length = parseFloat(row.querySelector('.item-length').value) || 0;
    const width = parseFloat(row.querySelector('.item-width').value) || 0;
    const thickness = row.querySelector('.item-thickness').value;
    const quantity = parseInt(row.querySelector('.item-quantity').value) || 1;
    
    // حساب المتر المربع = (الطول × العرض × العدد) ÷ 1,000,000
    const area = (length * width * quantity) / 1000000;
    
    row.querySelector('.item-area').textContent = formatNumber(area);
    
    // إعادة حساب الإجمالي
    calculateItemTotal(itemId);
}

/**
 * حساب إجمالي الصنف
 */
function calculateItemTotal(itemId) {
    const row = document.getElementById(`item-${itemId}`);
    if (!row) return;
    
    const area = parseFloat(row.querySelector('.item-area').textContent) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    
    const total = area * price;
    row.querySelector('.item-total').textContent = formatNumber(total);
    
    // إعادة حساب إجمالي الفاتورة
    calculateInvoiceTotal();
}

/**
 * إزالة صف من الجدول
 */
function removeInvoiceItem(itemId) {
    const row = document.getElementById(`item-${itemId}`);
    if (row) {
        row.remove();
        calculateInvoiceTotal();
    }
}

/**
 * حساب إجمالي الفاتورة
 */
function calculateInvoiceTotal() {
    let totalArea = 0;
    let totalItems = 0;
    let totalServices = 0;
    
    // حساب إجمالي الأصناف
    const items = document.querySelectorAll('#items-tbody tr');
    items.forEach(item => {
        const area = parseFloat(item.querySelector('.item-area').textContent) || 0;
        const total = parseFloat(item.querySelector('.item-total').textContent) || 0;
        totalArea += area;
        totalItems += total;
    });
    
    // حساب إجمالي الخدمات
    const services = document.querySelectorAll('.service-item');
    services.forEach(service => {
        const price = parseFloat(service.querySelector('.service-price').textContent) || 0;
        totalServices += price;
    });
    
    // الحصول على الخصم والضريبة
    const discount = parseFloat(document.getElementById('invoice-discount').value) || 0;
    const tax = parseFloat(document.getElementById('invoice-tax').value) || 0;
    
    // حساب الإجمالي النهائي
    const subtotal = totalItems + totalServices;
    const afterDiscount = subtotal - discount;
    const total = afterDiscount + tax;
    
    // تحديث العرض
    document.getElementById('total-area').textContent = formatNumber(totalArea);
    document.getElementById('total-items').textContent = formatNumber(totalItems);
    document.getElementById('total-services').textContent = formatNumber(totalServices);
    document.getElementById('invoice-total').textContent = formatNumber(total);
    
    // حساب المتبقي
    calculateInvoiceRemaining();
}

/**
 * حساب المتبقي
 */
function calculateInvoiceRemaining() {
    const total = parseFloat(document.getElementById('invoice-total').textContent) || 0;
    const paid = parseFloat(document.getElementById('invoice-paid').value) || 0;
    const remaining = total - paid;
    
    document.getElementById('invoice-remaining').textContent = formatNumber(remaining);
}

/**
 * حفظ الفاتورة
 */
async function saveInvoice() {
    try {
        // جمع بيانات الفاتورة
        const invoiceData = {
            id: document.getElementById('invoice-id').value || 'inv_' + Date.now(),
            number: document.getElementById('invoice-number').value,
            date: document.getElementById('invoice-date').value,
            customerId: document.getElementById('invoice-customer').value,
            items: [],
            subtotal: parseFloat(document.getElementById('invoice-subtotal').value) || 0,
            tax: parseFloat(document.getElementById('invoice-tax').value) || 0,
            total: parseFloat(document.getElementById('invoice-total').value) || 0,
            paid: parseFloat(document.getElementById('invoice-paid').value) || 0,
            remaining: parseFloat(document.getElementById('invoice-remaining').value) || 0,
            status: document.getElementById('invoice-status').value,
            notes: document.getElementById('invoice-notes').value,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // جمع الأصناف
        const items = document.querySelectorAll('.invoice-item');
        items.forEach(item => {
            const itemData = {
                id: item.dataset.itemId,
                description: item.querySelector('.item-description').value,
                quantity: parseFloat(item.querySelector('.item-quantity').value) || 0,
                unitPrice: parseFloat(item.querySelector('.item-unit-price').value) || 0,
                total: parseFloat(item.querySelector('.item-total').value) || 0
            };
            invoiceData.items.push(itemData);
        });

        // حفظ الفاتورة
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.saveInvoice(invoiceData);
            
            // إنشاء قيد محاسبي
            await createAccountingEntry(invoiceData);
            
            // إضافة إشعار
            await addNotification(
                'تم حفظ فاتورة جديدة',
                `تم إنشاء فاتورة رقم ${invoiceData.number} بقيمة ${formatCurrency(invoiceData.total)}`,
                'success',
                'sales'
            );
            
            console.log('✅ تم حفظ الفاتورة بنجاح');
            closeInvoiceModal();
            loadInvoicesList();
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في حفظ الفاتورة:', error);
        showError('فشل في حفظ الفاتورة');
    }
}

/**
 * إنشاء قيد محاسبي للفاتورة
 */
async function createAccountingEntry(invoice) {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const entry = {
                id: 'je_' + Date.now(),
                number: 'JE' + String(Date.now()).slice(-6),
                date: invoice.date,
                description: `فاتورة مبيعات رقم ${invoice.number}`,
                entries: [
                    {
                        accountId: '120000', // ح/ العملاء
                        debit: invoice.total,
                        credit: 0,
                        description: `فاتورة مبيعات رقم ${invoice.number}`
                    },
                    {
                        accountId: '400000', // ح/ المبيعات
                        debit: 0,
                        credit: invoice.subtotal,
                        description: `مبيعات فاتورة رقم ${invoice.number}`
                    }
                ],
                totalDebit: invoice.total,
                totalCredit: invoice.total,
                status: 'posted',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await sqliteDB.addJournalEntry(entry);
            console.log('✅ تم إنشاء القيد المحاسبي للفاتورة');
        }
    } catch (error) {
        console.error('❌ خطأ في إنشاء القيد المحاسبي:', error);
    }
}

/**
 * إغلاق نافذة الفاتورة
 */
function closeInvoiceModal() {
    const modal = document.getElementById('invoice-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * عرض نافذة العملاء
 */
function showCustomersModal() {
    const modal = document.getElementById('customer-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * عرض نافذة إضافة عميل
 */
function showAddCustomerModal() {
    const modal = document.getElementById('customer-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * إغلاق نافذة العملاء
 */
function closeCustomerModal() {
    const modal = document.getElementById('customer-modal');
    if (modal) {
        modal.style.display = 'none';
        // مسح النموذج
        document.getElementById('customer-form').reset();
    }
}

/**
 * حفظ عميل جديد
 */
async function saveCustomer() {
    try {
        const customerData = {
            id: document.getElementById('customer-id').value || 'cust_' + Date.now(),
            name: document.getElementById('customer-name').value,
            phone: document.getElementById('customer-phone').value,
            email: document.getElementById('customer-email').value,
            address: document.getElementById('customer-address').value,
            taxNumber: document.getElementById('customer-tax-number').value,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.saveCustomer(customerData);
            console.log('✅ تم حفظ العميل بنجاح');
            closeCustomerModal();
            loadCustomersSelect();
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في حفظ العميل:', error);
        showError('فشل في حفظ العميل');
    }
}

/**
 * تحميل قائمة العملاء في select
 */
function loadCustomersSelect() {
    const select = document.getElementById('invoice-customer');
    if (!select) return;
    
    // مسح الخيارات الحالية
    select.innerHTML = '<option value="">اختر العميل</option>';
    
    // إضافة العملاء
    salesData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        select.appendChild(option);
    });
}

/**
 * تحميل معلومات العميل
 */
function loadCustomerInfo() {
    const customerId = document.getElementById('invoice-customer').value;
    const customerDetails = document.getElementById('customer-details');
    
    if (!customerId) {
        customerDetails.innerHTML = '';
        return;
    }
    
    const customer = salesData.customers.find(c => c.id === customerId);
    if (customer) {
        customerDetails.innerHTML = `
            <div class="customer-info-card">
                <p><strong>الهاتف:</strong> ${customer.phone}</p>
                <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                <p><strong>الرقم الضريبي:</strong> ${customer.taxNumber || 'غير محدد'}</p>
            </div>
        `;
    }
}

/**
 * عرض نافذة الخدمات
 */
function showServicesModal() {
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * عرض نافذة إضافة خدمة
 */
function showAddServiceModal() {
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * إغلاق نافذة الخدمات
 */
function closeServiceModal() {
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'none';
        // مسح النموذج
        const form = document.getElementById('service-form');
        form.reset();
        form.dataset.editMode = 'false';
        form.dataset.editId = '';
    }
}

/**
 * حفظ خدمة جديدة
 */
async function saveService() {
    try {
        const serviceData = {
            id: document.getElementById('service-id').value || 'serv_' + Date.now(),
            name: document.getElementById('service-name').value,
            description: document.getElementById('service-desc').value,
            price: parseFloat(document.getElementById('service-price').value) || 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.saveService(serviceData);
            console.log('✅ تم حفظ الخدمة بنجاح');
            closeServiceModal();
            loadServicesList();
        } else {
            console.error('❌ قاعدة بيانات SQLite غير متوفرة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في حفظ الخدمة:', error);
        showError('فشل في حفظ الخدمة');
    }
}

/**
 * تحميل قائمة الخدمات
 */
function loadServicesList() {
    const servicesList = document.getElementById('services-list');
    if (!servicesList) return;
    
    const servicesHTML = salesData.services.map(service => `
        <div class="service-item" onclick="addServiceToInvoice('${service.id}')">
            <div class="service-info">
                <span class="service-name">${service.name}</span>
                <span class="service-price">${formatNumber(service.price)}</span>
            </div>
            <button class="btn-small" onclick="event.stopPropagation(); editService('${service.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn-small danger" onclick="event.stopPropagation(); deleteService('${service.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');
    
    servicesList.innerHTML = servicesHTML;
}

/**
 * إضافة خدمة إلى الفاتورة
 */
function addServiceToInvoice(serviceId) {
    const service = salesData.services.find(s => s.id === serviceId);
    if (!service) return;
    
    const servicesList = document.getElementById('services-list');
    const serviceElement = document.createElement('div');
    serviceElement.className = 'service-item selected';
    serviceElement.innerHTML = `
        <div class="service-info">
            <span class="service-name">${service.name}</span>
            <span class="service-price">${formatNumber(service.price)}</span>
        </div>
        <button class="btn-small danger" onclick="removeServiceFromInvoice(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    servicesList.appendChild(serviceElement);
    
    // إعادة حساب الإجمالي
    calculateInvoiceTotal();
}

/**
 * إزالة خدمة من الفاتورة
 */
function removeServiceFromInvoice(button) {
    button.closest('.service-item').remove();
    calculateInvoiceTotal();
}

/**
 * تحرير خدمة
 */
function editService(serviceId) {
    const service = salesData.services.find(s => s.id === serviceId);
    if (!service) return;
    
    // ملء النموذج بالبيانات الحالية
    document.getElementById('service-name').value = service.name;
    document.getElementById('service-price').value = service.price;
    
    // عرض النافذة
    showAddServiceModal();
    
    // تغيير سلوك الحفظ ليكون تحديثاً
    const form = document.getElementById('service-form');
    form.dataset.editMode = 'true';
    form.dataset.editId = serviceId;
}

/**
 * حذف خدمة
 */
function deleteService(serviceId) {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
        const index = salesData.services.findIndex(s => s.id === serviceId);
        if (index !== -1) {
            salesData.services.splice(index, 1);
            saveSalesData();
            loadServicesList();
            alert('تم حذف الخدمة بنجاح');
        }
    }
}

/**
 * الحصول على نص الحالة
 */
function getStatusText(status) {
    const statusMap = {
        'draft': 'مبدئية',
        'final': 'نهائية',
        'cancelled': 'ملغاة'
    };
    return statusMap[status] || status;
}

/**
 * طباعة الفاتورة
 */
function printInvoice(invoiceId) {
    // تنفيذ الطباعة
    window.print();
}

/**
 * فتح الفاتورة للعرض
 */
function openInvoice(invoiceId) {
    const invoice = salesData.invoices.find(inv => inv.id === invoiceId);
    if (!invoice) {
        alert('الفاتورة غير موجودة');
        return;
    }
    
    // عرض تفاصيل الفاتورة في نافذة منفصلة
    const modal = document.getElementById('invoice-modal');
    if (modal) {
        modal.style.display = 'block';
        
        // ملء البيانات
        document.getElementById('invoice-number').value = invoice.number;
        document.getElementById('invoice-date').value = invoice.date;
        document.getElementById('invoice-status').value = invoice.status;
        document.getElementById('invoice-customer').value = invoice.customerId;
        
        // تحميل تفاصيل العميل
        loadCustomerInfo();
        
        // ملء الأصناف
        const tbody = document.getElementById('items-tbody');
        tbody.innerHTML = '';
        invoice.items.forEach(item => {
            const itemId = Date.now() + Math.random();
            const row = `
                <tr id="item-${itemId}">
                    <td><input type="text" class="item-code" value="${item.code}" readonly></td>
                    <td><input type="text" class="item-name" value="${item.name}" readonly></td>
                    <td><input type="number" class="item-length" value="${item.length}" readonly></td>
                    <td><input type="number" class="item-width" value="${item.width}" readonly></td>
                    <td><input type="number" class="item-thickness" value="${item.thickness}" readonly></td>
                    <td><input type="number" class="item-quantity" value="${item.quantity}" readonly></td>
                    <td><span class="item-area">${formatNumber(item.area)}</span></td>
                    <td><input type="number" class="item-price" value="${item.price}" readonly></td>
                    <td><span class="item-total">${formatNumber(item.total)}</span></td>
                    <td><input type="text" class="item-notes" value="${item.notes}" readonly></td>
                    <td></td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', row);
        });
        
        // ملء الخدمات
        const servicesList = document.getElementById('services-list');
        servicesList.innerHTML = '';
        invoice.services.forEach(service => {
            const serviceElement = document.createElement('div');
            serviceElement.className = 'service-item selected';
            serviceElement.innerHTML = `
                <div class="service-info">
                    <span class="service-name">${service.name}</span>
                    <span class="service-price">${formatNumber(service.price)}</span>
                </div>
            `;
            servicesList.appendChild(serviceElement);
        });
        
        // ملء الملخص
        document.getElementById('total-area').textContent = formatNumber(invoice.totalArea);
        document.getElementById('total-items').textContent = formatNumber(invoice.totalItems);
        document.getElementById('total-services').textContent = formatNumber(invoice.totalServices);
        document.getElementById('invoice-discount').value = invoice.discount;
        document.getElementById('invoice-tax').value = invoice.tax;
        document.getElementById('invoice-total').textContent = formatNumber(invoice.total);
        document.getElementById('invoice-paid').value = invoice.paid;
        document.getElementById('invoice-remaining').textContent = formatNumber(invoice.remaining);
        
        // تعطيل التحرير
        document.querySelectorAll('#invoice-modal input, #invoice-modal select').forEach(el => {
            el.disabled = true;
        });
        
        // إخفاء أزرار الإضافة
        document.querySelectorAll('.btn-secondary[onclick*="add"]').forEach(btn => {
            btn.style.display = 'none';
        });
    }
}

/**
 * تحرير الفاتورة
 */
function editInvoice(invoiceId) {
    // تنفيذ التحرير
    console.log('تحرير الفاتورة:', invoiceId);
}

/**
 * حذف الفاتورة
 */
function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        const index = salesData.invoices.findIndex(invoice => invoice.id === invoiceId);
        if (index !== -1) {
            salesData.invoices.splice(index, 1);
            saveSalesData();
            loadInvoicesList();
            alert('تم حذف الفاتورة بنجاح');
        }
    }
}

// عبئ القوائم من الإعدادات
function fillInvoiceAccountsDropdowns() {
    const settings = JSON.parse(localStorage.getItem('glassERP_companySettings') || '{}');
    const debit = document.getElementById('invoice-sales-posting-from');
    const credit = document.getElementById('invoice-sales-posting-to');
    const returnsFrom = document.getElementById('invoice-sales-returns-posting-from');
    const returnsTo = document.getElementById('invoice-sales-returns-posting-to');
    if (debit) debit.value = settings.salesPostingFrom || '';
    if (credit) credit.value = settings.salesPostingTo || '';
    if (returnsFrom) returnsFrom.value = settings.salesReturnsPostingFrom || '';
    if (returnsTo) returnsTo.value = settings.salesReturnsPostingTo || '';
}

// تصدير الدوال للاستخدام
window.SalesModule = {
    initializeSales,
    createNewInvoice,
    saveInvoice,
    loadSalesData,
    saveSalesData
};

// تهيئة المبيعات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قليل للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        if (document.getElementById('sales').classList.contains('active')) {
            initializeSales();
        }
    }, 100);
});

function bindSalesTabsEvents() {
    const tabBtns = document.querySelectorAll('.sales-tabs .tab-btn');
    const tabPanes = document.querySelectorAll('.sales-tabs .tab-pane');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            this.classList.add('active');
            document.getElementById(this.dataset.tab).classList.add('active');
            if (this.dataset.tab === 'invoice-tab') {
                updateInvoiceCustomerList();
                updateInvoiceServicesList();
                setInvoiceDefaultAccounts();
            }
        });
    });
}

function bindServicesSectionEvents() {
    const addServiceBtn = document.getElementById('addServiceBtn');
    if (addServiceBtn) {
        addServiceBtn.addEventListener('click', function() {
            showAddServiceModal();
        });
    }

    const closeServiceModalBtn = document.getElementById('closeServiceModal');
    if (closeServiceModalBtn) {
        closeServiceModalBtn.addEventListener('click', function() {
            closeServiceModal();
        });
    }

    const serviceForm = document.getElementById('serviceForm');
    if (serviceForm) {
        serviceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveService();
        });
    }

    // البحث
    const searchService = document.getElementById('searchService');
    if (searchService) {
        searchService.addEventListener('input', function(e) {
            renderServicesTable(e.target.value.trim());
        });
    }

    // عرض الخدمات عند التحميل
    renderServicesTable();
}

async function getServicesData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            return await sqliteDB.getAllServices();
        }
        return [];
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات الخدمات:', error);
        return [];
    }
}

function saveServicesData(services) {
    localStorage.setItem('glassERP_services', JSON.stringify(services));
}

function renderServicesTable(search = '') {
    const tbody = document.querySelector('#servicesTable tbody');
    if (!tbody) return;
    let services = getServicesData();
    if (search) {
        services = services.filter(s =>
            s.name.includes(search) || (s.desc && s.desc.includes(search))
        );
    }
    tbody.innerHTML = '';
    services.forEach((s, i) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${s.name}</td>
            <td>${s.desc || ''}</td>
            <td>${s.price || ''}</td>
            <td>
                <button class="edit-btn" data-index="${i}">تعديل</button>
                <button class="delete-btn" data-index="${i}">حذف</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
    // ربط أحداث التعديل والحذف
    tbody.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            showAddServiceModal(this.dataset.index);
        });
    });
    tbody.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من حذف الخدمة؟')) {
                let services = getServicesData();
                services.splice(this.dataset.index, 1);
                saveServicesData(services);
                renderServicesTable(searchService.value.trim());
            }
        });
    });
}

function showAddServiceModal(editIndex = null) {
    const modal = document.getElementById('serviceModal');
    const form = document.getElementById('serviceForm');
    document.getElementById('serviceModalTitle').textContent = editIndex === null ? 'إضافة خدمة' : 'تعديل خدمة';
    if (editIndex !== null) {
        const services = getServicesData();
        const s = services[editIndex];
        document.getElementById('serviceId').value = editIndex;
        document.getElementById('serviceName').value = s.name;
        document.getElementById('serviceDesc').value = s.desc || '';
        document.getElementById('servicePrice').value = s.price || '';
    } else {
        form.reset();
        document.getElementById('serviceId').value = '';
    }
    modal.style.display = 'flex';
}

function closeServiceModal() {
    document.getElementById('serviceModal').style.display = 'none';
}

function saveService() {
    let services = getServicesData();
    const id = document.getElementById('serviceId').value;
    const s = {
        name: document.getElementById('serviceName').value.trim(),
        desc: document.getElementById('serviceDesc').value.trim(),
        price: document.getElementById('servicePrice').value.trim()
    };
    if (!s.name) return alert('اسم الخدمة مطلوب');
    if (id) {
        services[+id] = s;
    } else {
        services.push(s);
    }
    saveServicesData(services);
    renderServicesTable(document.getElementById('searchService').value.trim());
    closeServiceModal();
    updateInvoiceServicesList();
}

function bindCustomersSectionEvents() {
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    if (addCustomerBtn) {
        addCustomerBtn.addEventListener('click', function() {
            showAddCustomerModal();
        });
    }

    const closeCustomerModalBtn = document.getElementById('closeCustomerModal');
    if (closeCustomerModalBtn) {
        closeCustomerModalBtn.addEventListener('click', function() {
            closeCustomerModal();
        });
    }

    const customerForm = document.getElementById('customerForm');
    if (customerForm) {
        customerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveCustomer();
        });
    }

    // البحث
    const searchCustomer = document.getElementById('searchCustomer');
    if (searchCustomer) {
        searchCustomer.addEventListener('input', function(e) {
            renderCustomersTable(e.target.value.trim());
        });
    }

    // عرض العملاء عند التحميل
    renderCustomersTable();
}

async function getCustomersData() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            return await sqliteDB.getAllCustomers();
        }
        return [];
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات العملاء:', error);
        return [];
    }
}

function saveCustomersData(customers) {
    localStorage.setItem('glassERP_customers', JSON.stringify(customers));
}

function renderCustomersTable(search = '') {
    const tbody = document.querySelector('#customersTable tbody');
    if (!tbody) return;
    let customers = getCustomersData();
    if (search) {
        customers = customers.filter(c =>
            c.name.includes(search) || (c.phone && c.phone.includes(search)) || (c.address && c.address.includes(search)) || (c.email && c.email.includes(search))
        );
    }
    tbody.innerHTML = '';
    customers.forEach((c, i) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${c.name}</td>
            <td>${c.phone || ''}</td>
            <td>${c.address || ''}</td>
            <td>${c.email || ''}</td>
            <td>${c.notes || ''}</td>
            <td>
                <button class="edit-btn" data-index="${i}">تعديل</button>
                <button class="delete-btn" data-index="${i}">حذف</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
    // ربط أحداث التعديل والحذف
    tbody.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            showAddCustomerModal(this.dataset.index);
        });
    });
    tbody.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من حذف العميل؟')) {
                let customers = getCustomersData();
                customers.splice(this.dataset.index, 1);
                saveCustomersData(customers);
                renderCustomersTable(searchCustomer.value.trim());
            }
        });
    });
}

function showAddCustomerModal(editIndex = null) {
    const modal = document.getElementById('customerModal');
    const form = document.getElementById('customerForm');
    document.getElementById('customerModalTitle').textContent = editIndex === null ? 'إضافة عميل' : 'تعديل عميل';
    if (editIndex !== null) {
        const customers = getCustomersData();
        const c = customers[editIndex];
        document.getElementById('customerId').value = editIndex;
        document.getElementById('customerName').value = c.name;
        document.getElementById('customerPhone').value = c.phone || '';
        document.getElementById('customerAddress').value = c.address || '';
        document.getElementById('customerEmail').value = c.email || '';
        document.getElementById('customerNotes').value = c.notes || '';
    } else {
        form.reset();
        document.getElementById('customerId').value = '';
    }
    modal.style.display = 'flex';
}

function closeCustomerModal() {
    document.getElementById('customerModal').style.display = 'none';
}

function saveCustomer() {
    let customers = getCustomersData();
    const id = document.getElementById('customerId').value;
    const c = {
        name: document.getElementById('customerName').value.trim(),
        phone: document.getElementById('customerPhone').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        notes: document.getElementById('customerNotes').value.trim()
    };
    if (!c.name) return alert('الاسم مطلوب');
    if (id) {
        customers[+id] = c;
    } else {
        customers.push(c);
    }
    saveCustomersData(customers);
    renderCustomersTable(document.getElementById('searchCustomer').value.trim());
    closeCustomerModal();
    updateInvoiceCustomerList();
}

function updateInvoiceCustomerList() {
    const select = document.getElementById('invoice-customer');
    if (!select) return;
    let customers = [];
    try {
        customers = JSON.parse(localStorage.getItem('glassERP_customers') || '[]');
    } catch { customers = []; }
    select.innerHTML = '<option value="">اختر العميل</option>' + customers.map((c, i) => `<option value="${i}">${c.name}</option>`).join('');
}

function updateInvoiceServicesList() {
    // تستخدم في قوائم الخدمات المنسدلة في الأصناف
    window.invoiceServicesList = [];
    try {
        window.invoiceServicesList = JSON.parse(localStorage.getItem('glassERP_services') || '[]');
    } catch { window.invoiceServicesList = []; }
}

// تفعيل الأزرار
function openSalesInvoiceModal() {
    document.getElementById('sales-invoice-modal').classList.add('active');
}
function closeSalesInvoiceModal() {
    document.getElementById('sales-invoice-modal').classList.remove('active');
}
function addInvoiceItemRow() {
    const tbody = document.getElementById('invoice-items-body');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td><input type="text" name="service[]" required></td>
        <td><select name="thickness[]">${[...Array(100)].map((_,i)=>`<option>${i+1} ملم</option>`).join('')}</select></td>
        <td><input type="number" name="length[]" min="1" required></td>
        <td><input type="number" name="width[]" min="1" required></td>
        <td><input type="number" name="qty[]" min="1" required></td>
        <td><input type="number" name="unitPrice[]" min="0" required></td>
        <td><input type="text" name="itemTotal[]" readonly></td>
        <td><button type="button" onclick="this.closest('tr').remove()">🗑️</button></td>
    `;
    tbody.appendChild(row);
}
window.openSalesInvoiceModal = openSalesInvoiceModal;
window.closeSalesInvoiceModal = closeSalesInvoiceModal;
window.addInvoiceItemRow = addInvoiceItemRow;

// تهيئة قاعدة البيانات تلقائيًا عند تحميل الصفحة
initSalesDB().catch(e => alert('فشل في تهيئة قاعدة البيانات: ' + e.message)); 