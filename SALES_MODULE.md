# موديول المبيعات - Glass ERP System

## نظرة عامة

موديول المبيعات هو نظام متكامل لإدارة عمليات البيع والفواتير، مصمم خصيصاً للأنشطة التجارية والصناعية في مجال الزجاج والألمنيوم.

## الميزات الرئيسية

### ✅ 1. إدارة العملاء
- **إضافة عميل جديد**: اسم كامل، رقم هاتف، عنوان، رقم ضريبي (اختياري)
- **قائمة العملاء**: عرض جميع العملاء مع إمكانية التحرير والحذف
- **تفاصيل العميل**: عرض معلومات العميل عند اختياره في الفاتورة

### ✅ 2. إدارة الخدمات
- **إضافة خدمة جديدة**: اسم الخدمة (قص، تركيب، نقل...) والسعر
- **قائمة الخدمات**: عرض جميع الخدمات مع إمكانية التحرير والحذف
- **إضافة للفاتورة**: إضافة الخدمات للفواتير بسهولة

### ✅ 3. إنشاء الفواتير
- **ترقيم تلقائي**: يبدأ بـ S- متبوعاً برقم تسلسلي
- **حالات الفاتورة**:
  - مبدئية: لا يتم الترحيل المحاسبي
  - نهائية: يتم الترحيل تلقائياً
  - ملغاة: يتم ترحيل قيد مردودات

### ✅ 4. الحسابات التلقائية
- **حساب المتر المربع**: (الطول × العرض × العدد) ÷ 1,000,000
- **إجمالي الصف**: المتر المربع × سعر المتر
- **الإجماليات**:
  - إجمالي المتر المربع
  - إجمالي الأصناف
  - إجمالي الخدمات
  - الخصم
  - الضريبة
  - المبلغ النهائي
  - المدفوع
  - المتبقي

### ✅ 5. الترحيل المحاسبي التلقائي
- **فاتورة نهائية**: من ح/ العملاء إلى ح/ المبيعات
- **إلغاء فاتورة**: من ح/ مردودات المبيعات إلى ح/ العملاء
- **حذف الفاتورة**: حذف القيد المحاسبي المرتبط

## كيفية الاستخدام

### إنشاء فاتورة جديدة
1. اضغط على "فاتورة جديدة"
2. اختر العميل أو أضف عميل جديد
3. أضف الأصناف مع الأبعاد والكميات
4. أضف الخدمات الإضافية
5. راجع الحسابات التلقائية
6. اضغط "حفظ الفاتورة"

### إدارة العملاء
1. اضغط على "إدارة العملاء"
2. اضغط "إضافة عميل"
3. أدخل بيانات العميل
4. اضغط "حفظ العميل"

### إدارة الخدمات
1. اضغط على "إدارة الخدمات"
2. اضغط "إضافة خدمة"
3. أدخل اسم الخدمة والسعر
4. اضغط "حفظ الخدمة"

## بنية البيانات

### الفاتورة
```javascript
{
    id: "unique_id",
    number: "S-000001",
    date: "2024-12-01",
    status: "draft|final|cancelled",
    customerId: "customer_id",
    customerName: "اسم العميل",
    items: [...],
    services: [...],
    totalArea: 0,
    totalItems: 0,
    totalServices: 0,
    discount: 0,
    tax: 0,
    total: 0,
    paid: 0,
    remaining: 0,
    createdAt: "2024-12-01T10:00:00Z"
}
```

### العميل
```javascript
{
    id: "unique_id",
    name: "اسم العميل",
    phone: "رقم الهاتف",
    address: "العنوان",
    taxNumber: "الرقم الضريبي",
    createdAt: "2024-12-01T10:00:00Z"
}
```

### الخدمة
```javascript
{
    id: "unique_id",
    name: "اسم الخدمة",
    price: 100,
    createdAt: "2024-12-01T10:00:00Z"
}
```

### الصنف في الفاتورة
```javascript
{
    code: "كود الصنف",
    name: "اسم الصنف",
    length: 1000, // بالمليمتر
    width: 500,   // بالمليمتر
    quantity: 1,
    area: 0.5,    // بالمتر المربع
    price: 100,   // السعر للمتر المربع
    total: 50,    // الإجمالي
    notes: "ملاحظات"
}
```

## الحسابات الافتراضية

```javascript
defaultAccounts: {
    customers: '120000',     // ح/ العملاء
    sales: '400000',         // ح/ المبيعات
    salesReturns: '400100'   // ح/ مردودات المبيعات
}
```

## دوال JavaScript الرئيسية

### إدارة الفواتير
- `createNewInvoice()`: إنشاء فاتورة جديدة
- `saveInvoice()`: حفظ الفاتورة
- `openInvoice(id)`: فتح الفاتورة للعرض
- `editInvoice(id)`: تحرير الفاتورة
- `deleteInvoice(id)`: حذف الفاتورة
- `printInvoice(id)`: طباعة الفاتورة

### إدارة العملاء
- `showCustomersModal()`: عرض نافذة العملاء
- `saveCustomer()`: حفظ عميل جديد
- `loadCustomersSelect()`: تحميل قائمة العملاء
- `loadCustomerInfo()`: تحميل تفاصيل العميل

### إدارة الخدمات
- `showServicesModal()`: عرض نافذة الخدمات
- `saveService()`: حفظ خدمة جديدة
- `editService(id)`: تحرير خدمة
- `deleteService(id)`: حذف خدمة
- `addServiceToInvoice(id)`: إضافة خدمة للفاتورة

### الحسابات
- `calculateItemArea(id)`: حساب مساحة الصنف
- `calculateItemTotal(id)`: حساب إجمالي الصنف
- `calculateInvoiceTotal()`: حساب إجمالي الفاتورة
- `calculateInvoiceRemaining()`: حساب المتبقي

### الترحيل المحاسبي
- `createAccountingEntry(invoice)`: إنشاء قيد محاسبي

## التخزين المحلي

### المفاتيح المستخدمة
- `glassERP_sales_invoices`: الفواتير
- `glassERP_sales_customers`: العملاء
- `glassERP_sales_services`: الخدمات

## التصميم والواجهة

### الألوان المستخدمة
- **الأزرق الأساسي**: #007bff
- **الأزرق الداكن**: #0056b3
- **الرمادي**: #6c757d
- **الأحمر**: #dc3545
- **الأخضر**: #28a745

### الحالات
- **مبدئية**: أصفر (#fff3cd)
- **نهائية**: أخضر (#d4edda)
- **ملغاة**: أحمر (#f8d7da)

## الاختبار

### اختبار سريع
```javascript
// إنشاء فاتورة تجريبية
createNewInvoice();

// إضافة عميل تجريبي
showCustomersModal();

// إضافة خدمة تجريبية
showServicesModal();
```

### اختبار شامل
```javascript
// اختبار جميع الوظائف
SalesModule.testAll();
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **لا تظهر الفواتير**: تحقق من تحميل البيانات
2. **لا يتم الحفظ**: تحقق من مساحة التخزين المحلي
3. **الحسابات خاطئة**: تحقق من إدخال الأبعاد بشكل صحيح

### حلول
1. **تحديث الصفحة**: لإعادة تحميل البيانات
2. **مسح التخزين**: لحل مشاكل الحفظ
3. **فحص وحدة التحكم**: للتحقق من الأخطاء

## التطوير المستقبلي

### ميزات مقترحة
- [ ] نظام الخصومات المتقدم
- [ ] إدارة المخزون
- [ ] تقارير المبيعات
- [ ] نظام المدفوعات
- [ ] إشعارات تلقائية
- [ ] تصدير البيانات
- [ ] نظام النسخ الاحتياطي

### تحسينات مقترحة
- [ ] واجهة محسنة للجوال
- [ ] نظام البحث المتقدم
- [ ] تصفية وترتيب الفواتير
- [ ] نظام الطباعة المحسن
- [ ] دعم متعدد العملات

---

*تم التطوير بواسطة فريق Glass ERP - ديسمبر 2024* 