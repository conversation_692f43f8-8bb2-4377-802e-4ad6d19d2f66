/**
 * Sales Module CSS - تصميم موديول المبيعات
 * ========================================
 */

/* حاوية المبيعات الرئيسية */
.sales-container {
    padding: 32px 24px;
    background: linear-gradient(135deg, #f8fafc 60%, #e3f0ff 100%);
    min-height: calc(100vh - 120px);
    font-family: 'Cairo', sans-serif;
}

/* شريط الأدوات */
.sales-toolbar {
    background: linear-gradient(90deg, #007bff 0%, #2196f3 100%);
    color: #fff;
    padding: 28px 24px;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0,123,255,0.08);
    margin-bottom: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 18px;
    position: relative;
    overflow: hidden;
}

.sales-toolbar h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 800;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 14px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.toolbar-actions {
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
}

/* محتوى الفواتير */
.invoices-content {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 16px rgba(0,123,255,0.06);
    overflow: hidden;
}

.invoices-list {
    padding: 32px 24px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(370px, 1fr));
    gap: 28px;
}

/* بطاقة الفاتورة */
.invoice-card {
    background: linear-gradient(120deg, #f8f9fa 80%, #e3f0ff 100%);
    border: 1.5px solid #e3f0ff;
    border-radius: 14px;
    padding: 24px 20px;
    cursor: pointer;
    transition: box-shadow 0.2s, border 0.2s, transform 0.2s;
    box-shadow: 0 2px 8px rgba(0,123,255,0.04);
    position: relative;
    overflow: hidden;
}

.invoice-card:hover {
    box-shadow: 0 8px 32px rgba(0,123,255,0.10);
    border-color: #2196f3;
    transform: translateY(-2px) scale(1.02);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3f0ff;
}

.invoice-header h4 {
    margin: 0;
    color: #007bff;
    font-weight: 800;
    font-size: 1.2rem;
}

.status {
    padding: 5px 16px;
    border-radius: 20px;
    font-size: 0.95rem;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 1px 4px rgba(0,123,255,0.07);
}

.status.draft {
    background: #fffbe6;
    color: #bfa600;
    border: 1px solid #ffeaa7;
}

.status.final {
    background: #e3fbe6;
    color: #1e7e34;
    border: 1px solid #55a3ff;
}

.status.cancelled {
    background: #ffeaea;
    color: #c82333;
    border: 1px solid #ff7675;
}

.invoice-details {
    margin-bottom: 15px;
}

.invoice-details p {
    margin: 6px 0;
    color: #6c757d;
    font-size: 1rem;
}

.invoice-details strong {
    color: #495057;
}

.invoice-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 10px;
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #b0b8c1;
}

.empty-state i {
    font-size: 4.5rem;
    margin-bottom: 24px;
    color: #e3f0ff;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 1.3rem;
}

.empty-state p {
    margin: 0 0 20px 0;
    color: #6c757d;
}

/* نافذة الفاتورة */
.large-modal {
    max-width: 98%;
    width: 1280px;
    max-height: 95vh;
    border-radius: 18px;
}

.invoice-form {
    display: flex;
    flex-direction: column;
    gap: 28px;
}

/* رأس الفاتورة */
.invoice-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    padding: 24px 0 0 0;
    background: none;
    border-radius: 0;
}

.invoice-info, .customer-info {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

/* جدول الأصناف */
.invoice-items {
    background: #f8f9fa;
    border: 1.5px solid #e3f0ff;
    border-radius: 12px;
    padding: 24px 18px;
}

.invoice-items h4 {
    margin: 0 0 18px 0;
    color: #007bff;
    font-size: 1.1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.items-table-container {
    overflow-x: auto;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 18px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0,123,255,0.04);
}

.items-table th,
.items-table td {
    padding: 14px 8px;
    text-align: center;
    border-bottom: 1px solid #e3f0ff;
}

.items-table th {
    background: #e3f0ff;
    font-weight: 800;
    color: #007bff;
    font-size: 1rem;
}

.items-table input {
    width: 100%;
    padding: 8px;
    border: 1.5px solid #b0b8c1;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
    background: #f8f9fa;
    transition: border 0.2s;
}

.items-table input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

/* الخدمات */
.invoice-services {
    background: #f8f9fa;
    border: 1.5px solid #e3f0ff;
    border-radius: 12px;
    padding: 24px 18px;
    margin-top: 12px;
}

.invoice-services h4 {
    margin: 0 0 18px 0;
    color: #007bff;
    font-size: 1.1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.services-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 12px;
    margin-bottom: 18px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 14px;
    border: 1.5px solid #e3f0ff;
    border-radius: 8px;
    background: #fff;
    cursor: pointer;
    transition: box-shadow 0.2s, border 0.2s, background 0.2s;
    box-shadow: 0 1px 4px rgba(0,123,255,0.04);
}

.service-item:hover {
    background: #e3f0ff;
    border-color: #2196f3;
}

.service-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
}

.service-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.service-name {
    font-weight: 700;
    color: #007bff;
}

.service-price {
    font-size: 1rem;
    color: #28a745;
    font-weight: 700;
}

/* ملخص الفاتورة */
.invoice-summary {
    background: #f8f9fa;
    border: 1.5px solid #e3f0ff;
    border-radius: 12px;
    padding: 24px 18px;
    margin-top: 12px;
}

.summary-calculations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 18px;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 10px;
    background: #e3f0ff;
    border-radius: 8px;
    font-size: 1rem;
}

.calc-item.total {
    background: linear-gradient(90deg, #007bff 0%, #2196f3 100%);
    color: #fff;
    border: 2px solid #2196f3;
    font-weight: 800;
    font-size: 1.15rem;
}

.calc-item label {
    font-weight: 700;
    color: #007bff;
}

.calc-item span {
    font-weight: 800;
    color: #2c3e50;
}

.calc-item input {
    width: 120px;
    padding: 8px;
    border: 1.5px solid #b0b8c1;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
    background: #fff;
    transition: border 0.2s;
}

.calc-item input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

/* إجراءات الفاتورة */
.invoice-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    padding: 24px 0 0 0;
    background: none;
    border-radius: 0;
}

/* تفاصيل العميل */
.customer-details {
    margin-top: 10px;
}

.customer-info-card {
    background: #e3f0ff;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    box-shadow: 0 1px 4px rgba(0,123,255,0.04);
}

.customer-info-card p {
    margin: 5px 0;
    font-size: 1rem;
}

/* أزرار */
.btn-primary {
    background: linear-gradient(90deg, #007bff 0%, #2196f3 100%);
    color: #fff;
    border: none;
    padding: 12px 28px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0,123,255,0.10);
    transition: background 0.2s, transform 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-primary:hover {
    background: linear-gradient(90deg, #0056b3 0%, #007bff 100%);
    transform: translateY(-2px) scale(1.03);
}

.btn-secondary {
    background: #fff;
    color: #007bff;
    border: 2px solid #007bff;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 700;
    transition: background 0.2s, color 0.2s, border 0.2s, transform 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-secondary:hover {
    background: #e3f0ff;
    color: #0056b3;
    border-color: #2196f3;
    transform: translateY(-2px) scale(1.03);
}

.btn-danger {
    background: linear-gradient(90deg, #dc3545 0%, #ff7675 100%);
    color: #fff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 700;
    transition: background 0.2s, transform 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-danger:hover {
    background: linear-gradient(90deg, #c82333 0%, #ff7675 100%);
    transform: translateY(-2px) scale(1.03);
}

.btn-small {
    background: #e3f0ff;
    color: #007bff;
    border: none;
    padding: 7px 14px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 700;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-small:hover {
    background: #007bff;
    color: #fff;
    transform: scale(1.07);
}

.btn-small.danger {
    background: #ffeaea;
    color: #c82333;
}

.btn-small.danger:hover {
    background: #c82333;
    color: #fff;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.18);
    backdrop-filter: blur(6px);
}

.modal-content {
    background-color: #fff;
    margin: 4% auto;
    padding: 0;
    border-radius: 18px;
    width: 92%;
    max-width: 650px;
    box-shadow: 0 10px 30px rgba(0,123,255,0.18);
    animation: modalSlideIn 0.3s cubic-bezier(.4,2,.6,1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-60px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 24px 24px 12px 24px;
    border-bottom: 1.5px solid #e3f0ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #007bff;
    font-size: 1.3rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: #aaa;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #007bff;
}

.modal-body {
    padding: 24px;
}

/* النماذج */
.form-group {
    margin-bottom: 18px;
}

.form-group label {
    display: block;
    margin-bottom: 7px;
    font-weight: 700;
    color: #007bff;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1.5px solid #b0b8c1;
    border-radius: 8px;
    font-size: 1rem;
    background: #f8f9fa;
    transition: border 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 14px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 18px;
    border-top: 1.5px solid #e3f0ff;
}

/* التصميم المتجاوب */
@media (max-width: 900px) {
    .invoices-list {
        grid-template-columns: 1fr;
    }
    .invoice-header {
        grid-template-columns: 1fr;
    }
    .summary-calculations {
        grid-template-columns: 1fr;
    }
    .modal-content {
        width: 98%;
        margin: 8% auto;
    }
}

@media (max-width: 600px) {
    .sales-container {
        padding: 10px;
    }
    .sales-toolbar {
        padding: 12px;
        border-radius: 10px;
    }
    .sales-toolbar h2 {
        font-size: 1.1rem;
    }
    .invoice-card {
        padding: 10px;
        border-radius: 8px;
    }
    .modal-body {
        padding: 10px;
    }
    .form-actions {
        flex-direction: column;
        gap: 8px;
    }
}

/* تحسينات الأداء */
.items-table,
.services-list,
.invoice-card {
    will-change: transform;
}

.invoice-card:hover,
.service-item:hover {
    transform: translateZ(0);
}

/* تأثيرات بصرية إضافية */
.invoice-card {
    position: relative;
    overflow: hidden;
}

.invoice-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.invoice-card:hover::before {
    left: 100%;
}

/* تحسين قابلية القراءة */
.items-table th {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #e3f0ff;
}

/* تحسين التفاعل */
.btn-primary:active,
.btn-secondary:active,
.btn-danger:active {
    transform: translateY(0);
}

/* تحسين النماذج */
.form-group input[readonly] {
    background-color: #e3f0ff;
    cursor: not-allowed;
}

/* تحسين الجداول */
.items-table tbody tr:hover {
    background-color: #e3f0ff;
}

/* تحسين الألوان */
.status.draft {
    border: 1px solid #ffeaa7;
}

.status.final {
    border: 1px solid #55a3ff;
}

.status.cancelled {
    border: 1px solid #ff7675;
}

.invoice-accounts {
    background: #f8f9fa;
    border: 1.5px solid #e3f0ff;
    border-radius: 12px;
    padding: 24px 18px;
    margin-top: 12px;
}

.invoice-accounts h4 {
    margin: 0 0 18px 0;
    color: #007bff;
    font-size: 1.1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.accounts-fields {
    display: flex;
    gap: 18px;
    flex-wrap: wrap;
}

.accounts-fields .form-group {
    flex: 1 1 220px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.accounts-fields label {
    font-weight: 700;
    color: #007bff;
}

.accounts-fields select {
    padding: 8px;
    border: 1.5px solid #b0b8c1;
    border-radius: 6px;
    font-size: 1rem;
    background: #fff;
    transition: border 0.2s;
}

.accounts-fields select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

.items-table tr.item-desc-row td {
    background: #f8f9fa;
    border-top: none;
    padding: 8px 8px 16px 8px;
}

.items-table .item-desc {
    width: 100%;
    font-size: 15px;
    padding: 8px;
    border: 1.5px solid #b0b8c1;
    border-radius: 6px;
    background: #fff;
    margin-top: 2px;
}

.items-table .item-desc:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

.items-table select.item-service {
    width: 100%;
    padding: 8px;
    border: 1.5px solid #b0b8c1;
    border-radius: 6px;
    font-size: 1rem;
    background: #fff;
    transition: border 0.2s;
}

.items-table select.item-service:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.10);
}

.sales-tabs {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #f1f5f9;
    margin-bottom: 32px;
}
.sales-tabs .tab-buttons {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    overflow-x: auto;
}
.sales-tabs .tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    color: #64748b;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}
.sales-tabs .tab-btn:hover {
    background: #f1f5f9;
    color: #475569;
}
.sales-tabs .tab-btn.active {
    background: #fff;
    color: #007bff;
    border-bottom-color: #007bff;
}
.sales-tabs .tab-btn i {
    font-size: 1.1rem;
}
.sales-tabs .tab-content {
    padding: 2rem;
}
.sales-tabs .tab-pane {
    display: none;
}
.sales-tabs .tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
} 