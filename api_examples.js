// أمثلة لاستخدام Supabase API في Glass ERP System
// يمكنك استخدام هذه الأمثلة في تطبيقك

import { createClient } from '@supabase/supabase-js'

// إعداد الاتصال بـ Supabase
const supabaseUrl = 'YOUR_SUPABASE_PROJECT_URL'
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'

export const supabase = createClient(supabaseUrl, supabaseKey)

// ========================================
// إدارة إعدادات الشركة
// ========================================

/**
 * جلب جميع إعدادات الشركات
 */
export async function getAllCompanySettings() {
    try {
        const { data, error } = await supabase
            .from('company_settings')
            .select('*')
            .eq('is_active', true)
            .order('company_name', { ascending: true })

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب إعدادات الشركات:', error)
        return { data: null, error }
    }
}

/**
 * جلب إعدادات شركة محددة
 */
export async function getCompanySettingsById(id) {
    try {
        const { data, error } = await supabase
            .from('company_settings')
            .select('*')
            .eq('id', id)
            .single()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب إعدادات الشركة:', error)
        return { data: null, error }
    }
}

/**
 * إضافة شركة جديدة
 */
export async function addCompanySettings(companyData) {
    try {
        const { data, error } = await supabase
            .from('company_settings')
            .insert([{
                company_name: companyData.name,
                company_address: companyData.address,
                company_phone: companyData.phone,
                company_email: companyData.email,
                company_website: companyData.website,
                tax_number: companyData.taxNumber,
                currency: companyData.currency || 'LYD',
                logo_url: companyData.logoUrl,
                notes: companyData.notes
            }])
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في إضافة إعدادات الشركة:', error)
        return { data: null, error }
    }
}

/**
 * تحديث إعدادات شركة موجودة
 */
export async function updateCompanySettings(id, updates) {
    try {
        const { data, error } = await supabase
            .from('company_settings')
            .update(updates)
            .eq('id', id)
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في تحديث إعدادات الشركة:', error)
        return { data: null, error }
    }
}

/**
 * حذف شركة (تعطيل)
 */
export async function deactivateCompanySettings(id) {
    try {
        const { data, error } = await supabase
            .from('company_settings')
            .update({ is_active: false })
            .eq('id', id)
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في تعطيل إعدادات الشركة:', error)
        return { data: null, error }
    }
}

// ========================================
// إدارة المستخدمين
// ========================================

/**
 * جلب جميع المستخدمين
 */
export async function getAllUsers() {
    try {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false })

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب المستخدمين:', error)
        return { data: null, error }
    }
}

/**
 * إضافة مستخدم جديد
 */
export async function addUser(userData) {
    try {
        const { data, error } = await supabase
            .from('users')
            .insert([{
                email: userData.email,
                phone: userData.phone,
                first_name: userData.firstName,
                last_name: userData.lastName,
                role: userData.role || 'user'
            }])
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error)
        return { data: null, error }
    }
}

/**
 * تحديث بيانات المستخدم
 */
export async function updateUser(id, updates) {
    try {
        const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', id)
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في تحديث المستخدم:', error)
        return { data: null, error }
    }
}

// ========================================
// إدارة الإشعارات
// ========================================

/**
 * جلب إشعارات المستخدم
 */
export async function getUserNotifications(userId) {
    try {
        const { data, error } = await supabase
            .from('notifications')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب الإشعارات:', error)
        return { data: null, error }
    }
}

/**
 * إضافة إشعار جديد
 */
export async function addNotification(notificationData) {
    try {
        const { data, error } = await supabase
            .from('notifications')
            .insert([{
                user_id: notificationData.userId,
                title: notificationData.title,
                message: notificationData.message,
                type: notificationData.type || 'info'
            }])
            .select()

        if (error) throw error
        return { data, error }
    } catch (error) {
        console.error('خطأ في إضافة الإشعار:', error)
        return { data: null, error }
    }
}

/**
 * تحديث حالة الإشعار كمقروء
 */
export async function markNotificationAsRead(notificationId) {
    try {
        const { data, error } = await supabase
            .from('notifications')
            .update({ 
                is_read: true, 
                read_at: new Date().toISOString() 
            })
            .eq('id', notificationId)
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في تحديث حالة الإشعار:', error)
        return { data: null, error }
    }
}

// ========================================
// سجل النشاطات
// ========================================

/**
 * إضافة نشاط جديد
 */
export async function addActivityLog(activityData) {
    try {
        const { data, error } = await supabase
            .from('activity_logs')
            .insert([{
                user_id: activityData.userId,
                action: activityData.action,
                module: activityData.module,
                description: activityData.description,
                ip_address: activityData.ipAddress,
                user_agent: activityData.userAgent
            }])

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في إضافة سجل النشاط:', error)
        return { data: null, error }
    }
}

/**
 * جلب سجل نشاطات المستخدم
 */
export async function getUserActivityLogs(userId, limit = 50) {
    try {
        const { data, error } = await supabase
            .from('activity_logs')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(limit)

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب سجل النشاطات:', error)
        return { data: null, error }
    }
}

// ========================================
// إعدادات النظام
// ========================================

/**
 * جلب إعدادات النظام
 */
export async function getSystemConfig(key = null) {
    try {
        let query = supabase
            .from('system_config')
            .select('*')

        if (key) {
            query = query.eq('key', key)
        }

        const { data, error } = await query

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في جلب إعدادات النظام:', error)
        return { data: null, error }
    }
}

/**
 * تحديث إعدادات النظام
 */
export async function updateSystemConfig(key, value) {
    try {
        const { data, error } = await supabase
            .from('system_config')
            .update({ value: value })
            .eq('key', key)
            .select()

        if (error) throw error
        return { data, error: null }
    } catch (error) {
        console.error('خطأ في تحديث إعدادات النظام:', error)
        return { data: null, error }
    }
}

// ========================================
// أمثلة للاستخدام
// ========================================

// مثال: تحميل إعدادات الشركة عند بدء التطبيق
export async function initializeCompanySettings() {
    const { data, error } = await getAllCompanySettings()
    
    if (error) {
        console.error('فشل في تحميل إعدادات الشركة:', error)
        return null
    }

    if (data && data.length > 0) {
        // استخدام أول شركة نشطة
        return data[0]
    }

    return null
}

// مثال: إضافة نشاط عند تسجيل الدخول
export async function logUserLogin(userId, ipAddress, userAgent) {
    await addActivityLog({
        userId: userId,
        action: 'login',
        module: 'auth',
        description: 'تسجيل دخول المستخدم',
        ipAddress: ipAddress,
        userAgent: userAgent
    })
}

// مثال: إرسال إشعار للمستخدم
export async function sendUserNotification(userId, title, message, type = 'info') {
    await addNotification({
        userId: userId,
        title: title,
        message: message,
        type: type
    })
}

// مثال: تحديث إعدادات النظام
export async function updateAppSettings() {
    await updateSystemConfig('app_version', '"2.0.1"')
    await updateSystemConfig('maintenance_mode', 'false')
}

// ========================================
// دوال مساعدة
// ========================================

/**
 * التحقق من وجود خطأ في الاستجابة
 */
export function handleSupabaseError(response) {
    if (response.error) {
        console.error('خطأ في Supabase:', response.error)
        return true
    }
    return false
}

/**
 * تنسيق البيانات للعرض
 */
export function formatCompanyData(company) {
    return {
        id: company.id,
        name: company.company_name,
        address: company.company_address,
        phone: company.company_phone,
        email: company.company_email,
        website: company.company_website,
        taxNumber: company.tax_number,
        currency: company.currency,
        logoUrl: company.logo_url,
        isActive: company.is_active,
        createdAt: company.created_at,
        updatedAt: company.updated_at
    }
}

/**
 * التحقق من صلاحيات المستخدم
 */
export async function checkUserPermission(userId, permission) {
    try {
        const { data: user } = await supabase
            .from('users')
            .select('role')
            .eq('id', userId)
            .single()

        if (!user) return false

        const { data: role } = await supabase
            .from('user_roles')
            .select('permissions')
            .eq('name', user.role)
            .single()

        if (!role) return false

        return role.permissions.includes(permission)
    } catch (error) {
        console.error('خطأ في التحقق من الصلاحيات:', error)
        return false
    }
} 