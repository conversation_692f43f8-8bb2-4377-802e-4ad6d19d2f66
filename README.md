# Glass ERP System - نظام إدارة الشركات الزجاجية

## 🎉 تم تحديث النظام بالكامل - الاعتماد الكلي على SQLite

نظام إدارة شامل للشركات الزجاجية يعتمد على قاعدة بيانات SQLite المحلية مع واجهة مستخدم حديثة وسهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🏢 إدارة الشركة
- **إعدادات الشركة**: معلومات الشركة، العملة، اللغة، المنطقة الزمنية
- **إدارة المستخدمين**: نظام مستخدمين وصلاحيات متكامل
- **النسخ الاحتياطي**: نظام نسخ احتياطي واستعادة شامل

### 💰 النظام المحاسبي
- **إدارة الحسابات**: دليل حسابات شامل مع تصنيفات متعددة
- **القيود اليومية**: نظام قيود محاسبية متقدم
- **التقارير المالية**: تقارير شاملة للأصول والإيرادات والمصروفات
- **الترحيل التلقائي**: ربط تلقائي مع نظام المبيعات

### 🛒 نظام المبيعات
- **إدارة الفواتير**: إنشاء وتعديل وحذف وطباعة الفواتير
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **إدارة الخدمات**: كتالوج الخدمات والأسعار
- **التقارير**: تقارير مبيعات مفصلة

### 📊 لوحة التحكم
- **الإحصائيات**: عرض إحصائيات مالية شاملة
- **الرسوم البيانية**: رسوم بيانية تفاعلية للأداء المالي
- **تحليل التدفق النقدي**: تحليل شامل للتدفقات النقدية

### 🔔 نظام الإشعارات
- **إشعارات فورية**: إشعارات في الوقت الفعلي للأحداث المهمة
- **التصنيف**: إشعارات مصنفة حسب النوع والوحدة
- **الإدارة**: تحديد كمقروء، حذف، مسح قديم

## 🚀 التحديثات الجديدة

### ✅ إزالة كاملة لـ localStorage و IndexedDB
- **قاعدة بيانات SQLite**: نظام قاعدة بيانات محلي قوي وموثوق
- **الأداء المحسن**: سرعة تحميل وحفظ محسنة بشكل كبير
- **الموثوقية**: حفظ دائم وآمن للبيانات
- **الأمان**: حماية كاملة للبيانات مع التحقق من صحة البيانات

### 🔧 الميزات التقنية الجديدة
- **نظام المعاملات**: ضمان سلامة البيانات
- **النسخ الاحتياطي التلقائي**: حماية من فقدان البيانات
- **التصدير والاستيراد**: تبادل البيانات مع أنظمة أخرى
- **الفهرسة الذكية**: تحسين سرعة البحث والاستعلامات

## 📦 التثبيت والاستخدام

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يحتاج إلى تثبيت إضافي
- يعمل في البيئات المحلية والسحابية

### التثبيت
1. قم بتحميل الملفات
2. افتح `index.html` في المتصفح
3. ابدأ باستخدام النظام

### الإعداد الأولي
```javascript
// إنشاء بيانات تجريبية
createCleanTestData();

// إنشاء نسخة احتياطية أولية
sqliteDB.createBackup();
```

## 🗂️ هيكل الملفات

```
Glass-System/
├── index.html                 # الصفحة الرئيسية
├── glass-erp.html            # واجهة النظام الرئيسية
├── js/
│   ├── sqlite-database.js    # نظام قاعدة البيانات SQLite
│   ├── main.js              # إدارة النظام الرئيسية
│   ├── accounts.js          # النظام المحاسبي
│   ├── sales.js             # نظام المبيعات
│   ├── dashboard.js         # لوحة التحكم
│   ├── notifications.js     # نظام الإشعارات
│   ├── settings.js          # إعدادات النظام
│   ├── utils.js             # الدوال المساعدة
│   └── test-performance.js  # اختبارات الأداء
├── css/
│   ├── main.css             # التنسيقات الرئيسية
│   ├── dashboard.css        # تنسيقات لوحة التحكم
│   ├── accounts.css         # تنسيقات النظام المحاسبي
│   ├── sales.css            # تنسيقات نظام المبيعات
│   ├── notifications.css    # تنسيقات الإشعارات
│   ├── performance.css      # تنسيقات الأداء
│   └── settings.css         # تنسيقات الإعدادات
└── assets/
    ├── fonts/               # الخطوط
    ├── icons/               # الأيقونات
    └── libs/                # المكتبات الخارجية
```

## 🧪 اختبار النظام

### اختبارات الأداء
```javascript
// تشغيل اختبارات الأداء الشاملة
runPerformanceTests();

// فحص سريع للأداء
quickPerformanceCheck();

// اختبار شامل للنظام
runFullSystemTest();
```

### اختبار قاعدة البيانات
```javascript
// اختبار الحفظ والتحميل
testDataPersistence();

// اختبار نظام النسخ الاحتياطي
testBackupSystem();

// اختبار نظام الحفظ
testSaveSystem();
```

### اختبار البيانات التجريبية
```javascript
// إنشاء بيانات تجريبية نظيفة
createCleanTestData();

// مسح جميع البيانات التجريبية
clearAllTestData();

// اختبار نظام بيانات الشركة
testCompanyDataSystem();
```

## 📊 الجداول في قاعدة البيانات

### 1. جدول إعدادات الشركة
```sql
CREATE TABLE company_settings (
    id INTEGER PRIMARY KEY,
    name TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    website TEXT,
    tax_number TEXT,
    currency TEXT,
    language TEXT,
    timezone TEXT,
    fiscal_year_start TEXT,
    created_at TEXT,
    updated_at TEXT
);
```

### 2. جدول الحسابات
```sql
CREATE TABLE accounts (
    id TEXT PRIMARY KEY,
    name TEXT,
    type TEXT,
    parent_id TEXT,
    balance REAL DEFAULT 0,
    description TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TEXT,
    updated_at TEXT
);
```

### 3. جدول القيود اليومية
```sql
CREATE TABLE journal_entries (
    id TEXT PRIMARY KEY,
    number TEXT,
    date TEXT,
    description TEXT,
    total_debit REAL,
    total_credit REAL,
    status TEXT,
    created_at TEXT,
    updated_at TEXT
);
```

### 4. جدول تفاصيل القيود
```sql
CREATE TABLE journal_entry_details (
    id INTEGER PRIMARY KEY,
    journal_entry_id TEXT,
    account_id TEXT,
    debit REAL DEFAULT 0,
    credit REAL DEFAULT 0,
    description TEXT,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);
```

### 5. جدول الإشعارات
```sql
CREATE TABLE notifications (
    id TEXT PRIMARY KEY,
    title TEXT,
    message TEXT,
    type TEXT,
    timestamp TEXT,
    is_read INTEGER DEFAULT 0,
    module TEXT,
    created_at TEXT
);
```

### 6. جدول العملاء
```sql
CREATE TABLE customers (
    id TEXT PRIMARY KEY,
    name TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    tax_number TEXT,
    created_at TEXT,
    updated_at TEXT
);
```

### 7. جدول الخدمات
```sql
CREATE TABLE services (
    id TEXT PRIMARY KEY,
    name TEXT,
    description TEXT,
    price REAL,
    created_at TEXT,
    updated_at TEXT
);
```

### 8. جدول الفواتير
```sql
CREATE TABLE invoices (
    id TEXT PRIMARY KEY,
    number TEXT,
    date TEXT,
    customer_id TEXT,
    subtotal REAL,
    tax REAL,
    total REAL,
    paid REAL,
    remaining REAL,
    status TEXT,
    notes TEXT,
    created_at TEXT,
    updated_at TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

### 9. جدول تفاصيل الفواتير
```sql
CREATE TABLE invoice_items (
    id INTEGER PRIMARY KEY,
    invoice_id TEXT,
    description TEXT,
    quantity REAL,
    unit_price REAL,
    total REAL,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);
```

### 10. جدول المستخدمين
```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE,
    password_hash TEXT,
    full_name TEXT,
    email TEXT,
    role TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TEXT,
    updated_at TEXT
);
```

### 11. جدول الصلاحيات
```sql
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY,
    user_id TEXT,
    module TEXT,
    can_read INTEGER DEFAULT 0,
    can_write INTEGER DEFAULT 0,
    can_delete INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 🔧 الميزات التقنية

### نظام قاعدة البيانات SQLite
- **الأداء**: سرعة عالية في القراءة والكتابة
- **الموثوقية**: ضمان سلامة البيانات
- **الأمان**: حماية من فقدان البيانات
- **المرونة**: سهولة التطوير والصيانة

### نظام النسخ الاحتياطي
- **النسخ التلقائي**: نسخ احتياطي دوري
- **الاستعادة**: استعادة سريعة للبيانات
- **التصدير**: تصدير البيانات بصيغ مختلفة
- **الاستيراد**: استيراد البيانات من مصادر خارجية

### نظام الإشعارات
- **الوقت الفعلي**: إشعارات فورية
- **التصنيف**: إشعارات مصنفة
- **الإدارة**: إدارة شاملة للإشعارات
- **التخصيص**: تخصيص الإشعارات حسب المستخدم

### نظام التقارير
- **تقارير مالية**: تقارير شاملة للأداء المالي
- **تقارير مبيعات**: تحليل مفصل للمبيعات
- **تقارير العملاء**: معلومات شاملة عن العملاء
- **التصدير**: تصدير التقارير بصيغ مختلفة

## 🎯 المزايا التنافسية

### 1. البساطة
- واجهة مستخدم بسيطة وواضحة
- سهولة الاستخدام والتعلم
- لا يحتاج إلى تدريب مكثف

### 2. المرونة
- قابل للتخصيص حسب احتياجات الشركة
- إضافة ميزات جديدة بسهولة
- دعم متعدد اللغات

### 3. الموثوقية
- قاعدة بيانات قوية وموثوقة
- نسخ احتياطي تلقائي
- حماية من فقدان البيانات

### 4. الأداء
- سرعة عالية في الاستجابة
- استهلاك منخفض للموارد
- تحسين مستمر للأداء

### 5. الأمان
- حماية كاملة للبيانات
- نظام صلاحيات متقدم
- تشفير البيانات الحساسة

## 📈 خطط التطوير المستقبلية

### المرحلة القادمة
- [ ] نظام إدارة المخزون
- [ ] نظام إدارة المشتريات
- [ ] نظام إدارة الموظفين
- [ ] نظام إدارة المشاريع
- [ ] تطبيق جوال

### التحسينات التقنية
- [ ] واجهة برمجة تطبيقات (API)
- [ ] دعم السحابة الإلكترونية
- [ ] نظام تحليلات متقدم
- [ ] ذكاء اصطناعي للمساعدة في القرارات

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام! يمكنكم:

1. الإبلاغ عن الأخطاء
2. اقتراح ميزات جديدة
3. تحسين الكود
4. إضافة وثائق

## 📞 الدعم

للحصول على الدعم أو الاستفسارات:

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: www.glass-erp.com
- **التوثيق**: docs.glass-erp.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

---

## 🎉 شكراً لاستخدام Glass ERP System!

نظام إدارة شامل ومتطور للشركات الزجاجية، مصمم لتبسيط العمليات وتحسين الإنتاجية. 