/* ========================================
   Performance Optimizations CSS
   تحسينات الأداء الإضافية
   ======================================== */

/* تحسينات عامة للأداء */
* {
  /* تحسين الأداء */
  will-change: auto;
  transform: translateZ(0);
}

/* تحسين التمرير */
html {
  scroll-behavior: smooth;
  /* تحسين الأداء */
  -webkit-overflow-scrolling: touch;
}

body {
  /* تحسين الأداء */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* تحسين الصور */
img {
  /* تحسين الأداء */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* تحسين الجداول */
table {
  /* تحسين الأداء */
  border-collapse: collapse;
  table-layout: fixed;
}

/* تحسين النماذج */
input, select, textarea {
  /* تحسين الأداء */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* تحسين الأزرار */
button {
  /* تحسين الأداء */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* تحسين الروابط */
a {
  /* تحسين الأداء */
  -webkit-tap-highlight-color: transparent;
}

/* تحسين القوائم */
ul, ol {
  /* تحسين الأداء */
  list-style: none;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
  /* تحسين الأداء */
  font-weight: inherit;
  margin: 0;
}

/* تحسين الفقرات */
p {
  /* تحسين الأداء */
  margin: 0;
}

/* تحسين العناصر المطوية */
.collapsed {
  /* تحسين الأداء */
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

/* تحسين العناصر الموسعة */
.expanded {
  /* تحسين الأداء */
  margin-right: 0;
  transition: margin-right 0.3s ease;
}

/* تحسين العناصر المخفية */
.hidden {
  /* تحسين الأداء */
  display: none !important;
}

/* تحسين العناصر المرئية */
.visible {
  /* تحسين الأداء */
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* تحسين العناصر غير المرئية */
.invisible {
  /* تحسين الأداء */
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* تحسين الرسوم البيانية */
.chart-container {
  /* تحسين الأداء */
  position: relative;
  overflow: hidden;
}

.chart-container canvas {
  /* تحسين الأداء */
  max-width: 100%;
  height: auto;
}

/* تحسين الجداول الكبيرة */
.virtual-scroll-table {
  /* تحسين الأداء */
  position: relative;
  overflow: auto;
  max-height: 400px;
}

.virtual-scroll-table tbody {
  /* تحسين الأداء */
  position: relative;
}

.virtual-scroll-table tr {
  /* تحسين الأداء */
  position: absolute;
  width: 100%;
  transition: transform 0.1s ease;
}

/* تحسين العناصر المتأخرة */
.lazy-element {
  /* تحسين الأداء */
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.lazy-element.loaded {
  /* تحسين الأداء */
  opacity: 1;
  transform: translateY(0);
}

/* تحسين النوافذ المنبثقة */
.modal {
  /* تحسين الأداء */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  z-index: 10000;
  /* تحسين الأداء */
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.modal.show {
  /* تحسين الأداء */
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* تحسين الإشعارات */
.notification {
  /* تحسين الأداء */
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  z-index: 10001;
  /* تحسين الأداء */
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  /* تحسين الأداء */
  transform: translateX(0);
}

/* تحسين شريط التحميل */
.loading-bar {
  /* تحسين الأداء */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  z-index: 10002;
  /* تحسين الأداء */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.loading-bar.loading {
  /* تحسين الأداء */
  transform: scaleX(1);
}

/* تحسين العناصر المحددة */
.selected {
  /* تحسين الأداء */
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

/* تحسين العناصر المعطلة */
.disabled {
  /* تحسين الأداء */
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* تحسين العناصر المحملة */
.loading {
  /* تحسين الأداء */
  position: relative;
  overflow: hidden;
}

.loading::after {
  /* تحسين الأداء */
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loadingShimmer 1.5s infinite;
}

@keyframes loadingShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* تحسين الطباعة */
@media print {
  /* تحسين الأداء */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .sidebar,
  .top-header,
  .header-actions,
  .notification-btn,
  .user-profile {
    display: none !important;
  }
  
  .main {
    margin: 0 !important;
  }
  
  .content {
    padding: 0 !important;
  }
}

/* تحسين الأجهزة المحمولة */
@media (max-width: 768px) {
  /* تحسين الأداء */
  .lazy-element {
    transform: translateY(10px);
  }
  
  .modal {
    padding: 20px;
  }
  
  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
  }
  
  .notification.show {
    transform: translateY(0);
  }
}

/* تحسين الأجهزة اللوحية */
@media (max-width: 1024px) {
  /* تحسين الأداء */
  .virtual-scroll-table {
    max-height: 300px;
  }
}

/* تحسين الشاشات الكبيرة */
@media (min-width: 1200px) {
  /* تحسين الأداء */
  .content {
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
  /* تحسين الأداء */
  body {
    background: #1a1a2e;
    color: #ffffff;
  }
  
  .modal {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .notification {
    background: #2d2d2d;
    color: #ffffff;
  }
}

/* تحسين تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  /* تحسين الأداء */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .lazy-element {
    transition: none !important;
  }
  
  .modal.show {
    animation: none !important;
  }
  
  .notification.show {
    transition: none !important;
  }
} 