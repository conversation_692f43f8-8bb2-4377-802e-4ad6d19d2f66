# دليل إعداد قاعدة البيانات في Supabase

## 📋 الملفات المرفقة

1. **`company_settings.csv`** - ملف CSV يحتوي على إعدادات الشركات
2. **`supabase_company_settings.sql`** - ملف SQL لإنشاء الجدول في Supabase
3. **`system_settings.json`** - ملف JSON يحتوي على إعدادات النظام الإضافية

## 🚀 خطوات الإعداد

### الخطوة 1: إنشاء مشروع في Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. سجل دخول أو أنشئ حساب جديد
3. انقر على "New Project"
4. اختر اسم للمشروع (مثل: `glass-erp-system`)
5. اختر كلمة مرور قوية لقاعدة البيانات
6. اختر المنطقة الأقرب لك
7. ان<PERSON><PERSON> على "Create new project"

### الخطوة 2: إنشاء الجدول

1. في لوحة التحكم، اذهب إلى **SQL Editor**
2. انقر على **New Query**
3. انسخ محتوى ملف `supabase_company_settings.sql`
4. الصق الكود في المحرر
5. انقر على **Run** لتنفيذ الكود

### الخطوة 3: رفع البيانات (اختياري)

إذا كنت تريد رفع البيانات من ملف CSV:

1. اذهب إلى **Table Editor**
2. اختر جدول `company_settings`
3. انقر على **Import data**
4. اختر ملف `company_settings.csv`
5. تأكد من تطابق الأعمدة
6. انقر على **Import**

### الخطوة 4: إعداد Row Level Security (اختياري)

إذا كنت تريد حماية البيانات:

1. في **SQL Editor**، أزل التعليق من الأسطر التالية:
```sql
ALTER TABLE company_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Enable read access for all users" ON company_settings FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON company_settings FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users only" ON company_settings FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable delete for authenticated users only" ON company_settings FOR DELETE USING (auth.role() = 'authenticated');
```

2. نفذ الكود

## 📊 هيكل الجدول

| العمود | النوع | الوصف |
|--------|-------|--------|
| `id` | SERIAL PRIMARY KEY | المعرف الفريد (تلقائي) |
| `company_name` | VARCHAR(255) | اسم الشركة (مطلوب) |
| `company_address` | TEXT | عنوان الشركة |
| `company_phone` | VARCHAR(50) | رقم الهاتف |
| `company_email` | VARCHAR(255) | البريد الإلكتروني |
| `company_website` | VARCHAR(255) | الموقع الإلكتروني |
| `tax_number` | VARCHAR(50) | الرقم الضريبي |
| `currency` | VARCHAR(10) | العملة (افتراضي: LYD) |
| `logo_url` | TEXT | رابط الشعار |
| `created_at` | TIMESTAMP | تاريخ الإنشاء |
| `updated_at` | TIMESTAMP | تاريخ التحديث |
| `is_active` | BOOLEAN | حالة النشاط |
| `notes` | TEXT | ملاحظات إضافية |

## 🔧 إعدادات النظام الإضافية

ملف `system_settings.json` يحتوي على إعدادات متقدمة للنظام:

- **إعدادات النظام الأساسية** (اللغة، العملة، التوقيت)
- **إعدادات الأعمال** (نوع النشاط، الحجم، طرق الدفع)
- **إعدادات المستخدمين** (الأدوار، الأمان)
- **إعدادات الإشعارات** (البريد، الرسائل النصية)
- **إعدادات التكامل** (المحاسبة، البنوك، الشحن)

## 🔗 ربط التطبيق بـ Supabase

### 1. الحصول على بيانات الاتصال

1. في لوحة التحكم، اذهب إلى **Settings** > **API**
2. انسخ:
   - **Project URL**
   - **anon public key**
   - **service_role key** (للعمليات الإدارية)

### 2. تثبيت مكتبة Supabase

```bash
npm install @supabase/supabase-js
```

### 3. إعداد الاتصال في التطبيق

```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'YOUR_PROJECT_URL'
const supabaseKey = 'YOUR_ANON_KEY'

export const supabase = createClient(supabaseUrl, supabaseKey)
```

### 4. مثال على استخدام API

```javascript
// جلب جميع إعدادات الشركات
const { data, error } = await supabase
  .from('company_settings')
  .select('*')
  .eq('is_active', true)

// إضافة شركة جديدة
const { data, error } = await supabase
  .from('company_settings')
  .insert([
    {
      company_name: 'شركة جديدة',
      company_address: 'العنوان',
      currency: 'LYD'
    }
  ])

// تحديث شركة موجودة
const { data, error } = await supabase
  .from('company_settings')
  .update({ company_name: 'اسم محدث' })
  .eq('id', 1)
```

## 🔒 الأمان

### 1. Row Level Security (RLS)
- يسمح بالتحكم في الوصول للصفوف
- يمكن تحديد من يمكنه القراءة/الكتابة

### 2. Authentication
- دعم تسجيل الدخول بالبريد الإلكتروني
- دعم تسجيل الدخول بـ Google, GitHub, etc.
- إدارة الأدوار والصلاحيات

### 3. API Keys
- `anon key`: للعمليات العامة
- `service_role key`: للعمليات الإدارية (احتفظ بها آمنة)

## 📈 المراقبة والتحليلات

### 1. Logs
- مراقبة الاستعلامات
- مراقبة الأخطاء
- مراقبة الأداء

### 2. Analytics
- إحصائيات الاستخدام
- تحليل الأداء
- تقارير الأمان

## 🆘 الدعم

إذا واجهت أي مشاكل:

1. راجع [وثائق Supabase](https://supabase.com/docs)
2. انضم إلى [مجتمع Supabase](https://github.com/supabase/supabase/discussions)
3. راجع [أمثلة المشاريع](https://github.com/supabase/supabase/tree/master/examples)

## 📝 ملاحظات مهمة

- احتفظ بمفاتيح API آمنة
- استخدم RLS لحماية البيانات
- احتفظ بنسخ احتياطية منتظمة
- راقب استخدام قاعدة البيانات
- حدث الإعدادات حسب احتياجاتك

---

**تم إنشاء هذا الدليل بواسطة Glass ERP System**  
**تاريخ الإنشاء: يناير 2025** 