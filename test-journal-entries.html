<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القيود اليومية - Glass ERP</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/accounts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 2rem;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .test-section h3 {
            color: #374151;
            margin-top: 0;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .test-btn.primary {
            background: #3b82f6;
            color: white;
        }
        
        .test-btn.success {
            background: #10b981;
            color: white;
        }
        
        .test-btn.warning {
            background: #f59e0b;
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .log-container {
            background: #1f2937;
            color: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #374151;
        }
        
        .log-entry.success { color: #10b981; }
        .log-entry.error { color: #ef4444; }
        .log-entry.warning { color: #f59e0b; }
        .log-entry.info { color: #3b82f6; }
        
        #journal-entries {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-book"></i> اختبار نظام القيود اليومية الجديد</h1>
            <p>اختبار شامل لنظام IndexedDB والعرض الجديد للقيود اليومية</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testDataStore()">
                    <i class="fas fa-database"></i> اختبار DataStore
                </button>
                <button class="test-btn success" onclick="testSaveJournalEntry()">
                    <i class="fas fa-save"></i> اختبار حفظ قيد
                </button>
                <button class="test-btn warning" onclick="testLoadJournalEntries()">
                    <i class="fas fa-download"></i> اختبار تحميل القيود
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-table"></i> اختبار العرض</h3>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="createSampleData()">
                    <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                </button>
                <button class="test-btn success" onclick="displayJournalEntries()">
                    <i class="fas fa-eye"></i> عرض القيود
                </button>
                <button class="test-btn warning" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح البيانات
                </button>
            </div>
        </div>

        <div id="journal-entries"></div>

        <div class="log-container">
            <div id="test-log"></div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="js/utils.js"></script>
    <script src="js/accounts.js"></script>
    
    <script>
        // متغيرات الاختبار
        let testDataStore = null;
        let testAccounts = [];
        let testJournalEntries = [];

        // وظيفة تسجيل الأحداث
        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // تهيئة الاختبار
        async function initializeTest() {
            try {
                log('🔄 بدء تهيئة الاختبار...', 'info');
                
                // تهيئة DataStore
                if (typeof DataStore !== 'undefined') {
                    testDataStore = new DataStore('GlassERP_Test', 1);
                    await testDataStore.open();
                    log('✅ تم تهيئة DataStore بنجاح', 'success');
                } else {
                    log('❌ DataStore غير متاح', 'error');
                }
                
                // إنشاء حسابات تجريبية
                testAccounts = [
                    { id: '1100', name: 'النقدية', type: 'asset' },
                    { id: '2100', name: 'حسابات دائنة', type: 'liability' },
                    { id: '4100', name: 'إيرادات المبيعات', type: 'revenue' },
                    { id: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense' }
                ];
                
                log('✅ تم إنشاء الحسابات التجريبية', 'success');
                log('🎯 الاختبار جاهز للتشغيل', 'info');
                
            } catch (error) {
                log(`❌ خطأ في تهيئة الاختبار: ${error.message}`, 'error');
            }
        }

        // اختبار DataStore
        async function testDataStore() {
            try {
                log('🔄 اختبار DataStore...', 'info');
                
                if (!testDataStore) {
                    log('❌ DataStore غير متاح', 'error');
                    return;
                }
                
                // اختبار حفظ الحسابات
                await testDataStore.saveAllAccounts(testAccounts);
                log('✅ تم حفظ الحسابات في IndexedDB', 'success');
                
                // اختبار تحميل الحسابات
                const loadedAccounts = await testDataStore.getAllAccounts();
                log(`✅ تم تحميل ${loadedAccounts.length} حساب من IndexedDB`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في اختبار DataStore: ${error.message}`, 'error');
            }
        }

        // اختبار حفظ قيد يومي
        async function testSaveJournalEntry() {
            try {
                log('🔄 اختبار حفظ قيد يومي...', 'info');
                
                const testEntry = {
                    id: `JE${Date.now()}`,
                    number: 'JE001',
                    date: new Date().toISOString(),
                    description: 'قيد تجريبي للاختبار',
                    entries: [
                        { accountId: '1100', debit: 1000, credit: 0, description: 'نقدية' },
                        { accountId: '4100', debit: 0, credit: 1000, description: 'إيرادات' }
                    ]
                };
                
                if (testDataStore) {
                    await testDataStore.addOrUpdateJournalEntry(testEntry);
                    log('✅ تم حفظ القيد في IndexedDB', 'success');
                }
                
                // إضافة للمصفوفة المحلية
                testJournalEntries.push(testEntry);
                log(`✅ تم إضافة القيد رقم ${testEntry.number}`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في حفظ القيد: ${error.message}`, 'error');
            }
        }

        // اختبار تحميل القيود
        async function testLoadJournalEntries() {
            try {
                log('🔄 اختبار تحميل القيود...', 'info');
                
                if (testDataStore) {
                    const loadedEntries = await testDataStore.getAllJournalEntries();
                    log(`✅ تم تحميل ${loadedEntries.length} قيد من IndexedDB`, 'success');
                    testJournalEntries = loadedEntries;
                } else {
                    log('⚠️ استخدام البيانات المحلية', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في تحميل القيود: ${error.message}`, 'error');
            }
        }

        // إنشاء بيانات تجريبية
        async function createSampleData() {
            try {
                log('🔄 إنشاء بيانات تجريبية...', 'info');
                
                const sampleEntries = [
                    {
                        id: 'JE001',
                        number: 'JE001',
                        date: new Date('2024-01-01').toISOString(),
                        description: 'قيد افتتاحي - رأس المال',
                        entries: [
                            { accountId: '1100', debit: 50000, credit: 0 },
                            { accountId: '3100', debit: 0, credit: 50000 }
                        ]
                    },
                    {
                        id: 'JE002', 
                        number: 'JE002',
                        date: new Date('2024-01-02').toISOString(),
                        description: 'مبيعات نقدية',
                        entries: [
                            { accountId: '1100', debit: 5000, credit: 0 },
                            { accountId: '4100', debit: 0, credit: 5000 }
                        ]
                    },
                    {
                        id: 'JE003',
                        number: 'JE003', 
                        date: new Date('2024-01-03').toISOString(),
                        description: 'شراء بضاعة',
                        entries: [
                            { accountId: '5100', debit: 3000, credit: 0 },
                            { accountId: '1100', debit: 0, credit: 3000 }
                        ]
                    }
                ];
                
                testJournalEntries = sampleEntries;
                
                // حفظ في IndexedDB إذا كان متاحاً
                if (testDataStore) {
                    for (const entry of sampleEntries) {
                        await testDataStore.addOrUpdateJournalEntry(entry);
                    }
                    log('✅ تم حفظ البيانات التجريبية في IndexedDB', 'success');
                }
                
                log(`✅ تم إنشاء ${sampleEntries.length} قيد تجريبي`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في إنشاء البيانات التجريبية: ${error.message}`, 'error');
            }
        }

        // عرض القيود اليومية
        function displayJournalEntries() {
            try {
                log('🔄 عرض القيود اليومية...', 'info');
                
                const container = document.getElementById('journal-entries');
                
                if (testJournalEntries.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #6b7280;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                            <h3>لا توجد قيود يومية</h3>
                            <p>قم بإنشاء بيانات تجريبية أولاً</p>
                        </div>
                    `;
                    return;
                }
                
                // إنشاء الجدول
                let tableRows = '';
                
                testJournalEntries.forEach(entry => {
                    if (!entry || !entry.entries) return;
                    
                    const entryDate = new Date(entry.date).toLocaleDateString('ar-SA');
                    const entryNumber = entry.number || 'غير محدد';
                    const entryDescription = entry.description || 'غير محدد';
                    
                    entry.entries.forEach((item, index) => {
                        if (!item) return;
                        
                        const accountName = getTestAccountName(item.accountId);
                        const debitAmount = item.debit || 0;
                        const creditAmount = item.credit || 0;
                        const amount = debitAmount > 0 ? debitAmount : creditAmount;
                        
                        tableRows += `
                            <tr class="journal-entry-row">
                                <td class="entry-number">${index === 0 ? entryNumber : ''}</td>
                                <td class="entry-date">${index === 0 ? entryDate : ''}</td>
                                <td class="entry-description">${index === 0 ? entryDescription : ''}</td>
                                <td class="account-debit">${debitAmount > 0 ? accountName : ''}</td>
                                <td class="account-credit">${creditAmount > 0 ? accountName : ''}</td>
                                <td class="entry-amount">${formatTestCurrency(amount)}</td>
                            </tr>
                        `;
                    });
                });
                
                container.innerHTML = `
                    <div class="journal-entries-header">
                        <h3><i class="fas fa-book"></i> القيود اليومية المحفوظة</h3>
                        <span style="color: #6b7280;">عدد القيود: ${testJournalEntries.length}</span>
                    </div>
                    <div class="journal-entries-table-container">
                        <table class="journal-entries-table">
                            <thead>
                                <tr>
                                    <th>رقم القيد</th>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>الحساب المدين</th>
                                    <th>الحساب الدائن</th>
                                    <th>القيمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                `;
                
                log(`✅ تم عرض ${testJournalEntries.length} قيد يومي`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في عرض القيود: ${error.message}`, 'error');
            }
        }

        // مسح جميع البيانات
        async function clearAllData() {
            try {
                log('🔄 مسح جميع البيانات...', 'warning');
                
                testJournalEntries = [];
                document.getElementById('journal-entries').innerHTML = '';
                
                if (testDataStore) {
                    // مسح البيانات من IndexedDB
                    const db = await testDataStore.open();
                    const tx = db.transaction(['journalEntries', 'accounts'], 'readwrite');
                    await tx.objectStore('journalEntries').clear();
                    await tx.objectStore('accounts').clear();
                    log('✅ تم مسح البيانات من IndexedDB', 'success');
                }
                
                log('✅ تم مسح جميع البيانات', 'success');
                
            } catch (error) {
                log(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        // وظائف مساعدة
        function getTestAccountName(accountId) {
            const account = testAccounts.find(acc => acc.id === accountId);
            return account ? account.name : `حساب ${accountId}`;
        }

        function formatTestCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // تهيئة الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
