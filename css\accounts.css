/**
 * Accounts Mo<PERSON>le Styles - أنماط موديول الحسابات
 * ==============================================
 */

/* ===== شريط الأدوات العلوي ===== */
.accounts-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    color: white;
}

.toolbar-left h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.fiscal-info {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.fiscal-year, .fiscal-period {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.toolbar-right {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ===== البطاقات الإحصائية ===== */
.accounts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid #f1f5f9;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 600;
}

.stat-value {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e293b;
}

/* ===== تبويبات النظام المحاسبي ===== */
.accounts-tabs {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #f1f5f9;
}

.tab-buttons {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    color: #64748b;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #f1f5f9;
    color: #475569;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-btn i {
    font-size: 1rem;
}

.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* ===== شجرة الحسابات ===== */
.chart-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.chart-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
}

.chart-actions {
    display: flex;
    gap: 0.75rem;
}

.chart-tree {
    max-height: 600px;
    overflow-y: auto;
}

.chart-tree-header {
    display: grid;
    grid-template-columns: 150px 1fr 120px 150px 120px;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: #f1f5f9;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    font-size: 0.9rem;
}

.chart-tree-body {
    padding: 0;
}

.tree-item {
    border-bottom: 1px solid #f1f5f9;
}

.tree-row {
    display: grid;
    grid-template-columns: 150px 1fr 120px 150px 120px;
    gap: 1rem;
    padding: 1rem 1.5rem;
    align-items: center;
    transition: background-color 0.2s ease;
}

.tree-row:hover {
    background: #f8fafc;
}

.tree-row.has-children {
    font-weight: 600;
    background: #f8fafc;
}

.tree-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.account-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #475569;
}

.expand-icon {
    cursor: pointer;
    color: #667eea;
    transition: color 0.2s ease;
}

.expand-icon:hover {
    color: #5a67d8;
}

.account-name {
    font-weight: 500;
    color: #1e293b;
}

.type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.type-asset {
    background: #dbeafe;
    color: #1d4ed8;
}

.type-liability {
    background: #fecaca;
    color: #dc2626;
}

.type-equity {
    background: #d1fae5;
    color: #059669;
}

.type-revenue {
    background: #fef3c7;
    color: #d97706;
}

.type-expense {
    background: #f3e8ff;
    color: #7c3aed;
}

.balance-amount {
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.balance-amount.positive {
    color: #059669;
}

.balance-amount.negative {
    color: #dc2626;
}

.account-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: #f1f5f9;
    color: #64748b;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #e2e8f0;
    color: #475569;
}

.tree-children {
    background: #fafbfc;
    border-left: 3px solid #e2e8f0;
}

/* ===== دفتر اليومية ===== */
.journal-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.journal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.journal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
}

.journal-actions {
    display: flex;
    gap: 0.75rem;
}

.journal-entries {
    padding: 1.5rem;
    max-height: 500px;
    overflow-y: auto;
}

/* ===== دفتر الأستاذ ===== */
.ledger-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.ledger-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.ledger-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
}

.ledger-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 18px;
    background: #f8f9fa;
    padding: 18px 20px 10px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.ledger-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 160px;
}

.ledger-filters label {
    font-size: 14px;
    color: #444;
    font-weight: 500;
    margin-bottom: 2px;
}

.ledger-select, .ledger-date {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 15px;
    background: #fff;
    transition: border 0.2s;
}

.ledger-select:focus, .ledger-date:focus {
    border-color: #764ba2;
    outline: none;
}

.btn-primary, .btn-secondary {
    padding: 8px 18px;
    border-radius: 6px;
    border: none;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    margin-right: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b21a8 100%);
}

.btn-secondary {
    background: #e9ecef;
    color: #333;
}

.btn-secondary:hover {
    background: #d1d5db;
}

.ledger-table-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow-x: auto;
    padding: 0 0 10px 0;
}

.ledger-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
    margin-bottom: 0;
}

.ledger-table th, .ledger-table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}

.ledger-table th {
    background: #f8f9fa;
    color: #222;
    font-weight: 700;
    font-size: 16px;
}

.ledger-table tbody tr:hover {
    background: #f3f0fa;
}

.ledger-table .debit {
    color: #28a745;
    font-weight: 600;
}

.ledger-table .credit {
    color: #dc3545;
    font-weight: 600;
}

.ledger-table .balance {
    color: #764ba2;
    font-weight: 700;
}

.ledger-summary-row {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    font-weight: 700;
    font-size: 16px;
}

.ledger-summary-row td {
    color: #fff !important;
}

.btn-icon {
    background: none;
    border: none;
    color: #764ba2;
    font-size: 18px;
    cursor: pointer;
    margin: 0 2px;
    transition: color 0.2s;
}

.btn-icon:hover {
    color: #dc3545;
}

.empty-row {
    text-align: center;
    color: #888;
    font-size: 16px;
    padding: 30px 0;
}

@media (max-width: 900px) {
    .ledger-filters {
        flex-direction: column;
        gap: 10px;
        padding: 12px 8px 8px 8px;
    }
    .ledger-table th, .ledger-table td {
        padding: 8px 4px;
        font-size: 13px;
    }
}

/* ===== ميزان المراجعة ===== */
.trial-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.trial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.trial-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
}

.trial-actions {
    display: flex;
    gap: 0.75rem;
}

.trial-balance {
    padding: 1.5rem;
    max-height: 500px;
    overflow-y: auto;
}

/* ===== التقارير المالية ===== */
.reports-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
}

.reports-header {
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.reports-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.report-card {
    background: white;
    border: 2px solid #f1f5f9;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.report-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.15);
}

.report-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.report-card h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    color: #1e293b;
    font-weight: 600;
}

.report-card p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
}

/* ===== الأزرار ===== */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-sm {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-sm:hover {
    background: #e2e8f0;
    color: #1e293b;
}

/* ===== التجاوب ===== */
@media (max-width: 768px) {
    .accounts-toolbar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .toolbar-right {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .accounts-stats {
        grid-template-columns: 1fr;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
    
    .chart-tree-header,
    .tree-row {
        grid-template-columns: 100px 1fr 80px 100px 80px;
        gap: 0.5rem;
        font-size: 0.8rem;
    }
    
    .ledger-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-pane.active {
    animation: fadeIn 0.3s ease;
}

.stat-card {
    animation: fadeIn 0.3s ease;
}

.report-card {
    animation: fadeIn 0.3s ease;
}

/* ===== الحالات الفارغة ===== */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #64748b;
}

.empty-state i {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    color: #475569;
}

.empty-state p {
    margin: 0 0 1.5rem 0;
    font-size: 0.9rem;
}

/* ===== الجداول ===== */
.trial-balance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.trial-balance-table th,
.trial-balance-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.trial-balance-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #475569;
    font-size: 0.9rem;
}

.trial-balance-table td {
    color: #1e293b;
}

.trial-balance-table .debit {
    color: #059669;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.trial-balance-table .credit {
    color: #dc2626;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* ===== القيود اليومية ===== */
.journal-entry {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.entry-header {
    background: #f8fafc;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.entry-date {
    font-weight: 600;
    color: #475569;
}

.entry-number {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.entry-description {
    color: #1e293b;
    font-weight: 500;
}

.entry-details {
    padding: 1rem;
}

.entry-line {
    display: grid;
    grid-template-columns: 1fr 120px 120px;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
    align-items: center;
}

.entry-line:last-child {
    border-bottom: none;
}

.entry-line .debit {
    color: #059669;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    text-align: left;
}

.entry-line .credit {
    color: #dc2626;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    text-align: left;
}

/* ===== النوافذ المنبثقة ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content, .large-modal {
    max-width: 1200px !important;
    width: 80vw !important;
    min-width: 350px;
    padding: 2.5rem 2rem;
    box-sizing: border-box;
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 0 0 15px 15px;
}

/* ===== نماذج النوافذ المنبثقة ===== */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ===== نافذة إضافة القيود اليومية ===== */
.journal-header-info {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.journal-entries-container {
    margin-bottom: 1.5rem;
    padding: 1rem 0.5rem;
    background: #f8fafc;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(102,126,234,0.04);
}

.journal-entries-header, .journal-entry-line {
    display: grid;
    grid-template-columns: 2.5fr 1.2fr 1.2fr 2fr 0.7fr;
    gap: 0.7rem;
    align-items: center;
}

.journal-entry-line select,
.journal-entry-line input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.journal-totals {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    margin-top: 1rem;
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.total-item.balance {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.total-item span:first-child {
    font-weight: 600;
    color: #475569;
}

.total-item span:last-child {
    font-weight: 700;
    font-family: 'Courier New', monospace;
    color: #1e293b;
}

/* ===== الرسوم المتحركة ===== */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== شريط التمرير المخصص ===== */
.chart-tree::-webkit-scrollbar,
.journal-entries::-webkit-scrollbar,
.ledger-entries::-webkit-scrollbar,
.trial-balance::-webkit-scrollbar {
    width: 8px;
}

.chart-tree::-webkit-scrollbar-track,
.journal-entries::-webkit-scrollbar-track,
.ledger-entries::-webkit-scrollbar-track,
.trial-balance::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.chart-tree::-webkit-scrollbar-thumb,
.journal-entries::-webkit-scrollbar-thumb,
.ledger-entries::-webkit-scrollbar-thumb,
.trial-balance::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.chart-tree::-webkit-scrollbar-thumb:hover,
.journal-entries::-webkit-scrollbar-thumb:hover,
.ledger-entries::-webkit-scrollbar-thumb:hover,
.trial-balance::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* الإشعارات البسيطة */
.simple-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 9999;
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.simple-notification.success {
    border-left: 4px solid #10b981;
    color: #065f46;
}

.simple-notification.error {
    border-left: 4px solid #ef4444;
    color: #991b1b;
}

.simple-notification.info {
    border-left: 4px solid #3b82f6;
    color: #1e40af;
}

.simple-notification i {
    font-size: 16px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ========================================
   Accounts Module Styles - تنسيقات موديول الحسابات
   ======================================== */

/* ===== النافذة المنبثقة للتقارير ===== */
.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.report-modal.show {
    opacity: 1;
    visibility: visible;
}

.report-modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    width: 95%;
    max-width: 1400px;
    height: 90%;
    max-height: 900px;
    box-shadow: 
        0 25px 80px rgba(0, 0, 0, 0.3),
        0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.report-modal.show .report-modal-content {
    transform: scale(1) translateY(0);
}

/* ===== رأس النافذة المنبثقة ===== */
.report-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #8b5cf6 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 24px 24px 0 0;
    position: relative;
    overflow: hidden;
}

.report-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.report-modal-header:hover::before {
    left: 100%;
}

.report-header-info h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.report-header-info h2 i {
    font-size: 1.6rem;
    color: rgba(255, 255, 255, 0.9);
}

.report-meta {
    display: flex;
    gap: 2rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.report-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.report-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-report {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
    backdrop-filter: blur(10px);
}

.btn-report:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.btn-report.close-btn:hover {
    background: rgba(239, 68, 68, 0.8);
    border-color: rgba(239, 68, 68, 0.8);
}

.btn-report i {
    font-size: 1.1rem;
}

/* ===== جسم النافذة المنبثقة ===== */
.report-modal-body {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: #ffffff;
}

.report-company-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.company-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 12px;
    background: white;
    padding: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.company-details h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.company-details p {
    color: #64748b;
    margin: 0.25rem 0;
    font-size: 0.95rem;
}

/* ===== محتوى التقرير ===== */
.report-content {
    background: white;
    border-radius: 16px;
    overflow: hidden;
}

.report-section {
    margin-bottom: 2.5rem;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 700;
    padding: 1rem 1.5rem;
    border-bottom: 2px solid #e2e8f0;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #8b5cf6);
}

.account-info {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    color: #64748b;
    border-bottom: 1px solid #e2e8f0;
}

/* ===== الجداول ===== */
.report-content table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.report-content th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    font-weight: 600;
    padding: 1rem;
    text-align: right;
    border-bottom: 2px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.report-content td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    color: #374151;
}

.report-content tr:hover {
    background: #f8fafc;
}

.report-content .debit {
    color: #059669;
    font-weight: 600;
}

.report-content .credit {
    color: #dc2626;
    font-weight: 600;
}

.report-content .positive {
    color: #059669;
}

.report-content .negative {
    color: #dc2626;
}

.report-content .total-row {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 700;
    color: #1e293b;
    border-top: 2px solid #e2e8f0;
}

.report-content .total-row td {
    padding: 1.25rem 1rem;
}

/* ===== بطاقات الملخص ===== */
.summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.summary-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.summary-value {
    font-size: 2rem;
    font-weight: 800;
    color: #059669;
    line-height: 1.2;
}

.summary-value.negative {
    color: #dc2626;
}

/* ===== رسائل الحالة ===== */
.no-transactions {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 3rem 2rem;
    background: #f8fafc;
    border-radius: 8px;
    margin: 1rem 0;
}

.error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #fca5a5;
    margin: 1rem 0;
}

/* ===== تحسينات للطباعة ===== */
@media print {
    .report-modal {
        position: static;
        background: white;
    }
    
    .report-modal-content {
        width: 100%;
        height: auto;
        max-width: none;
        max-height: none;
        box-shadow: none;
        border: none;
        border-radius: 0;
    }
    
    .report-modal-header {
        background: #f8fafc;
        color: #1e293b;
        border-radius: 0;
    }
    
    .btn-report {
        display: none;
    }
    
    .report-modal-body {
        padding: 1rem;
    }
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .report-modal-content {
        width: 98%;
        height: 95%;
        border-radius: 16px;
    }
    
    .report-modal-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .report-header-info h2 {
        font-size: 1.4rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .report-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .report-actions {
        gap: 0.5rem;
    }
    
    .btn-report {
        min-width: 40px;
        height: 40px;
        padding: 0.5rem;
    }
    
    .report-modal-body {
        padding: 1rem;
    }
    
    .report-company-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .company-logo {
        width: 60px;
        height: 60px;
    }
    
    .report-content table {
        font-size: 0.8rem;
    }
    
    .report-content th,
    .report-content td {
        padding: 0.75rem 0.5rem;
    }
    
    .summary-card {
        padding: 1.5rem;
    }
    
    .summary-value {
        font-size: 1.5rem;
    }
}

/* ===== تحسينات للأجهزة اللوحية ===== */
@media (min-width: 769px) and (max-width: 1024px) {
    .report-modal-content {
        width: 96%;
        max-width: 1200px;
    }
    
    .report-modal-header {
        padding: 1.25rem 1.5rem;
    }
    
    .report-modal-body {
        padding: 1.5rem;
    }
    
    .report-content table {
        font-size: 0.85rem;
    }
}

/* ===== تأثيرات إضافية ===== */
.report-modal::-webkit-scrollbar {
    width: 8px;
}

.report-modal::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.report-modal::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #8b5cf6);
    border-radius: 4px;
}

.report-modal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #7c3aed);
}

/* ===== تحسينات الأداء ===== */
.report-modal * {
    box-sizing: border-box;
}

.report-content {
    contain: layout style paint;
}

/* ===== تحسينات إمكانية الوصول ===== */
.btn-report:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.report-modal:focus-within {
    outline: none;
}

/* ===== تحسينات للشاشات عالية الدقة ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .report-modal-content {
        border-width: 0.5px;
    }
    
    .report-content th,
    .report-content td {
        border-width: 0.5px;
    }
}

/* إضافة CSS للتبويبات الجديدة */

/* قائمة الدخل */
.income-container {
    padding: 20px;
}

.income-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.income-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.income-actions {
    display: flex;
    gap: 10px;
}

.income-actions .btn-sm {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.income-actions .btn-sm:first-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.income-actions .btn-sm:last-child {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.income-actions .btn-sm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.income-statement {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.income-section {
    margin-bottom: 25px;
}

.income-section h4 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

/* الميزانية العمومية */
.balance-container {
    padding: 20px;
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.balance-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.balance-actions {
    display: flex;
    gap: 10px;
}

.balance-actions .btn-sm {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.balance-actions .btn-sm:first-child {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.balance-actions .btn-sm:last-child {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.balance-actions .btn-sm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.balance-sheet {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.balance-sheet-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 20px;
}

.balance-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.balance-section {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.balance-section h4 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    padding: 15px 20px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

/* تحسينات عامة للتقارير */
.report-section {
    padding: 20px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    padding: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin-top: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.summary-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    opacity: 0.9;
}

.summary-value {
    font-size: 24px;
    font-weight: 700;
}

.summary-value.positive {
    color: #28a745;
}

.summary-value.negative {
    color: #dc3545;
}

/* تحسينات الجداول */
.income-section table,
.balance-section table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.income-section th,
.income-section td,
.balance-section th,
.balance-section td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.income-section th,
.balance-section th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.total-row {
    background: #e9ecef !important;
    font-weight: 600;
}

.grand-total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 700;
}

.grand-total td {
    color: white !important;
}

/* تحسينات للألوان */
.positive {
    color: #28a745;
    font-weight: 600;
}

.negative {
    color: #dc3545;
    font-weight: 600;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .balance-sheet-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .income-header,
    .balance-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .income-actions,
    .balance-actions {
        justify-content: center;
    }
}

/* تحسينات للطباعة */
@media print {
    .income-actions,
    .balance-actions {
        display: none;
    }
    
    .balance-sheet-layout {
        grid-template-columns: 1fr 1fr;
    }
    
    .summary-card {
        break-inside: avoid;
    }
}

/* ميزان المراجعة العصري */
.trial-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 18px;
    background: #f8f9fa;
    padding: 18px 20px 10px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.trial-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 160px;
}
.trial-filters label {
    font-size: 14px;
    color: #444;
    font-weight: 500;
    margin-bottom: 2px;
}
.trial-date {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 15px;
    background: #fff;
    transition: border 0.2s;
}
.trial-date:focus {
    border-color: #764ba2;
    outline: none;
}
.trial-table-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow-x: auto;
    padding: 0 0 10px 0;
}
.trial-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
    margin-bottom: 0;
}
.trial-table th, .trial-table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}
.trial-table th {
    background: #f8f9fa;
    color: #222;
    font-weight: 700;
    font-size: 16px;
}
.trial-table tbody tr:hover {
    background: #f3f0fa;
}
.trial-table .debit {
    color: #28a745;
    font-weight: 600;
}
.trial-table .credit {
    color: #dc3545;
    font-weight: 600;
}
.trial-table .balance {
    color: #764ba2;
    font-weight: 700;
}
.trial-summary-row {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    font-weight: 700;
    font-size: 16px;
}
.trial-summary-row td {
    color: #fff !important;
}
.trial-balance-check-row td {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #28a745;
    background: #f8f9fa;
    border-bottom: none;
}
.trial-balance-check-row td:contains('غير متوازن') {
    color: #dc3545;
}
@media (max-width: 900px) {
    .trial-filters {
        flex-direction: column;
        gap: 10px;
        padding: 12px 8px 8px 8px;
    }
    .trial-table th, .trial-table td {
        padding: 8px 4px;
        font-size: 13px;
    }
}

/* ===== أنماط الأزرار الجديدة ===== */
.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* تحسين شريط الأدوات للأزرار الجديدة */
@media (max-width: 1200px) {
    .toolbar-right {
        gap: 0.5rem;
    }
    
    .toolbar-right button {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .toolbar-right {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        margin-top: 1rem;
    }
    
    .toolbar-right button {
        width: 100%;
        justify-content: center;
    }
} 