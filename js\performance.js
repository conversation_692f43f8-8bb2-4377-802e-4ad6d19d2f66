/**
 * Performance Optimization Module - موديول تحسين الأداء
 * ========================================
 */

// متغيرات تحسين الأداء
let performanceCache = new Map();
let debounceTimers = new Map();
let lazyLoadQueue = [];
let isProcessingLazyLoad = false;

/**
 * تهيئة نظام تحسين الأداء
 */
function initializePerformanceOptimizations() {
    console.log('⚡ تهيئة تحسينات الأداء...');
    
    // تفعيل Lazy Loading
    initializeLazyLoading();
    
    // تفعيل Debouncing
    initializeDebouncing();
    
    // تفعيل Virtual Scrolling
    initializeVirtualScrolling();
    
    // تحسين الذاكرة
    initializeMemoryOptimization();
    
    console.log('✅ تم تفعيل تحسينات الأداء');
}

/**
 * تهيئة Lazy Loading
 */
function initializeLazyLoading() {
    // مراقبة العناصر المرئية
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const dataType = element.dataset.lazyType;
                
                if (dataType) {
                    lazyLoadQueue.push({ element, dataType });
                    processLazyLoadQueue();
                }
            }
        });
    }, {
        rootMargin: '50px'
    });
    
    // إضافة مراقبة للعناصر
    document.querySelectorAll('[data-lazy-type]').forEach(el => {
        observer.observe(el);
    });
}

/**
 * معالجة قائمة Lazy Loading
 */
function processLazyLoadQueue() {
    if (isProcessingLazyLoad || lazyLoadQueue.length === 0) return;
    
    isProcessingLazyLoad = true;
    
    setTimeout(() => {
        const items = lazyLoadQueue.splice(0, 5); // معالجة 5 عناصر في المرة
        
        items.forEach(item => {
            loadLazyContent(item.element, item.dataType);
        });
        
        isProcessingLazyLoad = false;
        
        // معالجة المزيد إذا كان هناك
        if (lazyLoadQueue.length > 0) {
            processLazyLoadQueue();
        }
    }, 100);
}

/**
 * تحميل المحتوى المتأخر
 */
function loadLazyContent(element, dataType) {
    switch (dataType) {
        case 'chart':
            loadChartData(element);
            break;
        case 'table':
            loadTableData(element);
            break;
        case 'stats':
            loadStatsData(element);
            break;
        default:
            console.warn(`⚠️ نوع بيانات غير معروف: ${dataType}`);
    }
}

/**
 * تحميل بيانات الرسم البياني
 */
function loadChartData(element) {
    const chartId = element.dataset.chartId;
    if (!chartId) return;
    
    // تحميل البيانات من الـ cache أولاً
    const cachedData = performanceCache.get(`chart_${chartId}`);
    if (cachedData) {
        element.innerHTML = cachedData;
        return;
    }
    
    // تحميل البيانات الجديدة
    const chartData = generateChartData(chartId);
    if (chartData) {
        element.innerHTML = chartData;
        performanceCache.set(`chart_${chartId}`, chartData);
    }
}

/**
 * تحميل بيانات الجدول
 */
function loadTableData(element) {
    const tableId = element.dataset.tableId;
    if (!tableId) return;
    
    const cachedData = performanceCache.get(`table_${tableId}`);
    if (cachedData) {
        element.innerHTML = cachedData;
        return;
    }
    
    const tableData = generateTableData(tableId);
    if (tableData) {
        element.innerHTML = tableData;
        performanceCache.set(`table_${tableId}`, tableData);
    }
}

/**
 * تحميل بيانات الإحصائيات
 */
function loadStatsData(element) {
    const statsId = element.dataset.statsId;
    if (!statsId) return;
    
    const cachedData = performanceCache.get(`stats_${statsId}`);
    if (cachedData) {
        element.innerHTML = cachedData;
        return;
    }
    
    const statsData = generateStatsData(statsId);
    if (statsData) {
        element.innerHTML = statsData;
        performanceCache.set(`stats_${statsId}`, statsData);
    }
}

/**
 * تهيئة Debouncing
 */
function initializeDebouncing() {
    // إضافة debouncing للأحداث المتكررة
    const debouncedEvents = [
        'resize',
        'scroll',
        'input',
        'change'
    ];
    
    debouncedEvents.forEach(eventType => {
        window.addEventListener(eventType, debounce((e) => {
            handleDebouncedEvent(eventType, e);
        }, 150));
    });
}

/**
 * دالة Debounce
 */
function debounce(func, wait) {
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(debounceTimers.get(func));
            func(...args);
        };
        
        clearTimeout(debounceTimers.get(func));
        debounceTimers.set(func, setTimeout(later, wait));
    };
}

/**
 * معالجة الأحداث المطموسة
 */
function handleDebouncedEvent(eventType, event) {
    switch (eventType) {
        case 'resize':
            handleResize();
            break;
        case 'scroll':
            handleScroll();
            break;
        case 'input':
            handleInput(event);
            break;
        case 'change':
            handleChange(event);
            break;
    }
}

/**
 * معالجة تغيير الحجم
 */
function handleResize() {
    // تحديث الرسوم البيانية
    if (typeof performanceChart !== 'undefined' && performanceChart) {
        performanceChart.resize();
    }
    if (typeof assetsChart !== 'undefined' && assetsChart) {
        assetsChart.resize();
    }
    if (typeof cashFlowChart !== 'undefined' && cashFlowChart) {
        cashFlowChart.resize();
    }
}

/**
 * معالجة التمرير
 */
function handleScroll() {
    // تحسين الأداء عند التمرير
    const scrollTop = window.pageYOffset;
    
    // إخفاء/إظهار العناصر حسب الحاجة
    document.querySelectorAll('.lazy-element').forEach(el => {
        const rect = el.getBoundingClientRect();
        if (rect.top < window.innerHeight && rect.bottom > 0) {
            el.classList.add('visible');
        } else {
            el.classList.remove('visible');
        }
    });
}

/**
 * معالجة الإدخال
 */
function handleInput(event) {
    const target = event.target;
    
    // حفظ تلقائي للنماذج
    if (target.closest('form')) {
        debounce(() => {
            if (typeof saveSettingsAuto === 'function') {
                saveSettingsAuto();
            }
        }, 500)();
    }
}

/**
 * معالجة التغيير
 */
function handleChange(event) {
    const target = event.target;
    
    // تحديث البيانات المرتبطة
    if (target.id === 'currency') {
        updateCurrencyFormat(target.value);
    }
}

/**
 * تهيئة Virtual Scrolling
 */
function initializeVirtualScrolling() {
    const tables = document.querySelectorAll('.virtual-scroll-table');
    
    tables.forEach(table => {
        const tbody = table.querySelector('tbody');
        if (!tbody) return;
        
        const items = Array.from(tbody.children);
        const itemHeight = 50; // ارتفاع كل صف
        const visibleItems = Math.ceil(window.innerHeight / itemHeight);
        
        // إظهار العناصر المرئية فقط
        function updateVisibleItems() {
            const scrollTop = table.scrollTop;
            const startIndex = Math.floor(scrollTop / itemHeight);
            const endIndex = Math.min(startIndex + visibleItems, items.length);
            
            items.forEach((item, index) => {
                if (index >= startIndex && index < endIndex) {
                    item.style.display = '';
                    item.style.transform = `translateY(${index * itemHeight}px)`;
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        table.addEventListener('scroll', debounce(updateVisibleItems, 16));
        updateVisibleItems();
    });
}

/**
 * تهيئة تحسين الذاكرة
 */
function initializeMemoryOptimization() {
    // تنظيف الـ cache دورياً
    setInterval(() => {
        cleanupCache();
    }, 300000); // كل 5 دقائق
    
    // تنظيف الذاكرة عند تغيير الصفحة
    window.addEventListener('beforeunload', () => {
        cleanupMemory();
    });
}

/**
 * تنظيف الـ Cache
 */
function cleanupCache() {
    const maxCacheSize = 100;
    
    if (performanceCache.size > maxCacheSize) {
        const entries = Array.from(performanceCache.entries());
        const toDelete = entries.slice(0, performanceCache.size - maxCacheSize);
        
        toDelete.forEach(([key]) => {
            performanceCache.delete(key);
        });
        
        console.log(`🧹 تم تنظيف ${toDelete.length} عنصر من الـ cache`);
    }
}

/**
 * تنظيف الذاكرة
 */
function cleanupMemory() {
    // إيقاف الـ timers
    debounceTimers.forEach(timer => clearTimeout(timer));
    debounceTimers.clear();
    
    // تنظيف الـ cache
    performanceCache.clear();
    
    // إيقاف الرسوم البيانية
    if (typeof performanceChart !== 'undefined' && performanceChart) {
        performanceChart.destroy();
    }
    if (typeof assetsChart !== 'undefined' && assetsChart) {
        assetsChart.destroy();
    }
    if (typeof cashFlowChart !== 'undefined' && cashFlowChart) {
        cashFlowChart.destroy();
    }
}

/**
 * دالة مساعدة لإنشاء بيانات الرسم البياني
 */
function generateChartData(chartId) {
    // يمكن إضافة منطق لإنشاء بيانات الرسم البياني
    return `<div class="chart-placeholder">تحميل الرسم البياني...</div>`;
}

/**
 * دالة مساعدة لإنشاء بيانات الجدول
 */
function generateTableData(tableId) {
    // يمكن إضافة منطق لإنشاء بيانات الجدول
    return `<div class="table-placeholder">تحميل الجدول...</div>`;
}

/**
 * دالة مساعدة لإنشاء بيانات الإحصائيات
 */
function generateStatsData(statsId) {
    // يمكن إضافة منطق لإنشاء بيانات الإحصائيات
    return `<div class="stats-placeholder">تحميل الإحصائيات...</div>`;
}

/**
 * دالة مساعدة لتحديث تنسيق العملة
 */
function updateCurrencyFormat(currencyCode) {
    // تحديث تنسيق العملة في جميع أنحاء النظام
    document.querySelectorAll('.currency-format').forEach(el => {
        el.dataset.currency = currencyCode;
    });
    
    // تحديث الـ cache
    performanceCache.set('currency', currencyCode);
}

/**
 * دالة مساعدة لقياس الأداء
 */
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    console.log(`⏱️ ${name}: ${(end - start).toFixed(2)}ms`);
    return result;
}

// تصدير الدوال للاستخدام في الملفات الأخرى
window.GlassERP = window.GlassERP || {};
window.GlassERP.Performance = {
    initializePerformanceOptimizations,
    debounce,
    measurePerformance,
    cleanupCache,
    cleanupMemory
}; 