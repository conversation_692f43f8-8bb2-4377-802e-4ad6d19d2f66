/**
 * Dashboard Module - موديول لوحة التحكم
 * ========================================
 */

// متغيرات الرسوم البيانية
let performanceChart, assetsChart, cashFlowChart;

/**
 * تهيئة الداشبورد
 */
async function initializeDashboard() {
    console.log('📊 تهيئة لوحة التحكم...');
    
    // استخدام التهيئة المحسنة دائماً
    if (typeof initializeDashboardEnhanced === 'function') {
        await initializeDashboardEnhanced();
    } else {
        // إنشاء محتوى الداشبورد
        createDashboardContent();
        
        // تحديث الإحصائيات
        await updateDashboardStats();
        
        // تهيئة الرسوم البيانية
        initializeCharts();
    }
    
    console.log('✅ تم تهيئة لوحة التحكم بنجاح');
}

/**
 * إنشاء محتوى الداشبورد
 */
function createDashboardContent() {
    const dashboardContainer = document.getElementById('dashboard');
    
    dashboardContainer.innerHTML = `
        <div class="dashboard-grid">
            <!-- إجمالي الأصول -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الأصول</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-assets">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- إجمالي الإيرادات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الإيرادات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-revenue">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- إجمالي المصروفات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي المصروفات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                        <i class="fas fa-receipt"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-expenses">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- صافي الدخل -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">صافي الدخل</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value" id="net-income">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- عدد القيود اليومية -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">القيود اليومية</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <i class="fas fa-book"></i>
                    </div>
                </div>
                <div class="stat-value" id="journal-count">0</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد قيود جديدة
                </div>
            </div>

            <!-- عدد الحسابات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">عدد الحسابات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                        <i class="fas fa-address-book"></i>
                    </div>
                </div>
                <div class="stat-value" id="accounts-count">0</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد حسابات جديدة
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">تحليل الأداء المالي</h3>
                <div class="chart-actions">
                    <button class="chart-btn active" onclick="updateChart('monthly')">شهري</button>
                    <button class="chart-btn" onclick="updateChart('quarterly')">ربع سنوي</button>
                    <button class="chart-btn" onclick="updateChart('yearly')">سنوي</button>
                </div>
            </div>
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">توزيع الأصول</h3>
            </div>
            <canvas id="assetsChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">تحليل التدفق النقدي</h3>
            </div>
            <canvas id="cashFlowChart" width="400" height="200"></canvas>
        </div>
    `;
}

/**
 * تحديث الداشبورد
 */
async function updateDashboard() {
    console.log('🔄 بدء تحديث الداشبورد...');
    
    try {
        // تحديث الإحصائيات
        await updateDashboardStats();
        
        // تهيئة الرسوم البيانية
        initializeCharts();
        
        // إضافة معلومات الشركة
        await addCompanyInfoToDashboard();
        
        // إضافة معلومات الجلسة
        addSessionInfoToDashboard();
        
        console.log('✅ تم تحديث الداشبورد بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تحديث الداشبورد:', error);
    }
}

/**
 * تحديث إحصائيات الداشبورد
 */
async function updateDashboardStats() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            // جلب الحسابات
            const accounts = await sqliteDB.getAllAccounts();
            
            // جلب القيود اليومية
            const journalEntries = await sqliteDB.getAllJournalEntries();
            
            if (accounts && journalEntries) {
                const assets = accounts.filter(a => a && a.type === 'asset');
                const revenues = accounts.filter(a => a && a.type === 'revenue');
                const expenses = accounts.filter(a => a && a.type === 'expense');

                const totalAssets = assets.reduce((sum, a) => sum + (a.balance || 0), 0);
                const totalRevenue = revenues.reduce((sum, a) => sum + (a.balance || 0), 0);
                const totalExpenses = expenses.reduce((sum, a) => sum + (a.balance || 0), 0);
                const netIncome = totalRevenue - totalExpenses;

                // تحديث العناصر في الواجهة
                const totalAssetsElement = document.getElementById('total-assets');
                const totalRevenueElement = document.getElementById('total-revenue');
                const totalExpensesElement = document.getElementById('total-expenses');
                const netIncomeElement = document.getElementById('net-income');
                const journalCountElement = document.getElementById('journal-count');
                const accountsCountElement = document.getElementById('accounts-count');

                if (totalAssetsElement) totalAssetsElement.textContent = formatCurrency(totalAssets);
                if (totalRevenueElement) totalRevenueElement.textContent = formatCurrency(totalRevenue);
                if (totalExpensesElement) totalExpensesElement.textContent = formatCurrency(totalExpenses);
                if (netIncomeElement) netIncomeElement.textContent = formatCurrency(netIncome);
                if (journalCountElement) journalCountElement.textContent = journalEntries.length;
                if (accountsCountElement) accountsCountElement.textContent = accounts.length;

                console.log('📊 تم تحديث إحصائيات الداشبورد');
            } else {
                console.warn('⚠️ بيانات الحسابات غير متوفرة');
            }
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في تحديث إحصائيات الداشبورد:', error);
    }
}

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts() {
    console.log('📊 بدء تهيئة الرسوم البيانية...');
    
    // تهيئة جميع الرسوم البيانية فوراً
    try {
        // رسم بياني الأداء المالي
        if (!performanceChart) {
            createPerformanceChart();
            console.log('✅ تم إنشاء رسم بياني الأداء المالي');
        }
        
        // رسم بياني توزيع الأصول
        if (!assetsChart) {
            createAssetsChart();
            console.log('✅ تم إنشاء رسم بياني توزيع الأصول');
        }
        
        // رسم بياني التدفق النقدي
        if (!cashFlowChart) {
            createCashFlowChart();
            console.log('✅ تم إنشاء رسم بياني التدفق النقدي');
        }
        
        console.log('🎉 تم تهيئة جميع الرسوم البيانية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة الرسوم البيانية:', error);
    }
}

/**
 * رسم بياني للأداء المالي
 */
function createPerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    if (!ctx) return;
    
    if (performanceChart) {
        performanceChart.destroy();
    }

    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
            const revenueData = [0, 0, 0, 0, 0, 0];
        const expenseData = [0, 0, 0, 0, 0, 0];

    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'الإيرادات',
                    data: revenueData,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'المصروفات',
                    data: expenseData,
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

/**
 * رسم بياني لتوزيع الأصول
 */
function createAssetsChart() {
    const ctx = document.getElementById('assetsChart');
    if (!ctx) return;
    
    if (assetsChart) {
        assetsChart.destroy();
    }

    // استخدام بيانات الحسابات الفعلية إذا كانت متوفرة
    let labels = ['لا توجد بيانات'];
    let data = [1];
    let colors = ['#e5e7eb'];

    if (typeof accounts !== 'undefined' && Array.isArray(accounts)) {
        const assetAccounts = accounts.filter(a => a && a.type === 'asset' && (a.balance || 0) > 0);
        if (assetAccounts.length > 0) {
            labels = assetAccounts.map(a => a.name || 'حساب غير محدد');
            data = assetAccounts.map(a => a.balance || 0);
            colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];
        }
    }

    assetsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, data.length),
                borderWidth: 0,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 11
                        },
                        padding: 15
                    }
                }
            }
        }
    });
}

/**
 * رسم بياني للتدفق النقدي
 */
function createCashFlowChart() {
    const ctx = document.getElementById('cashFlowChart');
    if (!ctx) return;
    
    if (cashFlowChart) {
        cashFlowChart.destroy();
    }

    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
    const cashIn = [0, 0, 0, 0, 0, 0];
    const cashOut = [0, 0, 0, 0, 0, 0];

    cashFlowChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'التدفق الداخل',
                    data: cashIn,
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: '#10b981',
                    borderWidth: 1
                },
                {
                    label: 'التدفق الخارج',
                    data: cashOut,
                    backgroundColor: 'rgba(239, 68, 68, 0.8)',
                    borderColor: '#ef4444',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

/**
 * تحديث الرسم البياني حسب الفترة
 */
function updateChart(period) {
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
    
    console.log(`📊 تحديث الرسم البياني للفترة: ${period}`);
    
    // هنا يمكن إضافة منطق لتحديث البيانات حسب الفترة المحددة
    // يمكن جلب بيانات مختلفة من localStorage أو حسابها
}

// تهيئة الداشبورد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قليل للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        if (document.getElementById('dashboard').classList.contains('active')) {
            initializeDashboard();
        }
    }, 100);
});

/**
 * التحسينات الجديدة للداشبورد
 * ========================================
 */

/**
 * إضافة معلومات الشركة للداشبورد
 */
async function addCompanyInfoToDashboard() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const settings = await sqliteDB.getCompanySettings();
            if (settings && settings.name) {
                updateDashboardCompanyInfo(settings);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في إضافة معلومات الشركة للداشبورد:', error);
    }
}

/**
 * تحديث معلومات الشركة في الداشبورد
 */
function updateDashboardCompanyInfo(settings) {
    const dashboardContainer = document.getElementById('dashboard');
    if (!dashboardContainer) return;

    // البحث عن عنصر معلومات الشركة أو إنشاؤه
    let companyInfoElement = dashboardContainer.querySelector('.company-info');
    if (!companyInfoElement) {
        companyInfoElement = document.createElement('div');
        companyInfoElement.className = 'company-info';
        companyInfoElement.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        `;
        dashboardContainer.insertBefore(companyInfoElement, dashboardContainer.firstChild);
    }

    companyInfoElement.innerHTML = `
        <h2 style="margin: 0 0 10px 0; font-size: 24px;">
            <i class="fas fa-building"></i> ${settings.name || 'شركة زجاج التجارية'}
        </h2>
        <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">
            <i class="fas fa-map-marker-alt"></i> ${settings.address || 'الرياض، المملكة العربية السعودية'}
        </p>
        <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">
            <i class="fas fa-phone"></i> ${settings.phone || '966-11-1234567'}
        </p>
        <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">
            <i class="fas fa-envelope"></i> ${settings.email || '<EMAIL>'}
        </p>
        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
            <small style="opacity: 0.8;">
                <i class="fas fa-calendar"></i> السنة المالية: ${settings.fiscalYearStart || '01-01'}
                <span style="margin: 0 10px;">|</span>
                <i class="fas fa-money-bill"></i> العملة: ${settings.currency || 'SAR'}
            </small>
        </div>
    `;
}

/**
 * تحميل معلومات الشركة للداشبورد
 */
async function loadCompanyInfoForDashboard() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const settings = await sqliteDB.getCompanySettings();
            if (settings) {
                updateDashboardCompanyInfo(settings);
                return settings;
            }
        }
        return null;
    } catch (error) {
        console.error('❌ خطأ في تحميل معلومات الشركة:', error);
        return null;
    }
}

/**
 * إضافة معلومات الجلسة للداشبورد
 */
function addSessionInfoToDashboard() {
    const dashboardContainer = document.getElementById('dashboard');
    
    // إنشاء قسم معلومات الجلسة
    const sessionInfoSection = `
        <div class="session-info-section">
            <div class="session-card">
                <div class="session-header">
                    <i class="fas fa-clock"></i>
                    <span>معلومات الجلسة</span>
                </div>
                <div class="session-details">
                    <div class="session-item">
                        <span class="session-label">وقت الدخول:</span>
                        <span class="session-value" id="login-time">--:--:--</span>
                    </div>
                    <div class="session-item">
                        <span class="session-label">مدة الجلسة:</span>
                        <span class="session-value" id="session-duration">--:--:--</span>
                    </div>
                    <div class="session-item">
                        <span class="session-label">آخر نشاط:</span>
                        <span class="session-value" id="last-activity">--:--:--</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إضافة قسم معلومات الجلسة في نهاية الداشبورد
    const lastChart = dashboardContainer.querySelector('.chart-container:last-child');
    if (lastChart) {
        lastChart.insertAdjacentHTML('afterend', sessionInfoSection);
    }
    
    // بدء تحديث معلومات الجلسة
    startSessionInfoUpdates();
}

/**
 * بدء تحديث معلومات الجلسة
 */
function startSessionInfoUpdates() {
    const loginTimeElement = document.getElementById('login-time');
    const sessionDurationElement = document.getElementById('session-duration');
    const lastActivityElement = document.getElementById('last-activity');
    
    if (loginTimeElement && sessionDurationElement && lastActivityElement) {
        // تحديث وقت الدخول
        const loginTime = getLastLoginTime() || new Date();
        loginTimeElement.textContent = formatTime(loginTime);
        
        // تحديث مدة الجلسة والوقت الحالي كل ثانية
        function updateSessionInfo() {
            const now = new Date();
            const sessionDuration = Math.floor((now - loginTime) / 1000); // بالثواني
            
            // تنسيق مدة الجلسة
            const hours = Math.floor(sessionDuration / 3600);
            const minutes = Math.floor((sessionDuration % 3600) / 60);
            const seconds = sessionDuration % 60;
            
            sessionDurationElement.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // تحديث آخر نشاط
            lastActivityElement.textContent = formatTime(now);
        }
        
        // تحديث فوري
        updateSessionInfo();
        
        // تحديث كل ثانية
        setInterval(updateSessionInfo, 1000);
    }
}

/**
 * تهيئة الداشبورد المحسنة
 */
async function initializeDashboardEnhanced() {
    console.log('🚀 بدء تهيئة الداشبورد المحسنة...');
    
    try {
        // إنشاء محتوى الداشبورد
        createDashboardContent();
        
        // تحميل معلومات الشركة
        await loadCompanyInfoForDashboard();
        
        // تحديث الإحصائيات
        await updateDashboardStats();
        
        // تهيئة الرسوم البيانية
        initializeCharts();
        
        // إضافة معلومات الجلسة
        addSessionInfoToDashboard();
        
        // بدء التحديثات التلقائية
        startSessionInfoUpdates();
        
        console.log('✅ تم تهيئة الداشبورد المحسنة بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة الداشبورد المحسنة:', error);
    }
}

// تصدير الدوال الجديدة
window.initializeDashboardEnhanced = initializeDashboardEnhanced;
window.addCompanyInfoToDashboard = addCompanyInfoToDashboard;
window.addSessionInfoToDashboard = addSessionInfoToDashboard; 