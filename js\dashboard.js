/**
 * Dashboard Module - موديول لوحة التحكم
 * ========================================
 */

// متغيرات الرسوم البيانية
let performanceChart, assetsChart, cashFlowChart;

/**
 * تهيئة الداشبورد
 */
function initializeDashboard() {
    console.log('📊 تهيئة لوحة التحكم...');
    
    // استخدام التهيئة المحسنة دائماً
    if (typeof initializeDashboardEnhanced === 'function') {
        initializeDashboardEnhanced();
    } else {
        // إنشاء محتوى الداشبورد
        createDashboardContent();
        
        // تحديث الإحصائيات
        updateDashboardStats();
        
        // تهيئة الرسوم البيانية
        initializeCharts();
    }
    
    console.log('✅ تم تهيئة لوحة التحكم بنجاح');
}

/**
 * إنشاء محتوى الداشبورد
 */
function createDashboardContent() {
    const dashboardContainer = document.getElementById('dashboard');
    
    dashboardContainer.innerHTML = `
        <div class="dashboard-grid">
            <!-- إجمالي الأصول -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الأصول</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-assets">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- إجمالي الإيرادات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الإيرادات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-revenue">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- إجمالي المصروفات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي المصروفات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                        <i class="fas fa-receipt"></i>
                    </div>
                </div>
                <div class="stat-value" id="total-expenses">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- صافي الدخل -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">صافي الدخل</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value" id="net-income">0.00</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد بيانات سابقة
                </div>
            </div>

            <!-- عدد القيود اليومية -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">القيود اليومية</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <i class="fas fa-book"></i>
                    </div>
                </div>
                <div class="stat-value" id="journal-count">0</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد قيود جديدة
                </div>
            </div>

            <!-- عدد الحسابات -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">عدد الحسابات</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                        <i class="fas fa-address-book"></i>
                    </div>
                </div>
                <div class="stat-value" id="accounts-count">0</div>
                <div class="stat-change">
                    <i class="fas fa-info-circle"></i> لا توجد حسابات جديدة
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">تحليل الأداء المالي</h3>
                <div class="chart-actions">
                    <button class="chart-btn active" onclick="updateChart('monthly')">شهري</button>
                    <button class="chart-btn" onclick="updateChart('quarterly')">ربع سنوي</button>
                    <button class="chart-btn" onclick="updateChart('yearly')">سنوي</button>
                </div>
            </div>
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">توزيع الأصول</h3>
            </div>
            <canvas id="assetsChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">تحليل التدفق النقدي</h3>
            </div>
            <canvas id="cashFlowChart" width="400" height="200"></canvas>
        </div>
    `;
}

/**
 * تحديث الداشبورد
 */
function updateDashboard() {
    console.log('🔄 بدء تحديث الداشبورد...');
    
    try {
        // تحديث الإحصائيات
        updateDashboardStats();
        
        // تهيئة الرسوم البيانية
        initializeCharts();
        
        // إضافة معلومات الشركة
        addCompanyInfoToDashboard();
        
        // إضافة معلومات الجلسة
        addSessionInfoToDashboard();
        
        console.log('✅ تم تحديث الداشبورد بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تحديث الداشبورد:', error);
    }
}

/**
 * تحديث إحصائيات الداشبورد
 */
function updateDashboardStats() {
    // التحقق من وجود متغيرات الحسابات
    if (typeof accounts === 'undefined' || !Array.isArray(accounts) || 
        typeof journalEntries === 'undefined' || !Array.isArray(journalEntries)) {
        console.warn('⚠️ بيانات الحسابات غير متوفرة أو غير صحيحة');
        return;
    }

    const assets = accounts.filter(a => a && a.type === 'asset');
    const revenues = accounts.filter(a => a && a.type === 'revenue');
    const expenses = accounts.filter(a => a && a.type === 'expense');

    const totalAssets = assets.reduce((sum, a) => sum + (a.balance || 0), 0);
    const totalRevenue = revenues.reduce((sum, a) => sum + (a.balance || 0), 0);
    const totalExpenses = expenses.reduce((sum, a) => sum + (a.balance || 0), 0);
    const netIncome = totalRevenue - totalExpenses;

    // تحديث العناصر في الواجهة
    const elements = {
        'total-assets': totalAssets,
        'total-revenue': totalRevenue,
        'total-expenses': totalExpenses,
        'net-income': netIncome,
        'journal-count': journalEntries.length,
        'accounts-count': accounts.length
    };

    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (typeof elements[id] === 'number') {
                element.textContent = GlassERP.formatNumber(elements[id]);
            } else {
                element.textContent = elements[id];
            }
        }
    });

    console.log('📈 تم تحديث إحصائيات الداشبورد');
}

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts() {
    console.log('📊 بدء تهيئة الرسوم البيانية...');
    
    // تهيئة جميع الرسوم البيانية فوراً
    try {
        // رسم بياني الأداء المالي
        if (!performanceChart) {
            createPerformanceChart();
            console.log('✅ تم إنشاء رسم بياني الأداء المالي');
        }
        
        // رسم بياني توزيع الأصول
        if (!assetsChart) {
            createAssetsChart();
            console.log('✅ تم إنشاء رسم بياني توزيع الأصول');
        }
        
        // رسم بياني التدفق النقدي
        if (!cashFlowChart) {
            createCashFlowChart();
            console.log('✅ تم إنشاء رسم بياني التدفق النقدي');
        }
        
        console.log('🎉 تم تهيئة جميع الرسوم البيانية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة الرسوم البيانية:', error);
    }
}

/**
 * رسم بياني للأداء المالي
 */
function createPerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    if (!ctx) return;
    
    if (performanceChart) {
        performanceChart.destroy();
    }

    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
            const revenueData = [0, 0, 0, 0, 0, 0];
        const expenseData = [0, 0, 0, 0, 0, 0];

    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'الإيرادات',
                    data: revenueData,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'المصروفات',
                    data: expenseData,
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

/**
 * رسم بياني لتوزيع الأصول
 */
function createAssetsChart() {
    const ctx = document.getElementById('assetsChart');
    if (!ctx) return;
    
    if (assetsChart) {
        assetsChart.destroy();
    }

    // استخدام بيانات الحسابات الفعلية إذا كانت متوفرة
    let labels = ['لا توجد بيانات'];
    let data = [1];
    let colors = ['#e5e7eb'];

    if (typeof accounts !== 'undefined' && Array.isArray(accounts)) {
        const assetAccounts = accounts.filter(a => a && a.type === 'asset' && (a.balance || 0) > 0);
        if (assetAccounts.length > 0) {
            labels = assetAccounts.map(a => a.name || 'حساب غير محدد');
            data = assetAccounts.map(a => a.balance || 0);
            colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];
        }
    }

    assetsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, data.length),
                borderWidth: 0,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 11
                        },
                        padding: 15
                    }
                }
            }
        }
    });
}

/**
 * رسم بياني للتدفق النقدي
 */
function createCashFlowChart() {
    const ctx = document.getElementById('cashFlowChart');
    if (!ctx) return;
    
    if (cashFlowChart) {
        cashFlowChart.destroy();
    }

    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
    const cashIn = [0, 0, 0, 0, 0, 0];
    const cashOut = [0, 0, 0, 0, 0, 0];

    cashFlowChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'التدفق الداخل',
                    data: cashIn,
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: '#10b981',
                    borderWidth: 1
                },
                {
                    label: 'التدفق الخارج',
                    data: cashOut,
                    backgroundColor: 'rgba(239, 68, 68, 0.8)',
                    borderColor: '#ef4444',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

/**
 * تحديث الرسم البياني حسب الفترة
 */
function updateChart(period) {
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
    
    console.log(`📊 تحديث الرسم البياني للفترة: ${period}`);
    
    // هنا يمكن إضافة منطق لتحديث البيانات حسب الفترة المحددة
    // يمكن جلب بيانات مختلفة من localStorage أو حسابها
}

// تهيئة الداشبورد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قليل للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        if (document.getElementById('dashboard').classList.contains('active')) {
            initializeDashboard();
        }
    }, 100);
});

/**
 * التحسينات الجديدة للداشبورد
 * ========================================
 */



/**
 * إضافة معلومات الشركة والشعار للداشبورد
 */
function addCompanyInfoToDashboard() {
    const dashboardContainer = document.getElementById('dashboard');
    
    // إنشاء قسم معلومات الشركة
    const companyInfoSection = `
        <div class="company-info-section">
            <div class="company-card">
                <div class="company-logo">
                    <img id="company-logo" src="assets/icons/company-logo.png" alt="شعار الشركة" onerror="this.style.display='none'">
                    <div class="logo-placeholder" id="logo-placeholder">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="company-details">
                    <h2 id="dashboard-company-name"></h2>
                    <p id="dashboard-company-address">عنوان الشركة</p>
                    <p id="dashboard-company-contact">هاتف: -- | بريد: --</p>
                </div>
            </div>
        </div>
    `;
    
    // إضافة قسم معلومات الشركة في بداية الداشبورد
    const dashboardGrid = dashboardContainer.querySelector('.dashboard-grid');
    if (dashboardGrid) {
        dashboardGrid.insertAdjacentHTML('beforebegin', companyInfoSection);
    }
    
    // تحميل معلومات الشركة
    loadCompanyInfoForDashboard();
}

/**
 * تحديث معلومات الشركة في الداشبورد
 */
function updateDashboardCompanyInfo(settings) {
    if (!settings) {
        // لا توجد بيانات محفوظة
        document.getElementById('dashboard-company-name').textContent = '';
        document.getElementById('dashboard-company-address').textContent = '';
        document.getElementById('dashboard-company-contact').textContent = '';
        document.getElementById('company-logo').style.display = 'none';
        document.getElementById('logo-placeholder').style.display = 'flex';
        return;
    }

    // تحديث اسم الشركة
    const companyNameElement = document.getElementById('dashboard-company-name');
    if (companyNameElement) {
        companyNameElement.textContent = settings.name ? settings.name : '';
    }

    // تحديث عنوان الشركة
    const companyAddressElement = document.getElementById('dashboard-company-address');
    if (companyAddressElement) {
        companyAddressElement.textContent = settings.address ? settings.address : '';
    }

    // تحديث معلومات الاتصال
    const companyContactElement = document.getElementById('dashboard-company-contact');
    if (companyContactElement) {
        const phone = settings.phone || '';
        const email = settings.email || '';
        const contactText = phone && email ? `هاتف: ${phone} | بريد: ${email}` :
                           phone ? `هاتف: ${phone}` :
                           email ? `بريد: ${email}` : '';
        companyContactElement.textContent = contactText;
    }

    // تحديث الشعار
    const companyLogoElement = document.getElementById('company-logo');
    const logoPlaceholderElement = document.getElementById('logo-placeholder');
    if (companyLogoElement && logoPlaceholderElement) {
        if (settings.logo) {
            companyLogoElement.src = settings.logo;
            companyLogoElement.style.display = 'block';
            logoPlaceholderElement.style.display = 'none';
        } else {
            companyLogoElement.style.display = 'none';
            logoPlaceholderElement.style.display = 'flex';
        }
    }
}

/**
 * تحميل معلومات الشركة للداشبورد
 */
async function loadCompanyInfoForDashboard() {
    try {
        // التحقق من وجود DataStore
        if (typeof DataStore === 'undefined') {
            console.warn('⚠️ DataStore غير متاح، سيتم استخدام localStorage');
            // استخدام localStorage كبديل
            const savedSettings = localStorage.getItem('glassERP_settings');
            const settings = savedSettings ? JSON.parse(savedSettings) : null;
            updateDashboardCompanyInfo(settings);
            return;
        }

        const store = new DataStore();
        const settings = await store.getCompanySettings();
        updateDashboardCompanyInfo(settings);
    } catch (error) {
        console.error('❌ خطأ في تحميل معلومات الشركة:', error);
    }
}

/**
 * إضافة معلومات الجلسة للداشبورد
 */
function addSessionInfoToDashboard() {
    const dashboardContainer = document.getElementById('dashboard');
    
    // إنشاء قسم معلومات الجلسة
    const sessionInfoSection = `
        <div class="session-info-section">
            <div class="session-card">
                <div class="session-header">
                    <i class="fas fa-clock"></i>
                    <span>معلومات الجلسة</span>
                </div>
                <div class="session-details">
                    <div class="session-item">
                        <span class="session-label">وقت الدخول:</span>
                        <span class="session-value" id="login-time">--:--:--</span>
                    </div>
                    <div class="session-item">
                        <span class="session-label">مدة الجلسة:</span>
                        <span class="session-value" id="session-duration">--:--:--</span>
                    </div>
                    <div class="session-item">
                        <span class="session-label">آخر نشاط:</span>
                        <span class="session-value" id="last-activity">--:--:--</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إضافة قسم معلومات الجلسة في نهاية الداشبورد
    const lastChart = dashboardContainer.querySelector('.chart-container:last-child');
    if (lastChart) {
        lastChart.insertAdjacentHTML('afterend', sessionInfoSection);
    }
    
    // بدء تحديث معلومات الجلسة
    startSessionInfoUpdates();
}

/**
 * بدء تحديث معلومات الجلسة
 */
function startSessionInfoUpdates() {
    const loginTimeElement = document.getElementById('login-time');
    const sessionDurationElement = document.getElementById('session-duration');
    const lastActivityElement = document.getElementById('last-activity');
    
    if (loginTimeElement && sessionDurationElement && lastActivityElement) {
        // تحديث وقت الدخول
        const loginTime = getLastLoginTime() || new Date();
        loginTimeElement.textContent = formatTime(loginTime);
        
        // تحديث مدة الجلسة والوقت الحالي كل ثانية
        function updateSessionInfo() {
            const now = new Date();
            const sessionDuration = Math.floor((now - loginTime) / 1000); // بالثواني
            
            // تنسيق مدة الجلسة
            const hours = Math.floor(sessionDuration / 3600);
            const minutes = Math.floor((sessionDuration % 3600) / 60);
            const seconds = sessionDuration % 60;
            
            sessionDurationElement.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // تحديث آخر نشاط
            lastActivityElement.textContent = formatTime(now);
        }
        
        // تحديث فوري
        updateSessionInfo();
        
        // تحديث كل ثانية
        setInterval(updateSessionInfo, 1000);
    }
}

/**
 * تحديث دالة تهيئة الداشبورد لتشمل التحسينات الجديدة
 */
function initializeDashboardEnhanced() {
    console.log('📊 تهيئة لوحة التحكم المحسنة...');
    
    // إنشاء محتوى الداشبورد الأساسي
    createDashboardContent();
    
    // إضافة معلومات الشركة
    addCompanyInfoToDashboard();
    
    // إضافة معلومات الجلسة
    addSessionInfoToDashboard();
    
    // تحديث الإحصائيات
    updateDashboardStats();
    
    // تهيئة الرسوم البيانية
    initializeCharts();
    
    // تحميل معلومات الشركة المحفوظة
    loadCompanyInfoForDashboard();
    
    console.log('✅ تم تهيئة لوحة التحكم المحسنة بنجاح');
}

// تصدير الدوال الجديدة
window.initializeDashboardEnhanced = initializeDashboardEnhanced;
window.addCompanyInfoToDashboard = addCompanyInfoToDashboard;
window.addSessionInfoToDashboard = addSessionInfoToDashboard; 