/* ========================================
   Settings Styles - تنسيقات الإعدادات المحسنة
   ======================================== */

/* ===== صفحة الإعدادات المحسنة ===== */

.settings-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.08),
        0 8px 25px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.settings-container h2 {
    color: #1e293b;
    font-size: 2.2rem;
    font-weight: 800;
    margin-bottom: 2.5rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding-bottom: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.settings-container h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #8b5cf6);
    border-radius: 2px;
}

.settings-container h2 i {
    color: #667eea;
    font-size: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== أقسام الإعدادات ===== */
.settings-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.04),
        0 2px 10px rgba(0, 0, 0, 0.02);
}

.settings-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea, #8b5cf6, #ec4899);
    border-radius: 20px 20px 0 0;
}

.settings-section:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 6px 20px rgba(0, 0, 0, 0.08);
    border-color: rgba(102, 126, 234, 0.3);
}

.settings-section h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    position: relative;
}

.settings-section h3::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #8b5cf6);
    border-radius: 1px;
}

.settings-section h3 i {
    color: #667eea;
    font-size: 1.3rem;
    width: 28px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== مجموعات الحقول ===== */
.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-group label {
    display: block;
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
}

.form-group label[for*="name"]::after {
    content: ' *';
    color: #ef4444;
    font-weight: 400;
}

.form-group label[for*="address"],
.form-group label[for*="phone"],
.form-group label[for*="email"],
.form-group label[for*="website"],
.form-group label[for*="tax"] {
    color: #6b7280;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1.25rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    color: #1f2937;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 2px rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
    background: #ffffff;
}

.form-group input:hover,
.form-group select:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04);
}

/* ===== تخطيط الشبكة للحقول ===== */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.form-grid .form-group {
    margin-bottom: 0;
}

/* ===== رفع الشعار ===== */
.logo-upload {
    text-align: center;
    padding: 3rem 2rem;
    border: 3px dashed #d1d5db;
    border-radius: 20px;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.logo-upload::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.6s ease;
}

.logo-upload:hover::before {
    left: 100%;
}

.logo-upload:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateY(-2px);
    box-shadow: 
        0 8px 25px rgba(102, 126, 234, 0.15),
        0 4px 12px rgba(102, 126, 234, 0.1);
}

.logo-preview {
    width: 140px;
    height: 140px;
    margin: 0 auto 1.5rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 2px 10px rgba(0, 0, 0, 0.04);
}

.logo-preview:hover {
    border-color: #667eea;
    transform: scale(1.05) translateY(-4px);
    box-shadow: 
        0 12px 40px rgba(102, 126, 234, 0.2),
        0 6px 20px rgba(102, 126, 234, 0.15);
}

.logo-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.logo-preview span {
    color: #6b7280;
    font-size: 0.95rem;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.logo-preview span::before {
    content: '\f03e';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 2rem;
    color: #cbd5e1;
    transition: all 0.3s ease;
}

.logo-preview:hover span::before {
    color: #667eea;
    transform: scale(1.1);
}

.logo-info {
    margin-top: 1.5rem;
}

.logo-info p {
    color: #6b7280;
    font-size: 0.9rem;
    margin: 0.5rem 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.7);
}

.logo-info p:first-child {
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.logo-info p:last-child {
    color: #d97706;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.logo-info p i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

/* ===== أزرار الإجراءات ===== */
.settings-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid rgba(226, 232, 240, 0.8);
}

.btn-primary,
.btn-secondary {
    padding: 1rem 2.5rem;
    border: none;
    border-radius: 16px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
}

.btn-primary::before,
.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 
        0 4px 20px rgba(102, 126, 234, 0.3),
        0 2px 10px rgba(102, 126, 234, 0.2);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 8px 30px rgba(102, 126, 234, 0.4),
        0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    border: 2px solid #e2e8f0;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 2px 10px rgba(0, 0, 0, 0.04);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #1e293b;
    transform: translateY(-3px);
    box-shadow: 
        0 8px 30px rgba(0, 0, 0, 0.12),
        0 4px 15px rgba(0, 0, 0, 0.08);
}

/* ===== رسائل النجاح والخطأ ===== */
.success-message,
.error-message {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin: 1rem 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideInRight 0.4s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.success-message {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border: 1px solid #86efac;
}

.error-message {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== تحسينات إضافية ===== */
.settings-section:nth-child(1)::before {
    background: linear-gradient(90deg, #667eea, #8b5cf6);
}

.settings-section:nth-child(2)::before {
    background: linear-gradient(90deg, #8b5cf6, #ec4899);
}

.settings-section:nth-child(3)::before {
    background: linear-gradient(90deg, #ec4899, #f59e0b);
}

.form-group input[required],
.form-group select[required] {
    border-color: #fbbf24;
}

.form-group input[required]:focus,
.form-group select[required]:focus {
    border-color: #f59e0b;
    box-shadow: 
        0 0 0 4px rgba(251, 191, 36, 0.1),
        0 4px 12px rgba(251, 191, 36, 0.15);
}

#currency {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

#currency option {
    padding: 0.75rem;
    font-weight: 500;
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .settings-container {
        margin: 1rem;
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .settings-container h2 {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .settings-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .settings-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        padding: 1.25rem 2rem;
    }
    
    .logo-preview {
        width: 120px;
        height: 120px;
    }
}

/* ===== تحسينات للأجهزة اللوحية ===== */
@media (min-width: 769px) and (max-width: 1024px) {
    .settings-container {
        max-width: 90%;
        padding: 2rem;
    }
    
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== تحسينات للشاشات الكبيرة ===== */
@media (min-width: 1025px) {
    .settings-container {
        max-width: 1200px;
    }
    
    .form-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* إضافة CSS للنص التوضيحي */
.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    display: block;
}

.form-group input:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input:valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* تحسين مظهر الحقول المطلوبة */
.form-group label[for*="company-name"]::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-group input#company-name-input:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input#company-name-input:valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
} 