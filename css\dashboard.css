/* ========================================
   Dashboard Styles - تنسيقات الداشبورد
   ======================================== */

/* Dashboard Grid - شبكة الداشبورد */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* Stat Cards - بطاقات الإحصائيات */
.stat-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stat-title {
  font-size: 0.9em;
  color: #64748b;
  font-weight: 500;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5em;
  color: white;
}

.stat-value {
  font-size: 2.2em;
  font-weight: 800;
  color: #1a1a2e;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 0.9em;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

/* Chart Container - حاوية الرسوم البيانية */
.chart-container {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  margin-bottom: 25px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.3em;
  font-weight: 700;
  color: #1a1a2e;
}

.chart-actions {
  display: flex;
  gap: 10px;
}

.chart-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 8px;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
}

.chart-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.chart-btn:not(.active) {
  background: #e2e8f0;
  color: #64748b;
}

.chart-btn:hover {
  transform: translateY(-2px);
}

/* Quick Actions - الإجراءات السريعة */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.quick-action-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.quick-action-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 1.5em;
  color: white;
}

.quick-action-title {
  font-weight: 600;
  color: #1a1a2e;
  margin-bottom: 5px;
}

.quick-action-desc {
  font-size: 0.9em;
  color: #64748b;
}

/* Chart Canvas - منطقة الرسم البياني */
canvas {
  max-height: 400px;
  width: 100% !important;
  height: auto !important;
}

/* Responsive Design for Dashboard */
@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .chart-actions {
    width: 100%;
    justify-content: center;
  }
}

/* ========================================
   التحسينات الجديدة للداشبورد - New Dashboard Enhancements
   ======================================== */

/* قسم معلومات الشركة */
.company-info-section {
  margin-bottom: 30px;
}

.company-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 25px;
  transition: all 0.3s ease;
}

.company-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.company-logo {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5em;
}

.company-details {
  flex: 1;
}

.company-details h2 {
  font-size: 1.8em;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 10px;
}

.company-details p {
  font-size: 1em;
  color: #64748b;
  margin-bottom: 5px;
  line-height: 1.5;
}

/* قسم معلومات الجلسة */
.session-info-section {
  margin-top: 30px;
}

.session-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.session-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e2e8f0;
}

.session-header i {
  font-size: 1.3em;
  color: #667eea;
}

.session-header span {
  font-size: 1.2em;
  font-weight: 600;
  color: #1a1a2e;
}

.session-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.session-item:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: translateX(5px);
}

.session-label {
  font-size: 0.95em;
  font-weight: 500;
  color: #64748b;
}

.session-value {
  font-size: 1em;
  font-weight: 600;
  color: #1a1a2e;
  font-family: 'Courier New', monospace;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .company-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .company-logo {
    width: 80px;
    height: 80px;
  }
  
  .company-details h2 {
    font-size: 1.5em;
  }
  
  .session-details {
    grid-template-columns: 1fr;
  }
  
  .session-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .company-card {
    padding: 20px;
  }
  
  .session-card {
    padding: 20px;
  }
  
  .session-header {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
} 