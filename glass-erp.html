<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Glass ERP System</title>
  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      overflow-x: hidden;
    }

    /* Sidebar Styles */
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
      color: #fff;
      height: 100vh;
      position: fixed;
      right: 0;
      top: 0;
      padding: 0;
      box-shadow: -5px 0 15px rgba(0,0,0,0.3);
      z-index: 1000;
      transition: all 0.3s ease;
    }

    .sidebar-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .sidebar-header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
      animation: shine 3s infinite;
    }

    @keyframes shine {
      0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
      100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .sidebar-header h2 {
      font-size: 1.8em;
      font-weight: 800;
      margin: 0;
      position: relative;
      z-index: 1;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .sidebar-header .subtitle {
      font-size: 0.9em;
      opacity: 0.9;
      margin-top: 5px;
      position: relative;
      z-index: 1;
    }

    .nav-menu {
      padding: 20px 0;
      height: calc(100vh - 120px);
      overflow-y: auto;
    }

    .nav-menu::-webkit-scrollbar {
      width: 5px;
    }

    .nav-menu::-webkit-scrollbar-track {
      background: rgba(255,255,255,0.1);
    }

    .nav-menu::-webkit-scrollbar-thumb {
      background: rgba(255,255,255,0.3);
      border-radius: 5px;
    }

    .nav-item {
      padding: 0;
      margin: 5px 15px;
      border-radius: 12px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .nav-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: left 0.5s ease;
    }

    .nav-item:hover::before {
      left: 100%;
    }

    .nav-link {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      color: #fff;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .nav-link i {
      width: 25px;
      margin-left: 15px;
      font-size: 1.1em;
      transition: all 0.3s ease;
    }

    .nav-link span {
      font-weight: 500;
      font-size: 0.95em;
    }

    .nav-item:hover .nav-link {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      transform: translateX(-5px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .nav-item.active .nav-link {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .nav-item:hover i {
      transform: scale(1.2);
    }

    /* Main Content */
    .main {
      margin-right: 280px;
      padding: 0;
      min-height: 100vh;
      background: #f8fafc;
      transition: all 0.3s ease;
    }

    .top-header {
      background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
      padding: 20px 30px;
      box-shadow: 0 2px 20px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .page-title {
      font-size: 1.8em;
      font-weight: 700;
      color: #1a1a2e;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .notification-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
      padding: 12px;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .notification-btn:hover {
      transform: scale(1.1);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .notification-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #ff4757;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 0.7em;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 25px;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .user-profile:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .user-avatar {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      background: rgba(255,255,255,0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    /* Content Area */
    .content {
      padding: 30px;
    }

    .module {
      display: none;
      animation: fadeIn 0.5s ease;
    }

    .module.active {
      display: block;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Dashboard Styles */
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 25px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
      border-radius: 20px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .stat-title {
      font-size: 0.9em;
      color: #64748b;
      font-weight: 500;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5em;
      color: white;
    }

    .stat-value {
      font-size: 2.2em;
      font-weight: 800;
      color: #1a1a2e;
      margin-bottom: 5px;
    }

    .stat-change {
      font-size: 0.9em;
      font-weight: 500;
    }

    .stat-change.positive {
      color: #10b981;
    }

    .stat-change.negative {
      color: #ef4444;
    }

    /* Chart Container */
    .chart-container {
      background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
      border-radius: 20px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-bottom: 25px;
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .chart-title {
      font-size: 1.3em;
      font-weight: 700;
      color: #1a1a2e;
    }

    .chart-actions {
      display: flex;
      gap: 10px;
    }

    .chart-btn {
      padding: 8px 15px;
      border: none;
      border-radius: 8px;
      font-size: 0.9em;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .chart-btn.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .chart-btn:not(.active) {
      background: #e2e8f0;
      color: #64748b;
    }

    .chart-btn:hover {
      transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .sidebar {
        transform: translateX(100%);
      }
      
      .sidebar.open {
        transform: translateX(0);
      }
      
      .main {
        margin-right: 0;
      }
      
      .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }
    }

    @media (max-width: 768px) {
      .dashboard-grid {
        grid-template-columns: 1fr;
      }
      
      .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
      }
      
      .content {
        padding: 20px;
      }
    }
    
    /* تنسيقات صفحة الإعدادات */
    .settings-container { max-width: 800px; }
    .settings-section {
      background: #fff; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px #0001;
    }
    .settings-section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    .form-group { margin-bottom: 20px; }
    .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
    .form-group input, .form-group select {
      width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;
    }
    .form-group input:focus, .form-group select:focus {
      outline: none; border-color: #007bff; box-shadow: 0 0 0 2px #007bff20;
    }
    .logo-upload { text-align: center; }
    .logo-preview {
      width: 150px; height: 150px; border: 2px dashed #ddd; border-radius: 8px;
      display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;
      cursor: pointer; transition: border-color 0.2s;
    }
    .logo-preview:hover { border-color: #007bff; }
    .logo-preview img { max-width: 100%; max-height: 100%; border-radius: 4px; }
    .logo-preview span { color: #666; font-size: 14px; }
    .settings-actions { text-align: center; margin-top: 30px; }
    .btn-primary, .btn-secondary {
      padding: 12px 30px; border: none; border-radius: 4px; font-size: 14px; cursor: pointer;
      margin: 0 10px; transition: background 0.2s;
    }
    .btn-primary { background: #007bff; color: #fff; }
    .btn-primary:hover { background: #0056b3; }
    .btn-secondary { background: #6c757d; color: #fff; }
    .btn-secondary:hover { background: #545b62; }
    
    /* تنسيقات إضافية للداشبورد */
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .quick-action-card {
      background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .quick-action-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .quick-action-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 15px;
      font-size: 1.5em;
      color: white;
    }

    .quick-action-title {
      font-weight: 600;
      color: #1a1a2e;
      margin-bottom: 5px;
    }

    .quick-action-desc {
      font-size: 0.9em;
      color: #64748b;
    }

    /* تنسيقات موديول الحسابات */
    .accounts-container { max-width: 1200px; }
    .accounts-section {
      background: #fff; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px #0001;
    }
    .chart-actions, .journal-actions, .reports-actions {
      margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;
    }
    .accounts-tree, .journal-entries, .financial-reports {
      max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 15px;
    }
    
    /* Modal تنسيقات */
    .modal {
      display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%;
      background-color: rgba(0,0,0,0.5);
    }
    .modal-content {
      background-color: #fff; margin: 5% auto; padding: 25px; border-radius: 8px; width: 80%; max-width: 600px;
      position: relative; max-height: 80vh; overflow-y: auto;
    }
    .close {
      color: #aaa; float: left; font-size: 28px; font-weight: bold; cursor: pointer;
    }
    .close:hover { color: #000; }
    .modal-actions { text-align: center; margin-top: 20px; }
    
    /* تنسيقات القيود اليومية */
    .entry-line {
      display: grid; grid-template-columns: 2fr 2fr 1fr 1fr auto; gap: 10px; margin-bottom: 10px; align-items: center;
    }
    .entry-totals {
      display: flex; justify-content: space-between; margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;
      font-weight: bold;
    }
    .btn-danger {
      background: #dc3545; color: #fff; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;
    }
    .btn-danger:hover { background: #c82333; }
    
    /* تنسيقات الجداول */
    table {
      width: 100%; border-collapse: collapse; margin-top: 15px;
    }
    th, td {
      border: 1px solid #ddd; padding: 12px; text-align: right;
    }
    th { background-color: #f8f9fa; font-weight: bold; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    
    /* تنسيقات شجرة الحسابات */
    .account-item {
      padding: 8px; margin: 2px 0; border-left: 3px solid #007bff; background: #f8f9fa;
      cursor: pointer; transition: background 0.2s;
    }
    .account-item:hover { background: #e9ecef; }
    .account-item.sub-account { margin-right: 20px; border-left-color: #28a745; }
    .account-item.sub-sub-account { margin-right: 40px; border-left-color: #ffc107; }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="sidebar-header">
      <h2><i class="fas fa-cube"></i> Glass ERP</h2>
      <div class="subtitle">نظام إدارة الموارد المتكامل</div>
    </div>
    <nav class="nav-menu">
      <div class="nav-item active" data-module="dashboard">
        <a href="#" class="nav-link">
          <i class="fas fa-tachometer-alt"></i>
          <span>لوحة التحكم</span>
        </a>
      </div>
      <div class="nav-item" data-module="settings">
        <a href="#" class="nav-link">
          <i class="fas fa-cog"></i>
          <span>إعدادات الشركة</span>
        </a>
      </div>
      <div class="nav-item" data-module="sales">
        <a href="#" class="nav-link">
          <i class="fas fa-shopping-cart"></i>
          <span>المبيعات</span>
        </a>
      </div>
      <div class="nav-item" data-module="purchases">
        <a href="#" class="nav-link">
          <i class="fas fa-shopping-bag"></i>
          <span>المشتريات</span>
        </a>
      </div>
      <div class="nav-item" data-module="payroll">
        <a href="#" class="nav-link">
          <i class="fas fa-users"></i>
          <span>الرواتب والأجور</span>
        </a>
      </div>
      <div class="nav-item" data-module="inventory">
        <a href="#" class="nav-link">
          <i class="fas fa-warehouse"></i>
          <span>المخازن</span>
        </a>
      </div>
      <div class="nav-item" data-module="payments">
        <a href="#" class="nav-link">
          <i class="fas fa-credit-card"></i>
          <span>المدفوعات</span>
        </a>
      </div>
      <div class="nav-item" data-module="receipts">
        <a href="#" class="nav-link">
          <i class="fas fa-money-bill-wave"></i>
          <span>المقبوضات</span>
        </a>
      </div>
      <div class="nav-item" data-module="accounts">
        <a href="#" class="nav-link">
          <i class="fas fa-chart-line"></i>
          <span>الحسابات</span>
        </a>
      </div>
      <div class="nav-item" data-module="customers">
        <a href="#" class="nav-link">
          <i class="fas fa-user-friends"></i>
          <span>العملاء</span>
        </a>
      </div>
      <div class="nav-item" data-module="services">
        <a href="#" class="nav-link">
          <i class="fas fa-tools"></i>
          <span>الخدمات</span>
        </a>
      </div>
    </nav>
  </div>
  
  <div class="main">
    <div class="top-header">
      <div class="header-content">
        <h1 class="page-title" id="page-title">لوحة التحكم</h1>
        <div class="header-actions">
          <button class="notification-btn">
            <i class="fas fa-bell"></i>
            <span class="notification-badge">3</span>
          </button>
          <div class="user-profile">
            <div class="user-avatar">
              <i class="fas fa-user"></i>
            </div>
            <span>المدير</span>
          </div>
        </div>
      </div>
    </div>
    
        <div class="content">
      <!-- Dashboard Module -->
      <div id="dashboard" class="module active">
        <div class="dashboard-grid">
          <!-- إجمالي الأصول -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">إجمالي الأصول</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-chart-pie"></i>
              </div>
            </div>
            <div class="stat-value" id="total-assets">0.00</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i> +12.5% من الشهر الماضي
            </div>
          </div>

          <!-- إجمالي الإيرادات -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">إجمالي الإيرادات</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                <i class="fas fa-dollar-sign"></i>
              </div>
            </div>
            <div class="stat-value" id="total-revenue">0.00</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i> +8.3% من الشهر الماضي
            </div>
          </div>

          <!-- إجمالي المصروفات -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">إجمالي المصروفات</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                <i class="fas fa-receipt"></i>
              </div>
            </div>
            <div class="stat-value" id="total-expenses">0.00</div>
            <div class="stat-change negative">
              <i class="fas fa-arrow-down"></i> -5.2% من الشهر الماضي
            </div>
          </div>

          <!-- صافي الدخل -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">صافي الدخل</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="fas fa-chart-line"></i>
              </div>
            </div>
            <div class="stat-value" id="net-income">0.00</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i> +15.7% من الشهر الماضي
            </div>
          </div>

          <!-- عدد القيود اليومية -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">القيود اليومية</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                <i class="fas fa-book"></i>
              </div>
            </div>
            <div class="stat-value" id="journal-count">0</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i> +3 قيود جديدة اليوم
            </div>
          </div>

          <!-- عدد الحسابات -->
          <div class="stat-card">
            <div class="stat-header">
              <div class="stat-title">عدد الحسابات</div>
              <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="fas fa-address-book"></i>
              </div>
            </div>
            <div class="stat-value" id="accounts-count">20</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i> +2 حسابات جديدة
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">تحليل الأداء المالي</h3>
            <div class="chart-actions">
              <button class="chart-btn active" onclick="updateChart('monthly')">شهري</button>
              <button class="chart-btn" onclick="updateChart('quarterly')">ربع سنوي</button>
              <button class="chart-btn" onclick="updateChart('yearly')">سنوي</button>
            </div>
          </div>
          <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">توزيع الأصول</h3>
          </div>
          <canvas id="assetsChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">تحليل التدفق النقدي</h3>
          </div>
          <canvas id="cashFlowChart" width="400" height="200"></canvas>
        </div>
      </div>

      <div id="settings" class="module">
      <div class="settings-container">
        <div class="settings-section">
          <h3>بيانات الشركة</h3>
          <div class="form-group">
            <label>اسم الشركة:</label>
            <input type="text" id="company-name-input" placeholder="أدخل اسم الشركة">
          </div>
          <div class="form-group">
            <label>العنوان:</label>
            <input type="text" id="company-address" placeholder="عنوان الشركة">
          </div>
          <div class="form-group">
            <label>رقم الهاتف:</label>
            <input type="tel" id="company-phone" placeholder="رقم الهاتف">
          </div>
          <div class="form-group">
            <label>البريد الإلكتروني:</label>
            <input type="email" id="company-email" placeholder="البريد الإلكتروني">
          </div>
        </div>
        
        <div class="settings-section">
          <h3>إعدادات النظام</h3>
          <div class="form-group">
            <label>العملة:</label>
            <select id="currency">
              <option value="LYD">دينار ليبي</option>
              <option value="SAR">ريال سعودي</option>
              <option value="EGP">جنيه مصري</option>
            </select>
          </div>
          <div class="form-group">
            <label>اللغة:</label>
            <select id="language">
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>
        
        <div class="settings-section">
          <h3>شعار الشركة</h3>
          <div class="logo-upload">
            <input type="file" id="logo-input" accept="image/*" style="display: none;">
            <div class="logo-preview" id="logo-preview">
              <span>اضغط لإضافة شعار</span>
            </div>
            <button type="button" onclick="document.getElementById('logo-input').click()">اختيار الشعار</button>
          </div>
        </div>
        
        <div class="settings-actions">
          <button type="button" onclick="saveSettings()" class="btn-primary">حفظ الإعدادات</button>
          <button type="button" onclick="resetSettings()" class="btn-secondary">إعادة تعيين</button>
        </div>
      </div>
    </div>
    <div id="sales" class="module">
      <h3>موديول المبيعات</h3>
      <p>إدارة عمليات البيع والفواتير.</p>
    </div>
    <div id="purchases" class="module">
      <h3>موديول المشتريات</h3>
      <p>إدارة عمليات الشراء والموردين.</p>
    </div>
    <div id="payroll" class="module">
      <h3>موديول الرواتب والأجور</h3>
      <p>إدارة الرواتب والموظفين.</p>
    </div>
    <div id="inventory" class="module">
      <h3>موديول المخازن</h3>
      <p>إدارة المخزون وحركة الأصناف.</p>
    </div>
    <div id="payments" class="module">
      <h3>موديول المدفوعات</h3>
      <p>تسجيل المدفوعات المختلفة.</p>
    </div>
    <div id="receipts" class="module">
      <h3>موديول المقبوضات</h3>
      <p>تسجيل المقبوضات المختلفة.</p>
    </div>
    <div id="accounts" class="module">
      <div class="accounts-container">
        <!-- شجرة الحسابات -->
        <div class="accounts-section">
          <h3>شجرة الحسابات</h3>
          <div class="chart-actions">
            <button onclick="addAccount()" class="btn-primary">إضافة حساب جديد</button>
            <button onclick="showAccountTree()" class="btn-secondary">عرض الشجرة</button>
          </div>
          <div id="accounts-tree" class="accounts-tree"></div>
        </div>

        <!-- القيود اليومية -->
        <div class="accounts-section">
          <h3>القيود اليومية</h3>
          <div class="journal-actions">
            <button onclick="addJournalEntry()" class="btn-primary">قيد جديد</button>
            <button onclick="showJournalEntries()" class="btn-secondary">عرض القيود</button>
          </div>
          <div id="journal-entries" class="journal-entries"></div>
        </div>

        <!-- القوائم المالية -->
        <div class="accounts-section">
          <h3>القوائم المالية</h3>
          <div class="reports-actions">
            <button onclick="generateBalanceSheet()" class="btn-primary">الميزانية العمومية</button>
            <button onclick="generateIncomeStatement()" class="btn-secondary">قائمة الدخل</button>
            <button onclick="generateTrialBalance()" class="btn-secondary">ميزان المراجعة</button>
          </div>
          <div id="financial-reports" class="financial-reports"></div>
        </div>
      </div>

      <!-- Modal إضافة حساب -->
      <div id="account-modal" class="modal">
        <div class="modal-content">
          <span class="close">&times;</span>
          <h3>إضافة حساب جديد</h3>
          <div class="form-group">
            <label>رقم الحساب:</label>
            <input type="text" id="account-number" placeholder="مثال: 1001">
          </div>
          <div class="form-group">
            <label>اسم الحساب:</label>
            <input type="text" id="account-name" placeholder="اسم الحساب">
          </div>
          <div class="form-group">
            <label>نوع الحساب:</label>
            <select id="account-type">
              <option value="asset">أصول</option>
              <option value="liability">خصوم</option>
              <option value="equity">حقوق ملكية</option>
              <option value="revenue">إيرادات</option>
              <option value="expense">مصروفات</option>
            </select>
          </div>
          <div class="form-group">
            <label>الحساب الأب:</label>
            <select id="parent-account">
              <option value="">بدون حساب أب</option>
            </select>
          </div>
          <div class="form-group">
            <label>الرصيد الافتتاحي:</label>
            <input type="number" id="opening-balance" placeholder="0.00" step="0.01">
          </div>
          <div class="modal-actions">
            <button onclick="saveAccount()" class="btn-primary">حفظ</button>
            <button onclick="closeAccountModal()" class="btn-secondary">إلغاء</button>
          </div>
        </div>
      </div>

      <!-- Modal قيد يومية -->
      <div id="journal-modal" class="modal">
        <div class="modal-content">
          <span class="close">&times;</span>
          <h3>قيد يومية جديد</h3>
          <div class="form-group">
            <label>التاريخ:</label>
            <input type="date" id="entry-date">
          </div>
          <div class="form-group">
            <label>الوصف:</label>
            <input type="text" id="entry-description" placeholder="وصف القيد">
          </div>
          <div class="form-group">
            <label>المرجع:</label>
            <input type="text" id="entry-reference" placeholder="رقم المرجع">
          </div>
          <div id="entry-lines">
            <div class="entry-line">
              <select class="account-select">
                <option value="">اختر الحساب</option>
              </select>
              <input type="text" class="description-input" placeholder="الوصف">
              <input type="number" class="debit-input" placeholder="مدين" step="0.01">
              <input type="number" class="credit-input" placeholder="دائن" step="0.01">
              <button type="button" onclick="removeEntryLine(this)" class="btn-danger">حذف</button>
            </div>
          </div>
          <button onclick="addEntryLine()" class="btn-secondary">إضافة سطر</button>
          <div class="entry-totals">
            <span>إجمالي مدين: <span id="total-debit">0.00</span></span>
            <span>إجمالي دائن: <span id="total-credit">0.00</span></span>
          </div>
          <div class="modal-actions">
            <button onclick="saveJournalEntry()" class="btn-primary">حفظ القيد</button>
            <button onclick="closeJournalModal()" class="btn-secondary">إلغاء</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  <script>
    // التنقل بين الموديولات
    const navItems = document.querySelectorAll('.nav-item');
    const modules = document.querySelectorAll('.module');
    const pageTitle = document.getElementById('page-title');
    
    navItems.forEach(item => {
      item.onclick = () => {
        navItems.forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        modules.forEach(m => m.classList.remove('active'));
        document.getElementById(item.dataset.module).classList.add('active');
        
        const linkText = item.querySelector('.nav-link span').textContent;
        pageTitle.textContent = linkText;
        
        // تحديث الداشبورد إذا كان مفتوحاً
        if (item.dataset.module === 'dashboard') {
          updateDashboard();
        }
      };
    });

    // متغيرات الرسوم البيانية
    let performanceChart, assetsChart, cashFlowChart;

    // إعدادات الشركة
    let companySettings = {
      name: '',
      address: '',
      phone: '',
      email: '',
      currency: 'LYD',
      language: 'ar',
      logo: ''
    };

    // تحميل الإعدادات عند بدء التطبيق
    window.onload = function() {
      loadSettings();
      setupLogoUpload();
    };

    // إعداد رفع الشعار
    function setupLogoUpload() {
      const logoInput = document.getElementById('logo-input');
      const logoPreview = document.getElementById('logo-preview');
      
      logoInput.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(e) {
            logoPreview.innerHTML = `<img src="${e.target.result}" alt="شعار الشركة">`;
            companySettings.logo = e.target.result;
          };
          reader.readAsDataURL(file);
        }
      };
      
      logoPreview.onclick = () => logoInput.click();
    }

    // حفظ الإعدادات
    function saveSettings() {
      companySettings.name = document.getElementById('company-name-input')?.value || '';
      companySettings.address = document.getElementById('company-address').value;
      companySettings.phone = document.getElementById('company-phone').value;
      companySettings.email = document.getElementById('company-email').value;
      companySettings.currency = document.getElementById('currency').value;
      companySettings.language = document.getElementById('language').value;
      
      localStorage.setItem('glassERP_settings', JSON.stringify(companySettings));
      alert('تم حفظ الإعدادات بنجاح!');
    }

    // تحميل الإعدادات
    function loadSettings() {
      const saved = localStorage.getItem('glassERP_settings');
      if (saved) {
        companySettings = JSON.parse(saved);
        
        document.getElementById('company-name-input').value = companySettings.name;
        document.getElementById('company-address').value = companySettings.address;
        document.getElementById('company-phone').value = companySettings.phone;
        document.getElementById('company-email').value = companySettings.email;
        document.getElementById('currency').value = companySettings.currency;
        document.getElementById('language').value = companySettings.language;
        
        if (companySettings.logo) {
          document.getElementById('logo-preview').innerHTML = 
            `<img src="${companySettings.logo}" alt="شعار الشركة">`;
        }
      }
    }

    // إعادة تعيين الإعدادات
    function resetSettings() {
      if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        localStorage.removeItem('glassERP_settings');
        location.reload();
      }
    }

    // ===== موديول الحسابات =====
    
    // البيانات الأساسية
    let accounts = [];
    let journalEntries = [];
    let nextAccountId = 1;
    let nextEntryId = 1;

    // تهيئة الحسابات الافتراضية
    function initializeDefaultAccounts() {
      if (accounts.length === 0) {
        const defaultAccounts = [
          // الأصول
          { id: 1, number: '1000', name: 'الأصول المتداولة', type: 'asset', parentId: null, balance: 0, level: 0 },
          { id: 2, number: '1100', name: 'النقد وما في حكمه', type: 'asset', parentId: 1, balance: 0, level: 1 },
          { id: 3, number: '1110', name: 'الصندوق', type: 'asset', parentId: 2, balance: 50000, level: 2 },
          { id: 4, number: '1120', name: 'البنك', type: 'asset', parentId: 2, balance: 150000, level: 2 },
          { id: 5, number: '1200', name: 'الذمم المدينة', type: 'asset', parentId: 1, balance: 0, level: 1 },
          { id: 6, number: '1210', name: 'عملاء', type: 'asset', parentId: 5, balance: 75000, level: 2 },
          { id: 7, number: '1300', name: 'المخزون', type: 'asset', parentId: 1, balance: 0, level: 1 },
          { id: 8, number: '1310', name: 'بضاعة', type: 'asset', parentId: 7, balance: 120000, level: 2 },
          
          // الخصوم
          { id: 9, number: '2000', name: 'الخصوم المتداولة', type: 'liability', parentId: null, balance: 0, level: 0 },
          { id: 10, number: '2100', name: 'الذمم الدائنة', type: 'liability', parentId: 9, balance: 0, level: 1 },
          { id: 11, number: '2110', name: 'موردين', type: 'liability', parentId: 10, balance: 0, level: 2 },
          
          // حقوق الملكية
          { id: 12, number: '3000', name: 'حقوق الملكية', type: 'equity', parentId: null, balance: 0, level: 0 },
          { id: 13, number: '3100', name: 'رأس المال', type: 'equity', parentId: 12, balance: 0, level: 1 },
          { id: 14, number: '3200', name: 'الأرباح المحتجزة', type: 'equity', parentId: 12, balance: 0, level: 1 },
          
          // الإيرادات
          { id: 15, number: '4000', name: 'الإيرادات', type: 'revenue', parentId: null, balance: 0, level: 0 },
          { id: 16, number: '4100', name: 'إيرادات المبيعات', type: 'revenue', parentId: 15, balance: 450000, level: 1 },
          
          // المصروفات
          { id: 17, number: '5000', name: 'المصروفات', type: 'expense', parentId: null, balance: 0, level: 0 },
          { id: 18, number: '5100', name: 'مصروفات تشغيلية', type: 'expense', parentId: 17, balance: 0, level: 1 },
          { id: 19, number: '5110', name: 'مصروفات إدارية', type: 'expense', parentId: 18, balance: 85000, level: 2 },
          { id: 20, number: '5120', name: 'مصروفات بيعية', type: 'expense', parentId: 18, balance: 65000, level: 2 }
        ];
        
        accounts = defaultAccounts;
        nextAccountId = 21;
        saveAccounts();
      }
    }

    // حفظ الحسابات
    function saveAccounts() {
      localStorage.setItem('glassERP_accounts', JSON.stringify(accounts));
      localStorage.setItem('glassERP_nextAccountId', nextAccountId.toString());
    }

    // تحميل الحسابات
    function loadAccounts() {
      const saved = localStorage.getItem('glassERP_accounts');
      const savedId = localStorage.getItem('glassERP_nextAccountId');
      
      if (saved) {
        accounts = JSON.parse(saved);
        nextAccountId = parseInt(savedId) || 1;
      } else {
        initializeDefaultAccounts();
      }
    }

    // حفظ القيود اليومية
    function saveJournalEntries() {
      localStorage.setItem('glassERP_journalEntries', JSON.stringify(journalEntries));
      localStorage.setItem('glassERP_nextEntryId', nextEntryId.toString());
    }

    // تحميل القيود اليومية
    function loadJournalEntries() {
      const saved = localStorage.getItem('glassERP_journalEntries');
      const savedId = localStorage.getItem('glassERP_nextEntryId');
      
      if (saved) {
        journalEntries = JSON.parse(saved);
        nextEntryId = parseInt(savedId) || 1;
      } else {
        // إضافة قيود تجريبية
        const sampleEntries = [
          {
            id: 1,
            date: '2024-01-15',
            description: 'بيع نقدي',
            reference: 'INV-001',
            lines: [
              { accountId: 3, description: 'صندوق', debit: 5000, credit: 0 },
              { accountId: 16, description: 'إيرادات المبيعات', debit: 0, credit: 5000 }
            ],
            createdAt: '2024-01-15T10:00:00.000Z'
          },
          {
            id: 2,
            date: '2024-01-16',
            description: 'شراء بضاعة',
            reference: 'PO-001',
            lines: [
              { accountId: 8, description: 'بضاعة', debit: 3000, credit: 0 },
              { accountId: 3, description: 'صندوق', debit: 0, credit: 3000 }
            ],
            createdAt: '2024-01-16T14:30:00.000Z'
          }
        ];
        
        journalEntries = sampleEntries;
        nextEntryId = 3;
        saveJournalEntries();
        
        // ترحيل القيود التجريبية للحسابات
        sampleEntries.forEach(entry => {
          postEntryToAccounts(entry);
        });
        saveAccounts();
      }
    }

    // إضافة حساب جديد
    function addAccount() {
      document.getElementById('account-modal').style.display = 'block';
      populateParentAccounts();
    }

    // إغلاق modal الحساب
    function closeAccountModal() {
      document.getElementById('account-modal').style.display = 'none';
      clearAccountForm();
    }

    // مسح نموذج الحساب
    function clearAccountForm() {
      document.getElementById('account-number').value = '';
      document.getElementById('account-name').value = '';
      document.getElementById('account-type').value = 'asset';
      document.getElementById('parent-account').value = '';
      document.getElementById('opening-balance').value = '';
    }

    // ملء قائمة الحسابات الأب
    function populateParentAccounts() {
      const select = document.getElementById('parent-account');
      select.innerHTML = '<option value="">بدون حساب أب</option>';
      
      accounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.number} - ${account.name}`;
        select.appendChild(option);
      });
    }

    // حفظ الحساب
    function saveAccount() {
      const number = document.getElementById('account-number').value;
      const name = document.getElementById('account-name').value;
      const type = document.getElementById('account-type').value;
      const parentId = document.getElementById('parent-account').value;
      const openingBalance = parseFloat(document.getElementById('opening-balance').value) || 0;

      if (!number || !name) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      // التحقق من عدم تكرار رقم الحساب
      if (accounts.find(a => a.number === number)) {
        alert('رقم الحساب موجود مسبقاً');
        return;
      }

      const parent = parentId ? accounts.find(a => a.id == parentId) : null;
      const level = parent ? parent.level + 1 : 0;

      const account = {
        id: nextAccountId++,
        number,
        name,
        type,
        parentId: parentId ? parseInt(parentId) : null,
        balance: openingBalance,
        level
      };

      accounts.push(account);
      saveAccounts();
      closeAccountModal();
      showAccountTree();
      updateDashboard(); // تحديث الداشبورد
      alert('تم حفظ الحساب بنجاح');
    }

    // عرض شجرة الحسابات
    function showAccountTree() {
      const container = document.getElementById('accounts-tree');
      container.innerHTML = '';

      const rootAccounts = accounts.filter(a => a.parentId === null);
      
      rootAccounts.forEach(account => {
        const accountDiv = createAccountElement(account);
        container.appendChild(accountDiv);
        addChildAccounts(account.id, container, 1);
      });
    }

    // إنشاء عنصر الحساب
    function createAccountElement(account) {
      const div = document.createElement('div');
      div.className = `account-item ${account.level > 0 ? 'sub-account' : ''} ${account.level > 1 ? 'sub-sub-account' : ''}`;
      div.innerHTML = `
        <strong>${account.number}</strong> - ${account.name}
        <span style="float: left; color: #666;">${formatBalance(account.balance)}</span>
      `;
      return div;
    }

    // إضافة الحسابات الفرعية
    function addChildAccounts(parentId, container, level) {
      const children = accounts.filter(a => a.parentId === parentId);
      children.forEach(child => {
        const childDiv = createAccountElement(child);
        container.appendChild(childDiv);
        addChildAccounts(child.id, container, level + 1);
      });
    }

    // إضافة قيد يومية جديد
    function addJournalEntry() {
      document.getElementById('journal-modal').style.display = 'block';
      document.getElementById('entry-date').value = new Date().toISOString().split('T')[0];
      populateAccountSelects();
      setupEntryLineEvents();
    }

    // إغلاق modal القيد
    function closeJournalModal() {
      document.getElementById('journal-modal').style.display = 'none';
      clearJournalForm();
    }

    // مسح نموذج القيد
    function clearJournalForm() {
      document.getElementById('entry-description').value = '';
      document.getElementById('entry-reference').value = '';
      document.getElementById('entry-lines').innerHTML = `
        <div class="entry-line">
          <select class="account-select">
            <option value="">اختر الحساب</option>
          </select>
          <input type="text" class="description-input" placeholder="الوصف">
          <input type="number" class="debit-input" placeholder="مدين" step="0.01">
          <input type="number" class="credit-input" placeholder="دائن" step="0.01">
          <button type="button" onclick="removeEntryLine(this)" class="btn-danger">حذف</button>
        </div>
      `;
      populateAccountSelects();
      setupEntryLineEvents();
      updateTotals();
    }

    // ملء قوائم الحسابات في القيد
    function populateAccountSelects() {
      const selects = document.querySelectorAll('.account-select');
      selects.forEach(select => {
        select.innerHTML = '<option value="">اختر الحساب</option>';
        accounts.forEach(account => {
          const option = document.createElement('option');
          option.value = account.id;
          option.textContent = `${account.number} - ${account.name}`;
          select.appendChild(option);
        });
      });
    }

    // إعداد أحداث أسطر القيد
    function setupEntryLineEvents() {
      const debitInputs = document.querySelectorAll('.debit-input');
      const creditInputs = document.querySelectorAll('.credit-input');
      
      debitInputs.forEach(input => {
        input.oninput = updateTotals;
      });
      
      creditInputs.forEach(input => {
        input.oninput = updateTotals;
      });
    }

    // إضافة سطر جديد للقيد
    function addEntryLine() {
      const container = document.getElementById('entry-lines');
      const newLine = document.createElement('div');
      newLine.className = 'entry-line';
      newLine.innerHTML = `
        <select class="account-select">
          <option value="">اختر الحساب</option>
        </select>
        <input type="text" class="description-input" placeholder="الوصف">
        <input type="number" class="debit-input" placeholder="مدين" step="0.01">
        <input type="number" class="credit-input" placeholder="دائن" step="0.01">
        <button type="button" onclick="removeEntryLine(this)" class="btn-danger">حذف</button>
      `;
      container.appendChild(newLine);
      populateAccountSelects();
      setupEntryLineEvents();
    }

    // حذف سطر من القيد
    function removeEntryLine(button) {
      const lines = document.querySelectorAll('.entry-line');
      if (lines.length > 1) {
        button.parentElement.remove();
        updateTotals();
      }
    }

    // تحديث المجاميع
    function updateTotals() {
      let totalDebit = 0;
      let totalCredit = 0;
      
      const debitInputs = document.querySelectorAll('.debit-input');
      const creditInputs = document.querySelectorAll('.credit-input');
      
      debitInputs.forEach(input => {
        totalDebit += parseFloat(input.value) || 0;
      });
      
      creditInputs.forEach(input => {
        totalCredit += parseFloat(input.value) || 0;
      });
      
      document.getElementById('total-debit').textContent = formatBalance(totalDebit);
      document.getElementById('total-credit').textContent = formatBalance(totalCredit);
    }

    // حفظ القيد اليومية
    function saveJournalEntry() {
      const date = document.getElementById('entry-date').value;
      const description = document.getElementById('entry-description').value;
      const reference = document.getElementById('entry-reference').value;
      
      if (!date || !description) {
        alert('يرجى ملء التاريخ والوصف');
        return;
      }

      const lines = [];
      const entryLines = document.querySelectorAll('.entry-line');
      
      entryLines.forEach(line => {
        const accountId = line.querySelector('.account-select').value;
        const lineDescription = line.querySelector('.description-input').value;
        const debit = parseFloat(line.querySelector('.debit-input').value) || 0;
        const credit = parseFloat(line.querySelector('.credit-input').value) || 0;
        
        if (accountId && (debit > 0 || credit > 0)) {
          lines.push({
            accountId: parseInt(accountId),
            description: lineDescription,
            debit,
            credit
          });
        }
      });

      if (lines.length < 2) {
        alert('يجب أن يحتوي القيد على سطرين على الأقل');
        return;
      }

      // التحقق من توازن القيد
      const totalDebit = lines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredit = lines.reduce((sum, line) => sum + line.credit, 0);
      
      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        alert('القيد غير متوازن. يجب أن يكون إجمالي المدين = إجمالي الدائن');
        return;
      }

      const entry = {
        id: nextEntryId++,
        date,
        description,
        reference,
        lines,
        createdAt: new Date().toISOString()
      };

      journalEntries.push(entry);
      saveJournalEntries();
      
      // ترحيل القيد للحسابات
      postEntryToAccounts(entry);
      
      closeJournalModal();
      showJournalEntries();
      updateDashboard(); // تحديث الداشبورد
      alert('تم حفظ القيد بنجاح');
    }

    // ترحيل القيد للحسابات
    function postEntryToAccounts(entry) {
      entry.lines.forEach(line => {
        const account = accounts.find(a => a.id === line.accountId);
        if (account) {
          if (account.type === 'asset' || account.type === 'expense') {
            account.balance += line.debit - line.credit;
          } else {
            account.balance += line.credit - line.debit;
          }
        }
      });
      saveAccounts();
    }

    // عرض القيود اليومية
    function showJournalEntries() {
      const container = document.getElementById('journal-entries');
      container.innerHTML = '';

      if (journalEntries.length === 0) {
        container.innerHTML = '<p>لا توجد قيود يومية</p>';
        return;
      }

      const table = document.createElement('table');
      table.innerHTML = `
        <thead>
          <tr>
            <th>التاريخ</th>
            <th>الوصف</th>
            <th>المرجع</th>
            <th>المبلغ</th>
            <th>الحساب</th>
          </tr>
        </thead>
        <tbody></tbody>
      `;

      const tbody = table.querySelector('tbody');
      
      journalEntries.forEach(entry => {
        entry.lines.forEach(line => {
          const account = accounts.find(a => a.id === line.accountId);
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${formatDate(entry.date)}</td>
            <td>${line.description || entry.description}</td>
            <td>${entry.reference}</td>
            <td>${line.debit > 0 ? formatBalance(line.debit) : formatBalance(line.credit)}</td>
            <td>${account ? `${account.number} - ${account.name}` : ''}</td>
          `;
          tbody.appendChild(row);
        });
      });

      container.appendChild(table);
    }

    // توليد الميزانية العمومية
    function generateBalanceSheet() {
      const container = document.getElementById('financial-reports');
      container.innerHTML = '';

      const assets = accounts.filter(a => a.type === 'asset' && a.balance !== 0);
      const liabilities = accounts.filter(a => a.type === 'liability' && a.balance !== 0);
      const equity = accounts.filter(a => a.type === 'equity' && a.balance !== 0);

      const totalAssets = assets.reduce((sum, a) => sum + a.balance, 0);
      const totalLiabilities = liabilities.reduce((sum, a) => sum + a.balance, 0);
      const totalEquity = equity.reduce((sum, a) => sum + a.balance, 0);

      container.innerHTML = `
        <h4>الميزانية العمومية</h4>
        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
        
        <h5>الأصول</h5>
        <table>
          <thead>
            <tr><th>الحساب</th><th>الرصيد</th></tr>
          </thead>
          <tbody>
            ${assets.map(a => `<tr><td>${a.number} - ${a.name}</td><td>${formatBalance(a.balance)}</td></tr>`).join('')}
            <tr style="font-weight: bold; background: #e9ecef;">
              <td>إجمالي الأصول</td><td>${formatBalance(totalAssets)}</td>
            </tr>
          </tbody>
        </table>

        <h5>الخصوم</h5>
        <table>
          <thead>
            <tr><th>الحساب</th><th>الرصيد</th></tr>
          </thead>
          <tbody>
            ${liabilities.map(a => `<tr><td>${a.number} - ${a.name}</td><td>${formatBalance(a.balance)}</td></tr>`).join('')}
            <tr style="font-weight: bold; background: #e9ecef;">
              <td>إجمالي الخصوم</td><td>${formatBalance(totalLiabilities)}</td>
            </tr>
          </tbody>
        </table>

        <h5>حقوق الملكية</h5>
        <table>
          <thead>
            <tr><th>الحساب</th><th>الرصيد</th></tr>
          </thead>
          <tbody>
            ${equity.map(a => `<tr><td>${a.number} - ${a.name}</td><td>${formatBalance(a.balance)}</td></tr>`).join('')}
            <tr style="font-weight: bold; background: #e9ecef;">
              <td>إجمالي حقوق الملكية</td><td>${formatBalance(totalEquity)}</td>
            </tr>
          </tbody>
        </table>
      `;
    }

    // توليد قائمة الدخل
    function generateIncomeStatement() {
      const container = document.getElementById('financial-reports');
      container.innerHTML = '';

      const revenues = accounts.filter(a => a.type === 'revenue' && a.balance !== 0);
      const expenses = accounts.filter(a => a.type === 'expense' && a.balance !== 0);

      const totalRevenue = revenues.reduce((sum, a) => sum + a.balance, 0);
      const totalExpenses = expenses.reduce((sum, a) => sum + a.balance, 0);
      const netIncome = totalRevenue - totalExpenses;

      container.innerHTML = `
        <h4>قائمة الدخل</h4>
        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
        
        <h5>الإيرادات</h5>
        <table>
          <thead>
            <tr><th>الحساب</th><th>المبلغ</th></tr>
          </thead>
          <tbody>
            ${revenues.map(a => `<tr><td>${a.number} - ${a.name}</td><td>${formatBalance(a.balance)}</td></tr>`).join('')}
            <tr style="font-weight: bold; background: #e9ecef;">
              <td>إجمالي الإيرادات</td><td>${formatBalance(totalRevenue)}</td>
            </tr>
          </tbody>
        </table>

        <h5>المصروفات</h5>
        <table>
          <thead>
            <tr><th>الحساب</th><th>المبلغ</th></tr>
          </thead>
          <tbody>
            ${expenses.map(a => `<tr><td>${a.number} - ${a.name}</td><td>${formatBalance(a.balance)}</td></tr>`).join('')}
            <tr style="font-weight: bold; background: #e9ecef;">
              <td>إجمالي المصروفات</td><td>${formatBalance(totalExpenses)}</td>
            </tr>
          </tbody>
        </table>

        <h5>صافي الدخل</h5>
        <table>
          <tbody>
            <tr style="font-weight: bold; background: ${netIncome >= 0 ? '#d4edda' : '#f8d7da'};">
              <td>صافي الدخل</td><td>${formatBalance(netIncome)}</td>
            </tr>
          </tbody>
        </table>
      `;
    }

    // توليد ميزان المراجعة
    function generateTrialBalance() {
      const container = document.getElementById('financial-reports');
      container.innerHTML = '';

      const activeAccounts = accounts.filter(a => a.balance !== 0);

      container.innerHTML = `
        <h4>ميزان المراجعة</h4>
        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
        
        <table>
          <thead>
            <tr>
              <th>رقم الحساب</th>
              <th>اسم الحساب</th>
              <th>مدين</th>
              <th>دائن</th>
            </tr>
          </thead>
          <tbody>
            ${activeAccounts.map(a => {
              const debit = (a.type === 'asset' || a.type === 'expense') && a.balance > 0 ? a.balance : 0;
              const credit = (a.type === 'liability' || a.type === 'equity' || a.type === 'revenue') && a.balance > 0 ? a.balance : 0;
              return `<tr>
                <td>${a.number}</td>
                <td>${a.name}</td>
                <td>${formatBalance(debit)}</td>
                <td>${formatBalance(credit)}</td>
              </tr>`;
            }).join('')}
          </tbody>
        </table>
      `;
    }

    // دوال مساعدة
    function formatBalance(amount) {
      return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    }

    function formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('ar-SA');
    }

    // إغلاق modals عند النقر خارجها
    window.onclick = function(event) {
      const accountModal = document.getElementById('account-modal');
      const journalModal = document.getElementById('journal-modal');
      
      if (event.target === accountModal) {
        closeAccountModal();
      }
      if (event.target === journalModal) {
        closeJournalModal();
      }
    }

    // إغلاق modals عند النقر على X
    document.querySelectorAll('.close').forEach(closeBtn => {
      closeBtn.onclick = function() {
        this.closest('.modal').style.display = 'none';
      }
    });

    // ===== وظائف الداشبورد =====
    
    // تحديث الداشبورد
    function updateDashboard() {
      updateDashboardStats();
      initializeCharts();
    }

    // تحديث إحصائيات الداشبورد
    function updateDashboardStats() {
      const assets = accounts.filter(a => a.type === 'asset');
      const revenues = accounts.filter(a => a.type === 'revenue');
      const expenses = accounts.filter(a => a.type === 'expense');

      const totalAssets = assets.reduce((sum, a) => sum + a.balance, 0);
      const totalRevenue = revenues.reduce((sum, a) => sum + a.balance, 0);
      const totalExpenses = expenses.reduce((sum, a) => sum + a.balance, 0);
      const netIncome = totalRevenue - totalExpenses;

      document.getElementById('total-assets').textContent = formatBalance(totalAssets);
      document.getElementById('total-revenue').textContent = formatBalance(totalRevenue);
      document.getElementById('total-expenses').textContent = formatBalance(totalExpenses);
      document.getElementById('net-income').textContent = formatBalance(netIncome);
      document.getElementById('journal-count').textContent = journalEntries.length;
      document.getElementById('accounts-count').textContent = accounts.length;
    }

    // تهيئة الرسوم البيانية
    function initializeCharts() {
      createPerformanceChart();
      createAssetsChart();
      createCashFlowChart();
    }

    // رسم بياني للأداء المالي
    function createPerformanceChart() {
      const ctx = document.getElementById('performanceChart').getContext('2d');
      
      if (performanceChart) {
        performanceChart.destroy();
      }

      const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
      const revenueData = [12000, 15000, 18000, 14000, 22000, 25000];
      const expenseData = [8000, 10000, 12000, 9000, 15000, 18000];

      performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: months,
          datasets: [
            {
              label: 'الإيرادات',
              data: revenueData,
              borderColor: '#10b981',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              borderWidth: 3,
              fill: true,
              tension: 0.4
            },
            {
              label: 'المصروفات',
              data: expenseData,
              borderColor: '#ef4444',
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              borderWidth: 3,
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                font: {
                  family: 'Cairo',
                  size: 12
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                font: {
                  family: 'Cairo'
                }
              }
            },
            x: {
              ticks: {
                font: {
                  family: 'Cairo'
                }
              }
            }
          }
        }
      });
    }

    // رسم بياني لتوزيع الأصول
    function createAssetsChart() {
      const ctx = document.getElementById('assetsChart').getContext('2d');
      
      if (assetsChart) {
        assetsChart.destroy();
      }

      const assetAccounts = accounts.filter(a => a.type === 'asset' && a.balance > 0);
      const labels = assetAccounts.map(a => a.name);
      const data = assetAccounts.map(a => a.balance);
      const colors = [
        '#667eea', '#764ba2', '#f093fb', '#f5576c',
        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
      ];

      assetsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors.slice(0, data.length),
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  family: 'Cairo',
                  size: 11
                },
                padding: 15
              }
            }
          }
        }
      });
    }

    // رسم بياني للتدفق النقدي
    function createCashFlowChart() {
      const ctx = document.getElementById('cashFlowChart').getContext('2d');
      
      if (cashFlowChart) {
        cashFlowChart.destroy();
      }

      const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
      const cashIn = [15000, 18000, 22000, 16000, 25000, 28000];
      const cashOut = [12000, 14000, 18000, 13000, 20000, 22000];

      cashFlowChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: months,
          datasets: [
            {
              label: 'التدفق الداخل',
              data: cashIn,
              backgroundColor: 'rgba(16, 185, 129, 0.8)',
              borderColor: '#10b981',
              borderWidth: 1
            },
            {
              label: 'التدفق الخارج',
              data: cashOut,
              backgroundColor: 'rgba(239, 68, 68, 0.8)',
              borderColor: '#ef4444',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                font: {
                  family: 'Cairo',
                  size: 12
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                font: {
                  family: 'Cairo'
                }
              }
            },
            x: {
              ticks: {
                font: {
                  family: 'Cairo'
                }
              }
            }
          }
        }
      });
    }

    // تحديث الرسم البياني حسب الفترة
    function updateChart(period) {
      // إزالة الفئة النشطة من جميع الأزرار
      document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      
      // إضافة الفئة النشطة للزر المحدد
      event.target.classList.add('active');
      
      // هنا يمكن إضافة منطق لتحديث البيانات حسب الفترة المحددة
      console.log('تحديث الرسم البياني للفترة:', period);
    }

    // تحميل البيانات عند بدء التطبيق
    window.addEventListener('load', function() {
      loadAccounts();
      loadJournalEntries();
      showAccountTree();
      updateDashboard();
    });
  </script>
</body>
</html> 