/**
 * نظام الإشعارات - Glass ERP System
 * إدارة الإشعارات وعرضها في الواجهة
 */

// متغيرات عامة
let notifications = [];
let unreadCount = 0;

/**
 * تهيئة نظام الإشعارات
 */
async function initializeNotifications() {
    console.log('🔔 تهيئة نظام الإشعارات...');
    
    // تحميل الإشعارات المحفوظة
    await loadNotifications();
    
    // إنشاء واجهة الإشعارات
    createNotificationsInterface();
    
    // ربط الأحداث
    bindNotificationEvents();
    
    // تحديث عدد الإشعارات غير المقروءة
    updateNotificationBadge();
    
    console.log('✅ تم تهيئة نظام الإشعارات بنجاح');
}

/**
 * تحميل الإشعارات من SQLite
 */
async function loadNotifications() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            const savedNotifications = await sqliteDB.getAllNotifications();
            if (savedNotifications && savedNotifications.length > 0) {
                notifications = savedNotifications;
                console.log('📋 تم تحميل الإشعارات:', notifications.length);
            } else {
                // إنشاء إشعارات تجريبية
                await createSampleNotifications();
            }
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة، إنشاء إشعارات تجريبية');
            await createSampleNotifications();
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الإشعارات:', error);
        notifications = [];
        await createSampleNotifications();
    }
}

/**
 * حفظ الإشعارات في SQLite
 */
async function saveNotifications() {
    try {
        if (typeof sqliteDB !== 'undefined' && sqliteDB) {
            await sqliteDB.saveAllNotifications(notifications);
            console.log('💾 تم حفظ الإشعارات');
        } else {
            console.warn('⚠️ قاعدة بيانات SQLite غير متوفرة');
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ الإشعارات:', error);
    }
}

/**
 * إنشاء إشعارات تجريبية
 */
async function createSampleNotifications() {
    const today = new Date();
    const sampleNotifications = [
        {
            id: 'notif_001',
            title: 'تم حفظ فاتورة جديدة',
            message: 'تم إنشاء فاتورة رقم FV-2024-001 بقيمة 15,000 دينار',
            type: 'success',
            timestamp: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 10, 45).toISOString(),
            isRead: false,
            module: 'sales'
        },
        {
            id: 'notif_002',
            title: 'تم إضافة قيد محاسبي',
            message: 'تم إضافة قيد يومي جديد برقم JE006',
            type: 'info',
            timestamp: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 9, 30).toISOString(),
            isRead: false,
            module: 'accounts'
        },
        {
            id: 'notif_003',
            title: 'تحديث إعدادات الشركة',
            message: 'تم تحديث معلومات الشركة بنجاح',
            type: 'warning',
            timestamp: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 15).toISOString(),
            isRead: true,
            module: 'settings'
        },
        {
            id: 'notif_004',
            title: 'تم حفظ قيد محاسبي',
            message: 'تم إضافة قيد يومي جديد برقم JE007',
            type: 'success',
            timestamp: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 11, 20).toISOString(),
            isRead: false,
            module: 'accounts'
        }
    ];
    
    notifications = sampleNotifications;
    await saveNotifications();
    console.log('📝 تم إنشاء إشعارات تجريبية');
}

/**
 * إنشاء واجهة الإشعارات
 */
function createNotificationsInterface() {
    // البحث عن زر الإشعارات
    const notificationBtn = document.querySelector('.notification-btn');
    if (!notificationBtn) {
        console.error('❌ لم يتم العثور على زر الإشعارات');
        return;
    }
    
    // إنشاء قائمة الإشعارات
    const notificationsDropdown = document.createElement('div');
    notificationsDropdown.className = 'notifications-dropdown';
    notificationsDropdown.id = 'notifications-dropdown';
    
    // إضافة القائمة للزر بدلاً من body
    notificationBtn.style.position = 'relative';
    notificationBtn.appendChild(notificationsDropdown);
    notificationsDropdown.innerHTML = `
        <div class="notifications-header">
            <h3><i class="fas fa-bell"></i> الإشعارات</h3>
            <button class="mark-all-read" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i> تحديد الكل كمقروء
            </button>
        </div>
        <div class="notifications-list" id="notifications-list">
            <!-- سيتم تحميل الإشعارات هنا -->
        </div>
        <div class="notifications-footer">
            <button class="view-all-notifications" onclick="viewAllNotifications()">
                عرض جميع الإشعارات
            </button>
        </div>
    `;
    
    // تحديث محتوى الإشعارات
    updateNotificationsList();
}

/**
 * ربط أحداث الإشعارات
 */
function bindNotificationEvents() {
    const notificationBtn = document.querySelector('.notification-btn');
    const notificationsDropdown = document.getElementById('notifications-dropdown');
    
    if (notificationBtn && notificationsDropdown) {
        // فتح/إغلاق قائمة الإشعارات
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleNotificationsDropdown();
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!notificationsDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                closeNotificationsDropdown();
            }
        });
        
        // منع إغلاق القائمة عند النقر داخلها
        notificationsDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}

/**
 * فتح/إغلاق قائمة الإشعارات
 */
function toggleNotificationsDropdown() {
    const dropdown = document.getElementById('notifications-dropdown');
    if (dropdown) {
        if (dropdown.classList.contains('active')) {
            closeNotificationsDropdown();
        } else {
            openNotificationsDropdown();
        }
    }
}

/**
 * فتح قائمة الإشعارات
 */
function openNotificationsDropdown() {
    const dropdown = document.getElementById('notifications-dropdown');
    if (dropdown) {
        dropdown.classList.add('active');
        updateNotificationsList();
        console.log('🔔 تم فتح قائمة الإشعارات');
    }
}

/**
 * إغلاق قائمة الإشعارات
 */
function closeNotificationsDropdown() {
    const dropdown = document.getElementById('notifications-dropdown');
    if (dropdown) {
        dropdown.classList.remove('active');
        console.log('🔕 تم إغلاق قائمة الإشعارات');
    }
}

/**
 * تحديث قائمة الإشعارات
 */
function updateNotificationsList() {
    const notificationsList = document.getElementById('notifications-list');
    if (!notificationsList) return;
    
    // الحصول على إشعارات اليوم فقط
    const todayNotifications = getTodayNotifications();
    
    if (todayNotifications.length === 0) {
        notificationsList.innerHTML = `
            <div class="no-notifications">
                <i class="fas fa-bell-slash"></i>
                <p>لا توجد إشعارات جديدة اليوم</p>
            </div>
        `;
    } else {
        notificationsList.innerHTML = todayNotifications.map(notification => `
            <div class="notification-item ${notification.isRead ? 'read' : 'unread'}" 
                 data-id="${notification.id}" onclick="markNotificationAsRead('${notification.id}')">
                <div class="notification-icon">
                    <i class="fas ${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${formatNotificationTime(notification.timestamp)}</div>
                </div>
                <div class="notification-actions">
                    <button class="delete-notification" onclick="deleteNotification('${notification.id}', event)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
}

/**
 * الحصول على إشعارات اليوم فقط
 */
function getTodayNotifications() {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    return notifications.filter(notification => {
        const notificationDate = new Date(notification.timestamp);
        return notificationDate >= todayStart && notificationDate <= todayEnd;
    }).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

/**
 * تحديث شارة الإشعارات
 */
function updateNotificationBadge() {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        const todayNotifications = getTodayNotifications();
        const unreadCount = todayNotifications.filter(n => !n.isRead).length;
        
        if (unreadCount > 0) {
            badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * إضافة إشعار جديد
 */
async function addNotification(title, message, type = 'info', module = 'general') {
    const notification = {
        id: 'notif_' + Date.now(),
        title: title,
        message: message,
        type: type,
        timestamp: new Date().toISOString(),
        isRead: false,
        module: module
    };
    
    notifications.unshift(notification);
    
    // حفظ الإشعارات
    await saveNotifications();
    
    // تحديث الواجهة
    updateNotificationsList();
    updateNotificationBadge();
    
    console.log('🔔 تم إضافة إشعار جديد:', title);
}

/**
 * تحديد إشعار كمقروء
 */
async function markNotificationAsRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.isRead = true;
        await saveNotifications();
        updateNotificationBadge();
        updateNotificationsList();
    }
}

/**
 * تحديد جميع الإشعارات كمقروءة
 */
async function markAllAsRead() {
    notifications.forEach(notification => {
        notification.isRead = true;
    });
    
    await saveNotifications();
    updateNotificationBadge();
    updateNotificationsList();
    
    console.log('✅ تم تحديد جميع الإشعارات كمقروءة');
}

/**
 * حذف إشعار
 */
async function deleteNotification(notificationId, event) {
    event.stopPropagation();
    
    if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        notifications = notifications.filter(n => n.id !== notificationId);
        await saveNotifications();
        updateNotificationsList();
        updateNotificationBadge();
        
        console.log('🗑️ تم حذف الإشعار');
    }
}

/**
 * عرض جميع الإشعارات
 */
function viewAllNotifications() {
    // يمكن إضافة نافذة منفصلة لعرض جميع الإشعارات
    alert('سيتم إضافة صفحة عرض جميع الإشعارات قريباً');
}

/**
 * الحصول على أيقونة الإشعار حسب النوع
 */
function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || 'fa-bell';
}

/**
 * تنسيق وقت الإشعار
 */
function formatNotificationTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    
    if (diffMins < 1) {
        return 'الآن';
    } else if (diffMins < 60) {
        return `منذ ${diffMins} دقيقة`;
    } else if (diffHours < 24) {
        return `منذ ${diffHours} ساعة`;
    } else {
        return date.toLocaleTimeString('ar-SA', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: true 
        });
    }
}

/**
 * مسح الإشعارات القديمة
 */
async function clearOldNotifications() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const oldNotifications = notifications.filter(n => {
        const notificationDate = new Date(n.timestamp);
        return notificationDate < thirtyDaysAgo;
    });
    
    if (oldNotifications.length > 0) {
        notifications = notifications.filter(n => {
            const notificationDate = new Date(n.timestamp);
            return notificationDate >= thirtyDaysAgo;
        });
        
        await saveNotifications();
        updateNotificationsList();
        updateNotificationBadge();
        
        console.log(`🗑️ تم مسح ${oldNotifications.length} إشعار قديم`);
    }
}

// تصدير الدوال للاستخدام في الملفات الأخرى
window.NotificationsModule = {
    initializeNotifications,
    addNotification,
    markNotificationAsRead,
    markAllAsRead,
    deleteNotification,
    clearOldNotifications
};

// تهيئة الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeNotifications();
    }, 100);
}); 