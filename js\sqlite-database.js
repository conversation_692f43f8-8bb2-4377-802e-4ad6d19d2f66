/**
 * نظام إدارة قاعدة بيانات SQLite للبرنامج
 * جميع العمليات تتم عبر SQLite فقط
 */

class SQLiteDatabase {
    constructor() {
        this.db = null;
        this.dbName = 'glass_erp.db';
        this.isInitialized = false;
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async initialize() {
        try {
            console.log('🚀 بدء تهيئة قاعدة بيانات SQLite...');
            
            // إنشاء قاعدة البيانات
            this.db = await this.createDatabase();
            
            // إنشاء الجداول
            await this.createTables();
            
            // إدراج البيانات الأولية
            await this.insertInitialData();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة بيانات SQLite بنجاح');
            return true;
        } catch (error) {
            console.error('❌ فشل في تهيئة قاعدة بيانات SQLite:', error);
            return false;
        }
    }

    /**
     * إنشاء قاعدة البيانات
     */
    async createDatabase() {
        if (!window.initSqlJs) {
            throw new Error('مكتبة sql.js غير محملة! يرجى إضافة <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>');
        }
        
        const SQL = await window.initSqlJs({ 
            locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}` 
        });
        
        return new SQL.Database();
    }

    /**
     * إنشاء الجداول
     */
    async createTables() {
        console.log('📊 إنشاء جداول قاعدة البيانات...');
        
        // جدول إعدادات الشركة
        this.db.run(`CREATE TABLE IF NOT EXISTS company_settings (
            id TEXT PRIMARY KEY,
            name TEXT,
            address TEXT,
            phone TEXT,
            email TEXT,
            website TEXT,
            taxNumber TEXT,
            currency TEXT,
            language TEXT,
            timezone TEXT,
            logo TEXT,
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول الحسابات
        this.db.run(`CREATE TABLE IF NOT EXISTS accounts (
            id TEXT PRIMARY KEY,
            code TEXT UNIQUE,
            name TEXT,
            type TEXT,
            parentId TEXT,
            balance REAL DEFAULT 0,
            description TEXT,
            isActive INTEGER DEFAULT 1,
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول القيود اليومية
        this.db.run(`CREATE TABLE IF NOT EXISTS journal_entries (
            id TEXT PRIMARY KEY,
            number TEXT,
            date TEXT,
            description TEXT,
            reference TEXT,
            totalDebit REAL DEFAULT 0,
            totalCredit REAL DEFAULT 0,
            status TEXT DEFAULT 'posted',
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول تفاصيل القيود اليومية
        this.db.run(`CREATE TABLE IF NOT EXISTS journal_entry_lines (
            id TEXT PRIMARY KEY,
            journalEntryId TEXT,
            accountId TEXT,
            description TEXT,
            debit REAL DEFAULT 0,
            credit REAL DEFAULT 0,
            FOREIGN KEY (journalEntryId) REFERENCES journal_entries (id),
            FOREIGN KEY (accountId) REFERENCES accounts (id)
        );`);

        // جدول العملاء
        this.db.run(`CREATE TABLE IF NOT EXISTS customers (
            id TEXT PRIMARY KEY,
            name TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            taxNumber TEXT,
            creditLimit REAL DEFAULT 0,
            balance REAL DEFAULT 0,
            isActive INTEGER DEFAULT 1,
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول الخدمات
        this.db.run(`CREATE TABLE IF NOT EXISTS services (
            id TEXT PRIMARY KEY,
            name TEXT,
            description TEXT,
            category TEXT,
            price REAL DEFAULT 0,
            cost REAL DEFAULT 0,
            isActive INTEGER DEFAULT 1,
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول الفواتير
        this.db.run(`CREATE TABLE IF NOT EXISTS invoices (
            id TEXT PRIMARY KEY,
            number TEXT UNIQUE,
            date TEXT,
            customerId TEXT,
            subtotal REAL DEFAULT 0,
            taxAmount REAL DEFAULT 0,
            total REAL DEFAULT 0,
            status TEXT DEFAULT 'draft',
            dueDate TEXT,
            createdAt TEXT,
            updatedAt TEXT,
            FOREIGN KEY (customerId) REFERENCES customers (id)
        );`);

        // جدول تفاصيل الفواتير
        this.db.run(`CREATE TABLE IF NOT EXISTS invoice_items (
            id TEXT PRIMARY KEY,
            invoiceId TEXT,
            serviceId TEXT,
            description TEXT,
            quantity REAL DEFAULT 1,
            price REAL DEFAULT 0,
            total REAL DEFAULT 0,
            FOREIGN KEY (invoiceId) REFERENCES invoices (id),
            FOREIGN KEY (serviceId) REFERENCES services (id)
        );`);

        // جدول الإشعارات
        this.db.run(`CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY,
            type TEXT,
            title TEXT,
            message TEXT,
            module TEXT,
            read INTEGER DEFAULT 0,
            date TEXT,
            createdAt TEXT
        );`);

        // جدول سجل النشاطات
        this.db.run(`CREATE TABLE IF NOT EXISTS activity_logs (
            id TEXT PRIMARY KEY,
            action TEXT,
            module TEXT,
            description TEXT,
            userId TEXT,
            date TEXT,
            createdAt TEXT
        );`);

        // جدول المستخدمين
        this.db.run(`CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE,
            email TEXT UNIQUE,
            password TEXT,
            role TEXT,
            isActive INTEGER DEFAULT 1,
            lastLogin TEXT,
            createdAt TEXT,
            updatedAt TEXT
        );`);

        // جدول الأذونات
        this.db.run(`CREATE TABLE IF NOT EXISTS permissions (
            id TEXT PRIMARY KEY,
            userId TEXT,
            module TEXT,
            canRead INTEGER DEFAULT 0,
            canWrite INTEGER DEFAULT 0,
            canDelete INTEGER DEFAULT 0,
            FOREIGN KEY (userId) REFERENCES users (id)
        );`);

        console.log('✅ تم إنشاء جميع الجداول بنجاح');
    }

    /**
     * إدراج البيانات الأولية
     */
    async insertInitialData() {
        try {
            // إعدادات الشركة الافتراضية
            const defaultCompanySettings = {
                id: 'main',
                name: 'شركة زجاج النظام',
                address: 'العنوان الافتراضي',
                phone: '+966-50-000-0000',
                email: '<EMAIL>',
                taxNumber: '*********',
                currency: 'SAR',
                language: 'ar',
                timezone: 'Asia/Riyadh',
                logo: '',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await this.addOrUpdateCompanySettings(defaultCompanySettings);

            // الحسابات الأساسية
            const defaultAccounts = [
                {
                    id: '1000',
                    code: '1000',
                    name: 'الصندوق',
                    type: 'asset',
                    parentId: null,
                    balance: 0,
                    description: 'النقد في الصندوق',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '1100',
                    code: '1100',
                    name: 'البنك',
                    type: 'asset',
                    parentId: null,
                    balance: 0,
                    description: 'الحساب البنكي',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '2000',
                    code: '2000',
                    name: 'الموردين',
                    type: 'liability',
                    parentId: null,
                    balance: 0,
                    description: 'حسابات الموردين',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '3000',
                    code: '3000',
                    name: 'رأس المال',
                    type: 'equity',
                    parentId: null,
                    balance: 0,
                    description: 'رأس مال الشركة',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '4000',
                    code: '4000',
                    name: 'إيرادات المبيعات',
                    type: 'revenue',
                    parentId: null,
                    balance: 0,
                    description: 'إيرادات المبيعات',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '5000',
                    code: '5000',
                    name: 'تكلفة البضاعة المباعة',
                    type: 'expense',
                    parentId: null,
                    balance: 0,
                    description: 'تكلفة البضاعة المباعة',
                    isActive: 1,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ];

            for (const account of defaultAccounts) {
                await this.addOrUpdateAccount(account);
            }

            console.log('✅ تم إدراج البيانات الأولية بنجاح');
        } catch (error) {
            console.error('❌ فشل في إدراج البيانات الأولية:', error);
        }
    }

    // === وظائف إعدادات الشركة ===

    async addOrUpdateCompanySettings(settings) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO company_settings 
            (id, name, address, phone, email, website, taxNumber, currency, language, timezone, logo, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            settings.id,
            settings.name,
            settings.address,
            settings.phone,
            settings.email,
            settings.website,
            settings.taxNumber,
            settings.currency,
            settings.language,
            settings.timezone,
            settings.logo,
            settings.createdAt,
            settings.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getCompanySettings() {
        const result = this.db.exec('SELECT * FROM company_settings WHERE id = "main" LIMIT 1');
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const settings = {};
            columns.forEach((col, i) => {
                settings[col] = values[i];
            });
            return settings;
        }
        return null;
    }

    async deleteCompanySettings() {
        this.db.run('DELETE FROM company_settings WHERE id = "main"');
        return true;
    }

    // === وظائف الحسابات ===

    async addOrUpdateAccount(account) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO accounts 
            (id, code, name, type, parentId, balance, description, isActive, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            account.id,
            account.code,
            account.name,
            account.type,
            account.parentId,
            account.balance || 0,
            account.description,
            account.isActive ? 1 : 0,
            account.createdAt,
            account.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getAccount(id) {
        const result = this.db.exec('SELECT * FROM accounts WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const account = {};
            columns.forEach((col, i) => {
                account[col] = values[i];
            });
            return account;
        }
        return null;
    }

    async getAllAccounts() {
        const result = this.db.exec('SELECT * FROM accounts ORDER BY code');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const account = {};
                columns.forEach((col, i) => {
                    account[col] = row[i];
                });
                return account;
            });
        }
        return [];
    }

    async deleteAccount(id) {
        this.db.run('DELETE FROM accounts WHERE id = ?', [id]);
        return true;
    }

    async getAccountsByType(type) {
        const result = this.db.exec('SELECT * FROM accounts WHERE type = ? ORDER BY code', [type]);
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const account = {};
                columns.forEach((col, i) => {
                    account[col] = row[i];
                });
                return account;
            });
        }
        return [];
    }

    async getAccountsByParent(parentId) {
        const result = this.db.exec('SELECT * FROM accounts WHERE parentId = ? ORDER BY code', [parentId]);
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const account = {};
                columns.forEach((col, i) => {
                    account[col] = row[i];
                });
                return account;
            });
        }
        return [];
    }

    // === وظائف القيود اليومية ===

    async addOrUpdateJournalEntry(entry) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO journal_entries 
            (id, number, date, description, reference, totalDebit, totalCredit, status, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            entry.id,
            entry.number,
            entry.date,
            entry.description,
            entry.reference,
            entry.totalDebit || 0,
            entry.totalCredit || 0,
            entry.status || 'posted',
            entry.createdAt,
            entry.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getJournalEntry(id) {
        const result = this.db.exec('SELECT * FROM journal_entries WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const entry = {};
            columns.forEach((col, i) => {
                entry[col] = values[i];
            });
            return entry;
        }
        return null;
    }

    async getAllJournalEntries() {
        const result = this.db.exec('SELECT * FROM journal_entries ORDER BY date DESC, number DESC');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const entry = {};
                columns.forEach((col, i) => {
                    entry[col] = row[i];
                });
                return entry;
            });
        }
        return [];
    }

    async deleteJournalEntry(id) {
        // حذف تفاصيل القيد أولاً
        this.db.run('DELETE FROM journal_entry_lines WHERE journalEntryId = ?', [id]);
        
        // حذف القيد الرئيسي
        this.db.run('DELETE FROM journal_entries WHERE id = ?', [id]);
        
        return true;
    }

    async getJournalEntriesByDateRange(startDate, endDate) {
        const result = this.db.exec(
            'SELECT * FROM journal_entries WHERE date BETWEEN ? AND ? ORDER BY date DESC, number DESC',
            [startDate, endDate]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const entry = {};
                columns.forEach((col, i) => {
                    entry[col] = row[i];
                });
                return entry;
            });
        }
        return [];
    }

    // === وظائف تفاصيل القيود اليومية ===

    async addOrUpdateJournalEntryLine(line) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO journal_entry_lines 
            (id, journalEntryId, accountId, description, debit, credit)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            line.id,
            line.journalEntryId,
            line.accountId,
            line.description,
            line.debit || 0,
            line.credit || 0
        ]);
        
        stmt.free();
        return true;
    }

    async getJournalEntryLines(journalEntryId) {
        const result = this.db.exec(
            'SELECT * FROM journal_entry_lines WHERE journalEntryId = ? ORDER BY id',
            [journalEntryId]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const line = {};
                columns.forEach((col, i) => {
                    line[col] = row[i];
                });
                return line;
            });
        }
        return [];
    }

    async deleteJournalEntryLine(id) {
        this.db.run('DELETE FROM journal_entry_lines WHERE id = ?', [id]);
        return true;
    }

    // === وظائف العملاء ===

    async addOrUpdateCustomer(customer) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO customers 
            (id, name, phone, email, address, taxNumber, creditLimit, balance, isActive, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            customer.id,
            customer.name,
            customer.phone,
            customer.email,
            customer.address,
            customer.taxNumber,
            customer.creditLimit || 0,
            customer.balance || 0,
            customer.isActive ? 1 : 0,
            customer.createdAt,
            customer.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getCustomer(id) {
        const result = this.db.exec('SELECT * FROM customers WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const customer = {};
            columns.forEach((col, i) => {
                customer[col] = values[i];
            });
            return customer;
        }
        return null;
    }

    async getAllCustomers() {
        const result = this.db.exec('SELECT * FROM customers ORDER BY name');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const customer = {};
                columns.forEach((col, i) => {
                    customer[col] = row[i];
                });
                return customer;
            });
        }
        return [];
    }

    async deleteCustomer(id) {
        this.db.run('DELETE FROM customers WHERE id = ?', [id]);
        return true;
    }

    // === وظائف الخدمات ===

    async addOrUpdateService(service) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO services 
            (id, name, description, category, price, cost, isActive, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            service.id,
            service.name,
            service.description,
            service.category,
            service.price || 0,
            service.cost || 0,
            service.isActive ? 1 : 0,
            service.createdAt,
            service.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getService(id) {
        const result = this.db.exec('SELECT * FROM services WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const service = {};
            columns.forEach((col, i) => {
                service[col] = values[i];
            });
            return service;
        }
        return null;
    }

    async getAllServices() {
        const result = this.db.exec('SELECT * FROM services ORDER BY name');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const service = {};
                columns.forEach((col, i) => {
                    service[col] = row[i];
                });
                return service;
            });
        }
        return [];
    }

    async deleteService(id) {
        this.db.run('DELETE FROM services WHERE id = ?', [id]);
        return true;
    }

    // === وظائف الفواتير ===

    async addOrUpdateInvoice(invoice) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO invoices 
            (id, number, date, customerId, subtotal, taxAmount, total, status, dueDate, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            invoice.id,
            invoice.number,
            invoice.date,
            invoice.customerId,
            invoice.subtotal || 0,
            invoice.taxAmount || 0,
            invoice.total || 0,
            invoice.status || 'draft',
            invoice.dueDate,
            invoice.createdAt,
            invoice.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getInvoice(id) {
        const result = this.db.exec('SELECT * FROM invoices WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const invoice = {};
            columns.forEach((col, i) => {
                invoice[col] = values[i];
            });
            return invoice;
        }
        return null;
    }

    async getAllInvoices() {
        const result = this.db.exec('SELECT * FROM invoices ORDER BY date DESC, number DESC');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const invoice = {};
                columns.forEach((col, i) => {
                    invoice[col] = row[i];
                });
                return invoice;
            });
        }
        return [];
    }

    async deleteInvoice(id) {
        // حذف تفاصيل الفاتورة أولاً
        this.db.run('DELETE FROM invoice_items WHERE invoiceId = ?', [id]);
        
        // حذف الفاتورة
        this.db.run('DELETE FROM invoices WHERE id = ?', [id]);
        
        return true;
    }

    // === وظائف تفاصيل الفواتير ===

    async addOrUpdateInvoiceItem(item) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO invoice_items 
            (id, invoiceId, serviceId, description, quantity, price, total)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            item.id,
            item.invoiceId,
            item.serviceId,
            item.description,
            item.quantity || 1,
            item.price || 0,
            item.total || 0
        ]);
        
        stmt.free();
        return true;
    }

    async getInvoiceItems(invoiceId) {
        const result = this.db.exec(
            'SELECT * FROM invoice_items WHERE invoiceId = ? ORDER BY id',
            [invoiceId]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const item = {};
                columns.forEach((col, i) => {
                    item[col] = row[i];
                });
                return item;
            });
        }
        return [];
    }

    async deleteInvoiceItem(id) {
        this.db.run('DELETE FROM invoice_items WHERE id = ?', [id]);
        return true;
    }

    // === وظائف الإشعارات ===

    async addOrUpdateNotification(notification) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO notifications 
            (id, type, title, message, module, read, date, createdAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            notification.id,
            notification.type,
            notification.title,
            notification.message,
            notification.module,
            notification.read ? 1 : 0,
            notification.date,
            notification.createdAt
        ]);
        
        stmt.free();
        return true;
    }

    async getNotification(id) {
        const result = this.db.exec('SELECT * FROM notifications WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const notification = {};
            columns.forEach((col, i) => {
                notification[col] = values[i];
            });
            return notification;
        }
        return null;
    }

    async getAllNotifications() {
        const result = this.db.exec('SELECT * FROM notifications ORDER BY date DESC');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const notification = {};
                columns.forEach((col, i) => {
                    notification[col] = row[i];
                });
                return notification;
            });
        }
        return [];
    }

    async getUnreadNotifications() {
        const result = this.db.exec('SELECT * FROM notifications WHERE read = 0 ORDER BY date DESC');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const notification = {};
                columns.forEach((col, i) => {
                    notification[col] = row[i];
                });
                return notification;
            });
        }
        return [];
    }

    async markNotificationAsRead(id) {
        this.db.run('UPDATE notifications SET read = 1 WHERE id = ?', [id]);
        return true;
    }

    async deleteNotification(id) {
        this.db.run('DELETE FROM notifications WHERE id = ?', [id]);
        return true;
    }

    // === وظائف سجل النشاطات ===

    async addActivityLog(log) {
        const stmt = this.db.prepare(`
            INSERT INTO activity_logs 
            (id, action, module, description, userId, date, createdAt)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            log.id,
            log.action,
            log.module,
            log.description,
            log.userId,
            log.date,
            log.createdAt
        ]);
        
        stmt.free();
        return true;
    }

    async getAllActivityLogs() {
        const result = this.db.exec('SELECT * FROM activity_logs ORDER BY date DESC');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const log = {};
                columns.forEach((col, i) => {
                    log[col] = row[i];
                });
                return log;
            });
        }
        return [];
    }

    async getActivityLogsByModule(module) {
        const result = this.db.exec(
            'SELECT * FROM activity_logs WHERE module = ? ORDER BY date DESC',
            [module]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const log = {};
                columns.forEach((col, i) => {
                    log[col] = row[i];
                });
                return log;
            });
        }
        return [];
    }

    async getActivityLogsByUser(userId) {
        const result = this.db.exec(
            'SELECT * FROM activity_logs WHERE userId = ? ORDER BY date DESC',
            [userId]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const log = {};
                columns.forEach((col, i) => {
                    log[col] = row[i];
                });
                return log;
            });
        }
        return [];
    }

    // === وظائف المستخدمين ===

    async addOrUpdateUser(user) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO users 
            (id, username, email, password, role, isActive, lastLogin, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            user.id,
            user.username,
            user.email,
            user.password,
            user.role,
            user.isActive ? 1 : 0,
            user.lastLogin,
            user.createdAt,
            user.updatedAt
        ]);
        
        stmt.free();
        return true;
    }

    async getUser(id) {
        const result = this.db.exec('SELECT * FROM users WHERE id = ? LIMIT 1', [id]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const user = {};
            columns.forEach((col, i) => {
                user[col] = values[i];
            });
            return user;
        }
        return null;
    }

    async getUserByUsername(username) {
        const result = this.db.exec('SELECT * FROM users WHERE username = ? LIMIT 1', [username]);
        if (result.length > 0 && result[0].values.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values[0];
            const user = {};
            columns.forEach((col, i) => {
                user[col] = values[i];
            });
            return user;
        }
        return null;
    }

    async getAllUsers() {
        const result = this.db.exec('SELECT * FROM users ORDER BY username');
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const user = {};
                columns.forEach((col, i) => {
                    user[col] = row[i];
                });
                return user;
            });
        }
        return [];
    }

    async deleteUser(id) {
        this.db.run('DELETE FROM users WHERE id = ?', [id]);
        return true;
    }

    // === وظائف الأذونات ===

    async addOrUpdatePermission(permission) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO permissions 
            (id, userId, module, canRead, canWrite, canDelete)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run([
            permission.id,
            permission.userId,
            permission.module,
            permission.canRead ? 1 : 0,
            permission.canWrite ? 1 : 0,
            permission.canDelete ? 1 : 0
        ]);
        
        stmt.free();
        return true;
    }

    async getUserPermissions(userId) {
        const result = this.db.exec(
            'SELECT * FROM permissions WHERE userId = ?',
            [userId]
        );
        if (result.length > 0) {
            const columns = result[0].columns;
            const values = result[0].values;
            return values.map(row => {
                const permission = {};
                columns.forEach((col, i) => {
                    permission[col] = row[i];
                });
                return permission;
            });
        }
        return [];
    }

    async deletePermission(id) {
        this.db.run('DELETE FROM permissions WHERE id = ?', [id]);
        return true;
    }

    // === وظائف عامة ===

    /**
     * مسح جميع البيانات
     */
    async clearAllData() {
        try {
            const tables = [
                'company_settings', 'accounts', 'journal_entries', 'journal_entry_lines',
                'customers', 'services', 'invoices', 'invoice_items',
                'notifications', 'activity_logs', 'users', 'permissions'
            ];

            for (const table of tables) {
                this.db.run(`DELETE FROM ${table}`);
            }

            console.log('✅ تم مسح جميع البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ فشل في مسح البيانات:', error);
            return false;
        }
    }

    /**
     * إنشاء نسخة احتياطية
     */
    async createBackup() {
        try {
            const backup = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                data: {}
            };

            // نسخ جميع الجداول
            const tables = [
                'company_settings', 'accounts', 'journal_entries', 'journal_entry_lines',
                'customers', 'services', 'invoices', 'invoice_items',
                'notifications', 'activity_logs', 'users', 'permissions'
            ];

            for (const table of tables) {
                const result = this.db.exec(`SELECT * FROM ${table}`);
                if (result.length > 0) {
                    const columns = result[0].columns;
                    const values = result[0].values;
                    backup.data[table] = values.map(row => {
                        const item = {};
                        columns.forEach((col, i) => {
                            item[col] = row[i];
                        });
                        return item;
                    });
                } else {
                    backup.data[table] = [];
                }
            }

            console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');
            return backup;
        } catch (error) {
            console.error('❌ فشل في إنشاء النسخة الاحتياطية:', error);
            return null;
        }
    }

    /**
     * استعادة نسخة احتياطية
     */
    async restoreBackup(backup) {
        try {
            // مسح البيانات الحالية
            await this.clearAllData();

            // استعادة البيانات
            for (const [tableName, data] of Object.entries(backup.data)) {
                for (const item of data) {
                    const columns = Object.keys(item);
                    const values = Object.values(item);
                    const placeholders = columns.map(() => '?').join(', ');
                    
                    this.db.run(
                        `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`,
                        values
                    );
                }
            }

            console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');
            return true;
        } catch (error) {
            console.error('❌ فشل في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    /**
     * تصدير قاعدة البيانات كملف
     */
    exportDatabase() {
        try {
            const data = this.db.export();
            return data;
        } catch (error) {
            console.error('❌ فشل في تصدير قاعدة البيانات:', error);
            return null;
        }
    }

    /**
     * استيراد قاعدة البيانات من ملف
     */
    importDatabase(data) {
        try {
            this.db = new SQL.Database(data);
            console.log('✅ تم استيراد قاعدة البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ فشل في استيراد قاعدة البيانات:', error);
            return false;
        }
    }

    /**
     * إغلاق قاعدة البيانات
     */
    /**
     * حفظ جميع الحسابات
     */
    async saveAllAccounts(accounts) {
        if (!Array.isArray(accounts)) return false;

        try {
            // حذف جميع الحسابات الموجودة
            this.db.run('DELETE FROM accounts');

            // إدراج الحسابات الجديدة
            const stmt = this.db.prepare('INSERT INTO accounts (id, code, name, type, parentId, balance, isActive) VALUES (?, ?, ?, ?, ?, ?, ?)');

            for (const account of accounts) {
                stmt.run([
                    account.id || account.code,
                    account.code,
                    account.name,
                    account.type,
                    account.parentId || null,
                    account.balance || 0,
                    account.isActive !== false ? 1 : 0
                ]);
            }

            stmt.free();
            console.log('✅ تم حفظ جميع الحسابات في SQLite:', accounts.length);
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ الحسابات:', error);
            return false;
        }
    }

    /**
     * حفظ جميع القيود اليومية
     */
    async saveAllJournalEntries(entries) {
        if (!Array.isArray(entries)) return false;

        try {
            // حذف جميع القيود الموجودة
            this.db.run('DELETE FROM journal_entry_lines');
            this.db.run('DELETE FROM journal_entries');

            // إدراج القيود الجديدة
            const entryStmt = this.db.prepare('INSERT INTO journal_entries (id, number, date, description, reference, totalDebit, totalCredit) VALUES (?, ?, ?, ?, ?, ?, ?)');
            const lineStmt = this.db.prepare('INSERT INTO journal_entry_lines (id, journalEntryId, accountId, accountName, debit, credit, description) VALUES (?, ?, ?, ?, ?, ?, ?)');

            for (const entry of entries) {
                // إدراج القيد الرئيسي
                entryStmt.run([
                    entry.id,
                    entry.number,
                    entry.date,
                    entry.description,
                    entry.reference || '',
                    entry.totalDebit || 0,
                    entry.totalCredit || 0
                ]);

                // إدراج تفاصيل القيد
                if (entry.lines && Array.isArray(entry.lines)) {
                    for (const line of entry.lines) {
                        lineStmt.run([
                            line.id || `${entry.id}_${Date.now()}_${Math.random()}`,
                            entry.id,
                            line.accountId,
                            line.accountName,
                            line.debit || 0,
                            line.credit || 0,
                            line.description || ''
                        ]);
                    }
                }
            }

            entryStmt.free();
            lineStmt.free();
            console.log('✅ تم حفظ جميع القيود اليومية في SQLite:', entries.length);
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ القيود اليومية:', error);
            return false;
        }
    }

    /**
     * حفظ جميع الإشعارات
     */
    async saveAllNotifications(notifications) {
        if (!Array.isArray(notifications)) return false;

        try {
            // حذف جميع الإشعارات الموجودة
            this.db.run('DELETE FROM notifications');

            // إدراج الإشعارات الجديدة
            const stmt = this.db.prepare('INSERT INTO notifications (id, title, message, type, timestamp, isRead, module) VALUES (?, ?, ?, ?, ?, ?, ?)');

            for (const notification of notifications) {
                stmt.run([
                    notification.id,
                    notification.title,
                    notification.message,
                    notification.type,
                    notification.timestamp,
                    notification.isRead ? 1 : 0,
                    notification.module || 'general'
                ]);
            }

            stmt.free();
            console.log('✅ تم حفظ جميع الإشعارات في SQLite:', notifications.length);
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ الإشعارات:', error);
            return false;
        }
    }

    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            this.isInitialized = false;
            console.log('🔒 تم إغلاق قاعدة البيانات');
        }
    }
}

// إنشاء مثيل عام لقاعدة البيانات
window.sqliteDB = new SQLiteDatabase();

// تصدير الكلاس للاستخدام في الملفات الأخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SQLiteDatabase;
} 