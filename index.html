<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glass ERP System - Development</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="stylesheet" href="css/accounts.css">
    <link rel="stylesheet" href="css/notifications.css">
    <link rel="stylesheet" href="css/performance.css">
</head>
<body>
    <!-- Sidebar - القائمة الجانبية -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-cube"></i> Glass ERP</h2>
            <div class="subtitle">نظام إدارة الموارد المتكامل</div>
        </div>
        <nav class="nav-menu">
            <div class="nav-item active" data-module="dashboard">
                <a href="#" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </div>
            <div class="nav-item" data-module="settings">
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>إعدادات الشركة</span>
                </a>
            </div>
            <div class="nav-item" data-module="sales">
                <a href="#" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </div>
            <div class="nav-item" data-module="purchases">
                <a href="#" class="nav-link">
                    <i class="fas fa-shopping-bag"></i>
                    <span>المشتريات</span>
                </a>
            </div>
            <div class="nav-item" data-module="payroll">
                <a href="#" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>الرواتب والأجور</span>
                </a>
            </div>
            <div class="nav-item" data-module="inventory">
                <a href="#" class="nav-link">
                    <i class="fas fa-warehouse"></i>
                    <span>المخازن</span>
                </a>
            </div>
            <div class="nav-item" data-module="payments">
                <a href="#" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    <span>المدفوعات</span>
                </a>
            </div>
            <div class="nav-item" data-module="receipts">
                <a href="#" class="nav-link">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المقبوضات</span>
                </a>
            </div>
            <div class="nav-item" data-module="accounts">
                <a href="#" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span>الحسابات</span>
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content - المحتوى الرئيسي -->
    <div class="main">
        <!-- Top Header - الشريط العلوي -->
        <div class="top-header">
            <div class="header-content">
                <!-- زر إخفاء/إظهار القائمة الجانبية -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <h1 class="page-title" id="page-title">لوحة التحكم</h1>
                
                <!-- معلومات الشركة والوقت -->
                <div class="company-info">
                    <div class="company-details">
                        <span class="company-name" id="company-name"></span>
                        <span class="current-time" id="current-time">--:--:--</span>
                    </div>
                </div>
                
                <div class="header-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="user-profile">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>المدير</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content Area - منطقة المحتوى -->
        <div class="content">
            <!-- Dashboard Module - موديول لوحة التحكم -->
            <div id="dashboard" class="module active">
                <!-- سيتم تحميل محتوى الداشبورد من dashboard.js -->
            </div>

            <!-- Settings Module - موديول الإعدادات -->
            <div id="settings" class="module">
                <!-- سيتم تحميل محتوى الإعدادات من settings.js -->
                <div class="settings-form">
                  <div class="form-group">
                    <label for="company-name-input">اسم الشركة</label>
                    <input type="text" id="company-name-input" placeholder="اسم الشركة" />
                  </div>
                  <div class="form-group">
                    <label for="company-address">عنوان الشركة</label>
                    <input type="text" id="company-address" placeholder="عنوان الشركة" />
                  </div>
                  <div class="form-group">
                    <label for="company-phone">رقم الهاتف</label>
                    <input type="text" id="company-phone" placeholder="رقم الهاتف" />
                  </div>
                  <div class="form-group">
                    <label for="company-email">البريد الإلكتروني</label>
                    <input type="email" id="company-email" placeholder="البريد الإلكتروني" />
                  </div>
                  <div class="form-group">
                    <label for="company-website">الموقع الإلكتروني</label>
                    <input type="url" id="company-website" placeholder="الموقع الإلكتروني" />
                  </div>
                  <div class="form-group">
                    <label for="company-tax">الرقم الضريبي</label>
                    <input type="text" id="company-tax" placeholder="الرقم الضريبي" />
                  </div>
                  <div class="form-group">
                    <label for="currency">العملة</label>
                    <select id="currency">
                      <option value="LYD">دينار ليبي (LYD)</option>
                      <option value="SAR">ريال سعودي (SAR)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                      <option value="GBP">جنيه إسترليني (GBP)</option>
                      <option value="AED">درهم إماراتي (AED)</option>
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="JOD">دينار أردني (JOD)</option>
                      <option value="KWD">دينار كويتي (KWD)</option>
                      <option value="QAR">ريال قطري (QAR)</option>
                      <option value="BHD">دينار بحريني (BHD)</option>
                    </select>
                  </div>
                </div>
            </div>

            <!-- Sales Module - موديول المبيعات -->
            <div id="sales" class="module">
                <h3>موديول المبيعات</h3>
                <p>إدارة عمليات البيع والفواتير.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Purchases Module - موديول المشتريات -->
            <div id="purchases" class="module">
                <h3>موديول المشتريات</h3>
                <p>إدارة عمليات الشراء والموردين.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Payroll Module - موديول الرواتب -->
            <div id="payroll" class="module">
                <h3>موديول الرواتب والأجور</h3>
                <p>إدارة الرواتب والموظفين.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Inventory Module - موديول المخازن -->
            <div id="inventory" class="module">
                <h3>موديول المخازن</h3>
                <p>إدارة المخزون وحركة الأصناف.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Payments Module - موديول المدفوعات -->
            <div id="payments" class="module">
                <h3>موديول المدفوعات</h3>
                <p>تسجيل المدفوعات المختلفة.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Receipts Module - موديول المقبوضات -->
            <div id="receipts" class="module">
                <h3>موديول المقبوضات</h3>
                <p>تسجيل المقبوضات المختلفة.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Accounts Module - موديول الحسابات -->
            <div id="accounts" class="module">
                <!-- سيتم تحميل محتوى الحسابات من accounts.js -->
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/accounts.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/test-performance.js"></script>
</body>
</html> 