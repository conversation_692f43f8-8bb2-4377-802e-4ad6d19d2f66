<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glass ERP System - Development</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="stylesheet" href="css/accounts.css">
    <link rel="stylesheet" href="css/notifications.css">
    <link rel="stylesheet" href="css/performance.css">
    <link rel="stylesheet" href="css/sales.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
</head>
<body>
    <!-- Sidebar - القائمة الجانبية -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-cube"></i> Glass ERP</h2>
            <div class="subtitle">نظام إدارة الموارد المتكامل</div>
        </div>
        <nav class="nav-menu">
            <div class="nav-item active" data-module="dashboard">
                <a href="#" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </div>
            <div class="nav-item" data-module="settings">
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>إعدادات الشركة</span>
                </a>
            </div>
            <div class="nav-item" data-module="sales">
                <a href="#" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </div>
            <div class="nav-item" data-module="purchases">
                <a href="#" class="nav-link">
                    <i class="fas fa-shopping-bag"></i>
                    <span>المشتريات</span>
                </a>
            </div>
            <div class="nav-item" data-module="payroll">
                <a href="#" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>الرواتب والأجور</span>
                </a>
            </div>
            <div class="nav-item" data-module="inventory">
                <a href="#" class="nav-link">
                    <i class="fas fa-warehouse"></i>
                    <span>المخازن</span>
                </a>
            </div>
            <div class="nav-item" data-module="payments">
                <a href="#" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    <span>المدفوعات</span>
                </a>
            </div>
            <div class="nav-item" data-module="receipts">
                <a href="#" class="nav-link">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>المقبوضات</span>
                </a>
            </div>
            <div class="nav-item" data-module="accounts">
                <a href="#" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span>الحسابات</span>
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content - المحتوى الرئيسي -->
    <div class="main">
        <!-- Top Header - الشريط العلوي -->
        <div class="top-header">
            <div class="header-content">
                <!-- زر إخفاء/إظهار القائمة الجانبية -->
                <!-- <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button> -->
                
                <h1 class="page-title" id="page-title">لوحة التحكم</h1>
                
                <!-- معلومات الشركة والوقت -->
                <div class="company-info">
                    <div class="company-details">
                        <span class="company-name" id="company-name"></span>
                    </div>
                    <div class="time-info">
                        <span class="current-time" id="current-time"></span>
                    </div>
                </div>
                
                <div class="header-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="user-profile">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>المدير</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content Area - منطقة المحتوى -->
        <div class="content">
            <!-- Dashboard Module - موديول لوحة التحكم -->
            <div id="dashboard" class="module active">
                <!-- سيتم تحميل محتوى الداشبورد من dashboard.js -->
            </div>

            <!-- Settings Module - موديول الإعدادات -->
            <div id="settings" class="module">
                <!-- سيتم تحميل محتوى الإعدادات من settings.js -->
                <div class="settings-form">
                  <div class="form-group">
                    <label for="company-name-input">اسم الشركة</label>
                    <input type="text" id="company-name-input" placeholder="اسم الشركة" />
                  </div>
                  <div class="form-group">
                    <label for="company-address">عنوان الشركة</label>
                    <input type="text" id="company-address" placeholder="عنوان الشركة" />
                  </div>
                  <div class="form-group">
                    <label for="company-phone">رقم الهاتف</label>
                    <input type="text" id="company-phone" placeholder="رقم الهاتف" />
                  </div>
                  <div class="form-group">
                    <label for="company-email">البريد الإلكتروني</label>
                    <input type="email" id="company-email" placeholder="البريد الإلكتروني" />
                  </div>
                  <div class="form-group">
                    <label for="company-website">الموقع الإلكتروني</label>
                    <input type="url" id="company-website" placeholder="الموقع الإلكتروني" />
                  </div>
                  <div class="form-group">
                    <label for="company-tax">الرقم الضريبي</label>
                    <input type="text" id="company-tax" placeholder="الرقم الضريبي" />
                  </div>
                  <div class="form-group">
                    <label for="currency">العملة</label>
                    <select id="currency">
                      <option value="LYD">دينار ليبي (LYD)</option>
                      <option value="SAR">ريال سعودي (SAR)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                      <option value="GBP">جنيه إسترليني (GBP)</option>
                      <option value="AED">درهم إماراتي (AED)</option>
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="JOD">دينار أردني (JOD)</option>
                      <option value="KWD">دينار كويتي (KWD)</option>
                      <option value="QAR">ريال قطري (QAR)</option>
                      <option value="BHD">دينار بحريني (BHD)</option>
                    </select>
                  </div>
                </div>
            </div>

            <!-- Sales Module - موديول المبيعات -->
            <div id="sales" class="module">
                <!-- سيتم تحميل محتوى المبيعات من sales.js -->
            </div>

            <!-- Customers Module - موديول العملاء -->
            <div id="customers" class="module">
                <!-- سيتم تحميل محتوى العملاء من customers.js -->
            </div>

            <!-- Services Module - موديول الخدمات -->
            <div id="services" class="module">
                <!-- سيتم تحميل محتوى الخدمات من services.js -->
            </div>

            <!-- Purchases Module - موديول المشتريات -->
            <div id="purchases" class="module">
                <h3>موديول المشتريات</h3>
                <p>إدارة عمليات الشراء والموردين.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Payroll Module - موديول الرواتب -->
            <div id="payroll" class="module">
                <h3>موديول الرواتب والأجور</h3>
                <p>إدارة الرواتب والموظفين.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Inventory Module - موديول المخازن -->
            <div id="inventory" class="module">
                <h3>موديول المخازن</h3>
                <p>إدارة المخزون وحركة الأصناف.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Payments Module - موديول المدفوعات -->
            <div id="payments" class="module">
                <h3>موديول المدفوعات</h3>
                <p>تسجيل المدفوعات المختلفة.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Receipts Module - موديول المقبوضات -->
            <div id="receipts" class="module">
                <h3>موديول المقبوضات</h3>
                <p>تسجيل المقبوضات المختلفة.</p>
                <p><em>قيد التطوير...</em></p>
            </div>

            <!-- Accounts Module - موديول الحسابات -->
            <div id="accounts" class="module">
                <!-- سيتم تحميل محتوى الحسابات من accounts.js -->
            </div>

            <!-- Sales Module Section (Modern UI) -->
            <div id="sales-module" class="sales-dashboard" style="display:none">
              <div class="sales-cards">
                <div class="sales-card">
                  <div class="card-title">إجمالي الفواتير</div>
                  <div class="card-value" id="sales-total-invoices">0</div>
                  <div class="card-desc">عدد الفواتير المسجلة</div>
                </div>
                <div class="sales-card">
                  <div class="card-title">إجمالي العملاء</div>
                  <div class="card-value" id="sales-total-customers">0</div>
                  <div class="card-desc">عدد العملاء النشطين</div>
                </div>
                <div class="sales-card">
                  <div class="card-title">إجمالي الخدمات</div>
                  <div class="card-value" id="sales-total-services">0</div>
                  <div class="card-desc">عدد الخدمات المتاحة</div>
                </div>
                <div class="sales-card">
                  <div class="card-title">إجمالي المقبوضات</div>
                  <div class="card-value" id="sales-total-receipts">0</div>
                  <div class="card-desc">إجمالي المقبوضات</div>
                </div>
              </div>
              <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:18px;">
                <h2 style="margin:0;font-size:1.3rem;color:#222;">جدول الفواتير</h2>
                <button class="sales-btn" onclick="openSalesInvoiceModal()"><i class="fas fa-plus"></i> إنشاء فاتورة جديدة</button>
              </div>
              <table class="sales-table">
                <thead>
                  <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>الإجمالي</th>
                    <th>الحالة</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody id="sales-invoice-table-body">
                  <!-- سيتم تعبئة الفواتير هنا -->
                </tbody>
              </table>
            </div>
            <!-- Sales Invoice Modal -->
            <div class="sales-modal" id="sales-invoice-modal">
              <div class="sales-modal-content">
                <button class="sales-modal-close" onclick="closeSalesInvoiceModal()">&times;</button>
                <h3 style="margin-bottom:18px;">إنشاء فاتورة مبيعات جديدة</h3>
                <form id="sales-invoice-form">
                  <div style="display:flex;gap:16px;">
                    <div style="flex:1;">
                      <label>التاريخ</label>
                      <input type="date" name="date" required style="width:100%;margin-bottom:10px;">
                    </div>
                    <div style="flex:1;">
                      <label>رقم الفاتورة</label>
                      <input type="text" name="invoiceNumber" readonly style="width:100%;margin-bottom:10px;background:#f1f5f9;">
                    </div>
                  </div>
                  <div style="margin-bottom:10px;">
                    <label>العميل</label>
                    <select name="customer" required style="width:100%;">
                      <option value="">اختر عميل...</option>
                    </select>
                  </div>
                  <div style="margin-bottom:10px;">
                    <label>بنود الفاتورة</label>
                    <table style="width:100%;background:#f9fafb;border-radius:8px;">
                      <thead>
                        <tr>
                          <th>الخدمة</th>
                          <th>سمك الزجاج</th>
                          <th>الطول (مم)</th>
                          <th>العرض (مم)</th>
                          <th>العدد</th>
                          <th>السعر/وحدة</th>
                          <th>الإجمالي</th>
                          <th></th>
                        </tr>
                      </thead>
                      <tbody id="invoice-items-body">
                        <!-- البنود هنا -->
                      </tbody>
                    </table>
                    <button type="button" class="sales-btn" style="margin-top:8px;" onclick="addInvoiceItemRow()"><i class="fas fa-plus"></i> إضافة بند</button>
                  </div>
                  <div style="display:flex;gap:16px;margin-bottom:10px;">
                    <div style="flex:1;">
                      <label>الخصم</label>
                      <input type="number" name="discount" min="0" value="0" style="width:100%;">
                    </div>
                    <div style="flex:1;">
                      <label>الضريبة (%)</label>
                      <input type="number" name="tax" min="0" value="0" style="width:100%;">
                    </div>
                  </div>
                  <div style="margin-bottom:10px;">
                    <label>الإجمالي النهائي</label>
                    <input type="text" name="total" readonly style="width:100%;background:#f1f5f9;">
                  </div>
                  <div style="text-align:left;">
                    <button type="submit" class="sales-btn">حفظ الفاتورة</button>
                  </div>
                </form>
              </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <!-- تحميل sqlite-database.js أولاً لأنه يحتوي على قاعدة البيانات المطلوبة -->
    <script src="js/sqlite-database.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/accounts.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/test-performance.js"></script>
    <script src="js/sqlite.js"></script>
    <script>
      // إظهار موديول المبيعات تلقائيًا عند تحميل الصفحة
      document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('sales-module').style.display = 'block';
      });
    </script>
</body>
</html> 